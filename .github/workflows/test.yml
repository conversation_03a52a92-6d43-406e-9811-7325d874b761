name: Build and Deploy Docker Image

on:
  push:
    branches:
      - main
      - 'feat/**'
      - 'fix/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

# 添加并发控制，取消之前队列中的 action
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: self-hosted
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24'

      - name: Run Tests
        run: |
          go test -v ./test/application/...

  build-and-deploy:
    needs: test
    runs-on: self-hosted
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        if: github.event_name == 'push'
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        if: github.event_name == 'push'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate tags
        if: github.event_name == 'push'
        id: tags
        run: |
          BRANCH_TAG=$(echo "${{ github.ref_name }}" | sed 's/\//-/g')
          SHORT_SHA=$(echo "${{ github.sha }}" | cut -c1-7)
          DEPLOY_TAG="${BRANCH_TAG}-${SHORT_SHA}"
          
          if [[ "${{ github.ref_name }}" == "main" ]]; then
            # main分支构建两个标签：具体标签和latest标签
            DOCKER_TAGS="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${DEPLOY_TAG},${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest"
          else
            # 其他分支只构建具体标签
            DOCKER_TAGS="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${DEPLOY_TAG}"
          fi
          
          echo "deploy_tag=$DEPLOY_TAG" >> $GITHUB_OUTPUT
          echo "docker_tags=$DOCKER_TAGS" >> $GITHUB_OUTPUT
          echo "Generated tags: $DOCKER_TAGS"
          echo "Deploy tag: $DEPLOY_TAG"

      - name: Build and push Docker image
        if: github.event_name == 'push'
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: ${{ steps.tags.outputs.docker_tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          provenance: false
          sbom: false

      - name: 重启 be-web-builder 容器
        if: success() && github.event_name == 'push'
        id: deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          port: ${{ secrets.SERVER_PORT }}
          script: |
            cd /root/web-builder
            echo "正在重启 be-web-builder 容器..."
            # 使用具体的部署标签拉取镜像
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.tags.outputs.deploy_tag }}
            # 修改 docker-compose.yml 中的镜像标签为具体标签
            sed -i "s|image: ghcr.io/web-builder-dev/be-web-builder:.*|image: ghcr.io/web-builder-dev/be-web-builder:${{ steps.tags.outputs.deploy_tag }}|" docker-compose.yml
            docker compose up -d --no-deps be-web-builder
            echo "be-web-builder 容器重启完成"
            docker compose ps be-web-builder

      - name: 构建和部署成功通知飞书
        if: success() && github.event_name == 'push'
        uses: foxundermoon/feishu-action@v2
        with:
          url: ${{ secrets.FEISHU_WEBHOOK_URL }}
          msg_type: post
          content: |
            post:
              zh_cn:
                title: ✅ (be-web-builder) Docker 镜像构建 & 部署成功
                content:
                - - tag: text
                    text: "📦 镜像地址（可 pull）:"
                  - tag: text
                    text: "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.tags.outputs.deploy_tag }}"
                - - tag: text
                    text: "🔁 构建平台: linux/amd64"
                - - tag: text
                    text: "🕒 构建时间: ${{ github.event.head_commit.timestamp }}"
                - - tag: text
                    text: "🚀 部署服务: be-web-builder"
                - - tag: text
                    text: "🔧 分支: ${{ github.ref_name }}"
                - - tag: text
                    text: "👤 提交者: ${{ github.actor }}"
                - - tag: text
                    text: "📝 提交信息: "
                  - tag: text
                    text: ${{ toJSON(github.event.head_commit.message) }}
                - - tag: text
                    text: "🔗 Commit: "
                  - tag: a
                    href: "${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}"
                    text: "点击查看 Commit"
                - - tag: text
                    text: "✅ 部署状态: 已成功部署到服务器"
                - - tag: a
                    text: "🌐 访问web-builder"
                    href: "https://code.webbuilder.site/"

      - name: 构建或部署失败通知飞书
        if: failure() || cancelled()
        continue-on-error: true
        uses: foxundermoon/feishu-action@v2
        with:
          url: ${{ secrets.FEISHU_WEBHOOK_URL }}
          msg_type: post
          content: |
            post:
              zh_cn:
                title: ❌ (be-web-builder) Docker 镜像构建或部署失败
                content:
                - - tag: text
                    text: "分支: ${{ github.ref_name }}"
                - - tag: text
                    text: "提交者: ${{ github.actor }}"
                - - tag: text
                    text: "提交信息: "
                  - tag: text
                    text: ${{ toJSON(github.event.head_commit.message) }}
                - - tag: text
                    text: "Commit: "
                  - tag: a
                    href: "${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}"
                    text: "点击查看 Commit"
                - - tag: a
                    text: "🔗 查看本次 Action 运行详情"
                    href: "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                - - tag: text
                    text: "❌ 部署状态: 构建或部署失败，请及时关注！"
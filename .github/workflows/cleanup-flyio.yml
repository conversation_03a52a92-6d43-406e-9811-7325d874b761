name: Cleanup Fly.io

on:
  schedule:
    # 每天 14:00 UTC 执行
    - cron: '0 14 * * *'
  workflow_dispatch: # 允许手动触发

jobs:
  cleanup:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'  

      - name: Run cleanup script
        run: go run scripts/cleanup_fly_apps/main.go
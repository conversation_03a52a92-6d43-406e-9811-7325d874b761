package main

import (
	"context"
	"fmt"
	"log"

	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

func main() {
	// 初始化日志
	logger.InitLogger("info", "production")

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 检查必要的配置
	if cfg.Cloudflare.APIKey == "" || cfg.Cloudflare.APIEmail == "" || cfg.Cloudflare.AccountID == "" {
		log.Fatal("Cloudflare API credentials are not configured")
	}

	if cfg.Cloudflare.KVNamespaceID == "" {
		log.Fatal("Cloudflare KV namespace ID is not configured")
	}

	// 创建 Cloudflare 基础设施服务
	cloudflareInfra, err := cloudflare.NewService(
		cfg.Cloudflare.APIKey,
		cfg.Cloudflare.APIEmail,
		cfg.Cloudflare.AccountID,
	)
	if err != nil {
		log.Fatalf("Failed to create Cloudflare infrastructure service: %v", err)
	}

	// 创建 Worker KV 应用服务
	kvService := cloudflare_service.NewWorkerKVService(cloudflareInfra, cfg)

	ctx := context.Background()

	// 示例1: 创建新的 URL 配置
	fmt.Println("=== 示例1: 创建新的 URL 配置 ===")
	previewURL := "https://preview.example.com"
	publishURL := "https://publish.example.com"

	err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", &previewURL, &publishURL)
	if err != nil {
		log.Printf("Failed to create URL config: %v", err)
	} else {
		fmt.Println("✓ 成功创建 URL 配置")
	}

	// 读取并显示配置
	config, err := kvService.ReadKeyValuePair(ctx, "website-config")
	if err != nil {
		log.Printf("Failed to read config: %v", err)
	} else {
		fmt.Printf("✓ 当前配置: %s\n", config)
	}

	// 示例2: 只更新预览 URL
	fmt.Println("\n=== 示例2: 只更新预览 URL ===")
	newPreviewURL := "https://new-preview.example.com"

	err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", &newPreviewURL, nil)
	if err != nil {
		log.Printf("Failed to update preview URL: %v", err)
	} else {
		fmt.Println("✓ 成功更新预览 URL")
	}

	// 读取并显示更新后的配置
	config, err = kvService.ReadKeyValuePair(ctx, "website-config")
	if err != nil {
		log.Printf("Failed to read updated config: %v", err)
	} else {
		fmt.Printf("✓ 更新后配置: %s\n", config)
	}

	// 示例3: 只更新发布 URL
	fmt.Println("\n=== 示例3: 只更新发布 URL ===")
	newPublishURL := "https://new-publish.example.com"

	err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", nil, &newPublishURL)
	if err != nil {
		log.Printf("Failed to update publish URL: %v", err)
	} else {
		fmt.Println("✓ 成功更新发布 URL")
	}

	// 读取并显示最终配置
	config, err = kvService.ReadKeyValuePair(ctx, "website-config")
	if err != nil {
		log.Printf("Failed to read final config: %v", err)
	} else {
		fmt.Printf("✓ 最终配置: %s\n", config)
	}

	// 示例4: 创建只有预览 URL 的配置
	fmt.Println("\n=== 示例4: 创建部分配置 ===")
	partialPreviewURL := "https://partial-preview.example.com"

	err = kvService.CreateOrUpdateURLConfig(ctx, "partial-config", &partialPreviewURL, nil)
	if err != nil {
		log.Printf("Failed to create partial config: %v", err)
	} else {
		fmt.Println("✓ 成功创建部分配置（只有预览 URL）")
	}

	// 读取部分配置
	partialConfig, err := kvService.ReadKeyValuePair(ctx, "partial-config")
	if err != nil {
		log.Printf("Failed to read partial config: %v", err)
	} else {
		fmt.Printf("✓ 部分配置: %s\n", partialConfig)
	}

	// 示例5: 后续添加发布 URL
	fmt.Println("\n=== 示例5: 补充发布 URL ===")
	partialPublishURL := "https://partial-publish.example.com"

	err = kvService.CreateOrUpdateURLConfig(ctx, "partial-config", nil, &partialPublishURL)
	if err != nil {
		log.Printf("Failed to add publish URL: %v", err)
	} else {
		fmt.Println("✓ 成功添加发布 URL")
	}

	// 读取完整配置
	completeConfig, err := kvService.ReadKeyValuePair(ctx, "partial-config")
	if err != nil {
		log.Printf("Failed to read complete config: %v", err)
	} else {
		fmt.Printf("✓ 完整配置: %s\n", completeConfig)
	}

	// 示例6: 批量管理多个项目配置
	fmt.Println("\n=== 示例6: 批量管理多个项目配置 ===")
	projects := []struct {
		name       string
		previewURL string
		publishURL string
	}{
		{"project-a", "https://preview-a.example.com", "https://publish-a.example.com"},
		{"project-b", "https://preview-b.example.com", "https://publish-b.example.com"},
		{"project-c", "https://preview-c.example.com", "https://publish-c.example.com"},
	}

	for _, project := range projects {
		err = kvService.CreateOrUpdateURLConfig(ctx, project.name, &project.previewURL, &project.publishURL)
		if err != nil {
			log.Printf("Failed to create config for %s: %v", project.name, err)
		} else {
			fmt.Printf("✓ 成功创建 %s 的配置\n", project.name)
		}
	}

	// 读取所有项目配置
	fmt.Println("\n所有项目配置:")
	for _, project := range projects {
		config, err := kvService.ReadKeyValuePair(ctx, project.name)
		if err != nil {
			log.Printf("Failed to read config for %s: %v", project.name, err)
		} else {
			fmt.Printf("✓ %s: %s\n", project.name, config)
		}
	}

	fmt.Println("\n=== 示例完成 ===")
	fmt.Println("注意: 这些示例需要有效的 Cloudflare 凭据和 KV namespace ID")
	fmt.Println("请确保在 conf/config_dev.yaml 中配置了正确的值，或设置相应的环境变量")
	fmt.Println("\nCreateOrUpdateURLConfig 方法的特点:")
	fmt.Println("- 自动处理创建和更新逻辑")
	fmt.Println("- 支持部分字段更新（传入 nil 表示不更新该字段）")
	fmt.Println("- 自动解析和合并现有配置")
	fmt.Println("- 提供详细的日志记录")
}

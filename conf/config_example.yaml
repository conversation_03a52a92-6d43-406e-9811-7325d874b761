log:
  level: "info"
github:
  owner: "your-github-owner"
  token: "your_github_token_if_not_using_env_var"
netlify:
  token: "your_netlify_access_token"
  hook_url: "your_default_netlify_webhook_url_here_if_not_using_env_var"
supabase:
  url: "YOUR_SUPABASE_URL"       # 例如 https://your-project-ref.supabase.co
  service_key: "YOUR_SUPABASE_SERVICE_KEY"
  edge_functions_token: "YOUR_EDGE_FUNCTIONS_TOKEN"  # Bearer token for Edge Functions
cloudflare:
  api_key: "YOUR_CLOUDFLARE_API_KEY"
  api_email: "YOUR_CLOUDFLARE_API_EMAIL"
  account_id: "YOUR_CLOUDFLARE_ACCOUNT_ID"
  webhook_auth_key: "YOUR_CLOUDFLARE_WEBHOOK_AUTH_KEY"
  webhook_id: "YOUR_CLOUDFLARE_WEBHOOK_ID"
  kv_namespace_id: "YOUR_CLOUDFLARE_KV_NAMESPACE_ID"
fly:
  api_token: "YOUR_FLY_API_TOKEN"
  org_slug: "YOUR_FLY_ORG_SLUG"
  wss_url: "wss://your-fly-wss-url/ws/agent"
be_screenshot_api_base_url: "http://localhost:9000"
log:
  level: "debug" # 可选值: debug, info, warn, error, dpanic, panic, fatal
github:
  token: "*********************************************************************************************"
  owner: "NextSpace-coder"
netlify:
  token: "****************************************"
  hook_url: "https://be-web-builder.webbuilder.site/api/netlify/hooks"
supabase:
  url: "https://riaisoioxkiniiwahncx.supabase.co"
  service_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uJtQwmuL7BCoDLWunbh--iTNKKh-yj698T_S-ScXoWQ"
  edge_functions_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F9CcPmVstnxVzA-4NfND7MhUW5OSGColITa99GR3XNw"
cloudflare:
  api_key: "618b6c8066fd8138910981c873c56018e6177"
  api_email: "<EMAIL>"
  account_id: "68642b46207b7df320c516af3dabb16a"
  webhook_auth_key: "XgKXLs2Pi3aFZG"
  webhook_id: "f96bc22a73074489a193b3b8e294e6c0" # 对应这个webhook：https://dash.cloudflare.com/68642b46207b7df320c516af3dabb16a/notifications/webhook/edit/f96bc22a73074489a193b3b8e294e6c0
  kv_namespace_id: "e7619ad26bd54e70811c56fa99547fae" # 请替换为实际的 KV namespace ID
fly:
  api_token: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************HM6Ly9hcGkuZmx5LmlvL2FhYS92MZgEks5oWjjWzwAAAAEkUlb0F84AEMx3CpHOABDMdwzEEE6JAqDrCwfeV/lWAdVU6+nEIHOp37GiPzGAznZKi6r+y4YIxLqtVCA+j1obRiKzJO83"
  org_slug: "byteflow"
  wss_url: "wss://be-web-builder.webbuilder.site/ws/agent"
be_screenshot_api_base_url: "http://be-screenshot:9143"
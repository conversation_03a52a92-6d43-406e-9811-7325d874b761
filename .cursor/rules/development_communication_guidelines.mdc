---
description: 
globs: 
alwaysApply: true
---
# Development and Communication Guidelines

## Communication Language

- 永远用中文回答我 (Always respond in Chinese).

## Code and Logging Standards

- 项目里会被日志打印出来的东西都用英文 (Log messages in the project should be in English).
- 代码注释用中文 (Code comments should be in Chinese).
- 所有设计要符合阿里巴巴开发规范 (All designs should generally follow Alibaba Development Specifications, focusing on their universal principles).
- Commit Message: 遵循 Conventional Commits 规范 (e.g., `feat:`, `fix:`, `refactor:`, `docs:`, `test:`, `chore:` etc.).
- 在写代码时遵循最小改动原则，避免影响原先的功能即使识别到历史问题也不要自行优化，可以先告知我问题描述和对当前需求的影响，不要直接改跟本次需求无关的代码
- 请你按照开发者的角度写代码注释，而不是给我讲解的角度

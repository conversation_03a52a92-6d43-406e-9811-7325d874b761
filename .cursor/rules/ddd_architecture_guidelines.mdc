---
description: 
globs: 
alwaysApply: true
---
# DDD Architecture Guidelines

This project uses the DDD (Domain-Driven Design) and adheres to the following specifications.

## Repository Structure

- **domain**: The heart of the software, representing business logic and rules.
    - **entities**: Fundamental objects within our system, like Product and Seller. Contains basic validation logic.
    - **repository**: Repository interfaces that define data access contracts
- **application**: Contains use-case specific operations that interact with the domain layer.
    - **services**: Implements application logic by interacting with entities and repository interfaces.
    - **commands / queries**: Represents specific operations that encapsulate input for modifying or reading data.
    - **mappers**: Responsible for converting between domain models and other representations. 
- **infrastructure**: Supports the higher layers with technical capabilities like database access.
    - **db**: Database access and models.
    - **repositories**: Concrete implementations of our storage needs.
- **interface**: The external layer which interacts with the outside world, like API endpoints.
    - **api/rest**: Handlers or controllers for managing HTTP requests and responses.
    - **dto**: Defines Data Transfer Objects used for API input and output.

## Further principles

### Domain

*   Must not depend on other layers.
*   Provides infrastructure with interfaces, but must not access infrastructure.
*   Implements business logic and rules.
*   Executes validations on entities. Validated entities are passed to the infrastructure layer.
*   Domain layer sets defaults of entities (e.g. uuid for ID or creation timestamp). Don't set defaults in the infrastructure layer or even database!
*   Do not leak domain objects to the outside world.

### Application

*   The glue code between the domain and infrastructure layer.

### Infrastructure

*   Repositories are responsible for translating a domain entity to a database model and retrieving it. No business logic is executed here.
*   Implements interfaces defined by the domain layer.
*   Implements persistence logic like accessing a postgres or mysql database.
*   When writing to storage, read written data before returning it. This ensures that the data is written correctly.

## Best Practices

*   Don't return validated entities from read methods in the repository. Instead, return the domain entity type directly.
*   Validations will change over time. You don't want to migrate all the data in your database. Instead, you should guarantee you can always load historical data, regardless of how your validation logic has evolved.
*   Otherwise, you won't be able to read data from the database that was written with a different validation logic. You will have to handle errors at runtime.
*   Push validation to the write side-creation (NewX) and update methods - where you must enforce invariants anyway.
*   Don't put default values (e.g current timestamp or ID) in the database. Set them in the domain layer (factory!) for several reasons:
    *   It's quite dangerous to have two sources of truth.
    *   It's easier to test the domain layer.
    *   Databases can get replaced, and you don't want to have to change all your default values.
*   Always read the entity after write in the infrastructure layer.
    *   This ensures that the data is written correctly, and we are never operating on stale data.
*   `find` vs `get`:
    *   `find` methods can return null or an empty list.
    *   `get` methods must return a value. If the value is not found, throw an error.
*   Deletion: Always use soft deletion. Create a `deleted_at` column in your database and set it to the current timestamp when deleting an entity. This way, you can always restore the entity if needed.

---
description: 
globs: 
alwaysApply: true
---
# be-web-builder 项目概览

本项目旨在创建一个 web-builder 的后端服务。

## 核心功能

1.  调用github和netlify的api来完成一些复杂操作
   
## 技术栈

*   **语言**: Go
*   **Web 框架**: [Gin](mdc:https:/gin-gonic.com)
*   **架构**: 领域驱动设计 (DDD)，参考 [go-ddd](mdc:https:/github.com/sklinkert/go-ddd) 实践。
*   **依赖管理**: Go Modules ([go.mod](mdc:go.mod), [go.sum](mdc:go.sum))
*   **中间件**: 数据库使用supabase
*   **配置文件**: 配置存储在conf文件夹下，通过后缀区分不同环境 yaml 配置: _pro.yaml、_test.yaml、 _dev.yaml、pre.yaml。可被环境变量覆盖。
*   **部署**: 部署使用docker部署，采用docker-compose组织前后端容器以及一个mysql容器，使用.env作为配置，同时生成.env.example

## 项目结构 (预期)

遵循 DDD 分层架构:

*   `cmd/`                     # 程序入口 (例如: [main.go](mdc:cmd/main.go))
*   `internal/`                # 项目内部代码，不对外暴露
    *   `application/`         # 应用层: Use Cases, Application Services
    *   `domain/`              # 领域层: Entities, Value Objects, Domain Events, Repository Interfaces
        *   `project`
            *   `entity/`        # 领域模型
            *   `repository/`      # 仓储接口定义
    *   `infrastructure/`      # 基础设施层:
        *   `db/`              # 持久化实现
            *   `supabase/`    # 连接supabase的数据库（postgres）
        *   `github/`          # github API 客户端, 使用@go-github
        *   `netlify/`         # netlify API 客户端
    *   `interface/`           # 接口层 (例如 REST API Handlers)
        *   `api/rest`
        *   `dto`
*   `pkg/`                     # 可共享的库代码 (如果需要)
*   `test/`                    # 测试
*   `.gitignore`               # Git 忽略文件配置
*   `.env.example`             # 环境变量示例
*   `Dockerfile`               # Dockerfile
*   `docker-compose.yml`       # Docker Compose 配置
*   `go.mod`                   # Go 模块定义
*   `go.sum`                   # Go 模块校验和
*   `README.md`                # 项目说明文件

## 开发规范

*   **日志**: 项目中打印的日志信息应使用英文。
*   **注释**: 代码的每个方法、接口和结构体都要有详细的注释，包含方法的参数和响应体模型，注释应使用中文。
*   **编码规范**: 遵循[阿里巴巴 Java 开发手册](mdc:https:/github.com/alibaba/p3c) (虽然是 Java 规范，但其通用原则可参考) 及 Go 社区通用规范。
*   **Git**: 使用 Git 进行版本控制。
*   **Commit Message**: 遵循 Conventional Commits 规范 (例如 `feat:`, `fix:`, `refactor:`, `docs:`, `test:`, `chore:` 等)。

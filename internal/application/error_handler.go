package application

import (
	"context"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// ErrorHandler 处理项目状态更新相关的错误
type ErrorHandler struct {
	statusRepo repository.ProjectStatusRepository
}

// NewErrorHandler 创建一个新的 ErrorHandler 实例
func NewErrorHandler(statusRepo repository.ProjectStatusRepository) *ErrorHandler {
	return &ErrorHandler{
		statusRepo: statusRepo,
	}
}

// UpdateProjectStatusOnError 处理错误并更新项目状态
// 如果提供了 projectID，则优先使用 projectID 更新状态
// 如果只提供了 netlifySiteID，则使用 netlifySiteID 更新状态
// projectID 和 netlifySiteID 不能同时为空
func (h *ErrorHandler) UpdateProjectStatusOnError(ctx context.Context, projectID, netlifySiteID, errorCode, errorMsg string) error {
	if projectID == "" && netlifySiteID == "" {
		return fmt.Errorf("both projectID and netlifySiteID cannot be empty")
	}

	updates := map[string]any{
		"status":     "done",
		"error_code": errorCode,
		"error_msg":  errorMsg,
	}

	var err error
	if projectID != "" {
		// 优先使用 projectID 更新状态
		_, err = h.statusRepo.UpdateByProjectID(ctx, projectID, updates)
		if err != nil {
			logger.Error("Failed to update project status by ProjectID",
				"Error", err,
				"ProjectID", projectID,
				"ErrorCode", errorCode)
			return fmt.Errorf("failed to update project status by ProjectID %s: %w", projectID, err)
		}
		logger.Info("Successfully updated project status by ProjectID",
			"ProjectID", projectID,
			"ErrorCode", errorCode)
	} else {
		// 使用 netlifySiteID 更新状态
		_, err = h.statusRepo.UpdateByNetlifySiteID(ctx, netlifySiteID, updates)
		if err != nil {
			logger.Error("Failed to update project status by NetlifySiteID",
				"Error", err,
				"NetlifySiteID", netlifySiteID,
				"ErrorCode", errorCode)
			return fmt.Errorf("failed to update project status by NetlifySiteID %s: %w", netlifySiteID, err)
		}
		logger.Info("Successfully updated project status by NetlifySiteID",
			"NetlifySiteID", netlifySiteID,
			"ErrorCode", errorCode)
	}

	return nil
}

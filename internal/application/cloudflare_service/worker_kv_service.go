package cloudflare_service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// WorkerKVService 提供了 Cloudflare Worker KV 的应用服务。
type WorkerKVService struct {
	cloudflareInfra *cloudflare.Service
	cfg             *config.Config
}

// NewWorkerKVService 创建一个新的 WorkerKVService 实例。
func NewWorkerKVService(
	cloudflareInfra *cloudflare.Service,
	cfg *config.Config,
) *WorkerKVService {
	return &WorkerKVService{
		cloudflareInfra: cloudflareInfra,
		cfg:             cfg,
	}
}

// ReadKeyValuePair 从配置的 KV namespace 中读取键值对。
// ctx: 上下文。
// keyName: 要读取的键名。
//
// 返回:
// - string: 键对应的值。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (s *WorkerKVService) ReadKeyValuePair(ctx context.Context, keyName string) (string, error) {

	logger.Debug("Reading key-value pair from KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
	)

	value, err := s.cloudflareInfra.WorkerKV.ReadKeyValuePair(
		ctx,
		s.cfg.Cloudflare.KVNamespaceID,
		keyName,
	)
	if err != nil {
		logger.Error("Failed to read key-value pair from KV",
			"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
			"KeyName", keyName,
			"Error", err.Error(),
		)
		return "", fmt.Errorf("failed to read key-value pair: %w", err)
	}

	logger.Debug("Successfully read key-value pair from KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
		"ValueLength", len(value),
	)

	return value, nil
}

// WriteKeyValuePairWithOptionalMetadata 向配置的 KV namespace 中写入键值对，支持可选的元数据。
// ctx: 上下文。
// keyName: 要写入的键名。
// value: 要写入的值。
// metadata: 可选的元数据（可以为 nil）。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (s *WorkerKVService) WriteKeyValuePairWithOptionalMetadata(
	ctx context.Context,
	keyName, value string,
	metadata map[string]interface{},
) error {
	if s.cfg.Cloudflare.KVNamespaceID == "" {
		return fmt.Errorf("KV namespace ID is not configured")
	}

	logger.Debug("Writing key-value pair to KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
		"ValueLength", len(value),
		"HasMetadata", metadata != nil,
	)

	err := s.cloudflareInfra.WorkerKV.WriteKeyValuePairWithOptionalMetadata(
		ctx,
		s.cfg.Cloudflare.KVNamespaceID,
		keyName,
		value,
		metadata,
	)
	if err != nil {
		logger.Error("Failed to write key-value pair to KV",
			"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
			"KeyName", keyName,
			"Error", err.Error(),
		)
		return fmt.Errorf("failed to write key-value pair: %w", err)
	}

	logger.Info("Successfully wrote key-value pair to KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
		"ValueLength", len(value),
		"HasMetadata", metadata != nil,
	)

	return nil
}

// WriteKeyValuePair 向配置的 KV namespace 中写入键值对（不带元数据）。
// 这是一个便利方法，等同于调用 WriteKeyValuePairWithOptionalMetadata 并传入 nil 元数据。
// ctx: 上下文。
// keyName: 要写入的键名。
// value: 要写入的值。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (s *WorkerKVService) WriteKeyValuePair(ctx context.Context, keyName, value string) error {
	return s.WriteKeyValuePairWithOptionalMetadata(ctx, keyName, value, nil)
}

// URLConfig 表示存储在 KV 中的 URL 配置结构
type URLConfig struct {
	PreviewURL string `json:"previewUrl,omitempty"`
	PublishURL string `json:"publishUrl,omitempty"`
}

// CreateOrUpdateURLConfig 创建或更新指定 key 的 URL 配置。
// 如果 key 不存在，则创建新的配置；如果存在，则更新指定的字段。
// previewUrl 和 publishUrl 参数可以为 nil，表示不更新对应字段。
//
// ctx: 上下文。
// keyName: 要操作的键名。
// previewUrl: 预览 URL，为 nil 时不更新此字段。
// publishUrl: 发布 URL，为 nil 时不更新此字段。
//
// 返回:
// - error: 如果在操作过程中发生错误，则返回错误信息。
func (s *WorkerKVService) CreateOrUpdateURLConfig(
	ctx context.Context,
	keyName string,
	previewUrl, publishUrl *string,
) error {
	if s.cfg.Cloudflare.KVNamespaceID == "" {
		return fmt.Errorf("KV namespace ID is not configured")
	}

	logger.Debug("Creating or updating URL config",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
		"HasPreviewUrl", previewUrl != nil,
		"HasPublishUrl", publishUrl != nil,
	)

	// 尝试获取现有配置
	var config URLConfig
	existingValue, err := s.ReadKeyValuePair(ctx, keyName)
	if err != nil {
		// 如果读取失败，假设 key 不存在，创建新配置
		logger.Debug("Key does not exist, creating new config",
			"KeyName", keyName,
			"Error", err.Error(),
		)
	} else {
		// 解析现有配置
		if err := json.Unmarshal([]byte(existingValue), &config); err != nil {
			logger.Warn("Failed to parse existing config, creating new config",
				"KeyName", keyName,
				"ExistingValue", existingValue,
				"Error", err.Error(),
			)
			// 解析失败，重置为空配置
			config = URLConfig{}
		} else {
			logger.Debug("Successfully parsed existing config",
				"KeyName", keyName,
				"ExistingPreviewUrl", config.PreviewURL,
				"ExistingPublishUrl", config.PublishURL,
			)
		}
	}

	// 更新指定的字段
	if previewUrl != nil {
		config.PreviewURL = *previewUrl
		logger.Debug("Updated preview URL", "KeyName", keyName, "PreviewUrl", *previewUrl)
	}
	if publishUrl != nil {
		config.PublishURL = *publishUrl
		logger.Debug("Updated publish URL", "KeyName", keyName, "PublishUrl", *publishUrl)
	}

	// 序列化配置为 JSON
	configJSON, err := json.Marshal(config)
	if err != nil {
		logger.Error("Failed to marshal URL config",
			"KeyName", keyName,
			"Config", config,
			"Error", err.Error(),
		)
		return fmt.Errorf("failed to marshal URL config: %w", err)
	}

	// 写入到 KV
	err = s.WriteKeyValuePair(ctx, keyName, string(configJSON))
	if err != nil {
		logger.Error("Failed to write URL config to KV",
			"KeyName", keyName,
			"Error", err.Error(),
		)
		return fmt.Errorf("failed to write URL config: %w", err)
	}

	logger.Info("Successfully created or updated URL config",
		"KeyName", keyName,
		"PreviewUrl", config.PreviewURL,
		"PublishUrl", config.PublishURL,
		"UpdatedPreview", previewUrl != nil,
		"UpdatedPublish", publishUrl != nil,
	)

	return nil
}

// DeleteKeyValuePair 从 KV 存储中删除指定的键值对。
func (s *WorkerKVService) DeleteKeyValuePair(ctx context.Context, keyName string) error {
	logger.Debug("Deleting key-value pair from KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
	)

	err := s.cloudflareInfra.WorkerKV.DeleteKeyValuePair(ctx, s.cfg.Cloudflare.KVNamespaceID, keyName)
	if err != nil {
		logger.Error("Failed to delete key-value pair from KV",
			"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
			"KeyName", keyName,
			"Error", err,
		)
		return fmt.Errorf("failed to delete key-value pair: %w", err)
	}

	logger.Info("Successfully deleted key-value pair from KV",
		"NamespaceID", s.cfg.Cloudflare.KVNamespaceID,
		"KeyName", keyName,
	)
	return nil
}

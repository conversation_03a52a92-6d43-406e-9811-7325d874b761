package cloudflare_service

import (
	"context"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// InitCloudflarePagesService 提供了初始化 Cloudflare Pages 项目的应用服务。
type InitCloudflarePagesService struct {
	cloudflareInfra *cloudflare.Service
	cfg             *config.Config
}

// NewInitCloudflarePagesService 创建一个新的 InitCloudflarePagesService 实例。
func NewInitCloudflarePagesService(
	cloudflareInfra *cloudflare.Service,
	cfg *config.Config,
) *InitCloudflarePagesService {
	return &InitCloudflarePagesService{
		cloudflareInfra: cloudflareInfra,
		cfg:             cfg,
	}
}

// InitCloudflarePagesCore 初始化 Cloudflare Pages 项目。
// ctx: 上下文。
// projectName: 项目名称。
// buildCommand: 构建命令。
// buildOutputDir: 构建输出目录。
// repoName: GitHub 仓库名称。
//
// 返回:
// - string: 创建的项目 ID。
// - error: 如果在过程中发生任何错误。
func (s *InitCloudflarePagesService) InitCloudflarePagesCore(
	ctx context.Context,
	projectName string,
	buildCommand string,
	buildOutputDir string,
	repoName string,
) (string, error) {
	// 创建 Pages 项目
	project, err := s.cloudflareInfra.Project.CreateProject(
		ctx,
		projectName,
		buildCommand,       // 构建命令
		buildOutputDir,     // 构建输出目录
		"github",           // 源代码类型
		s.cfg.GitHub.Owner, // 从配置中读取 GitHub 仓库所有者
		repoName,           // GitHub 仓库名称
		"main",             // 生产环境分支
	)
	if err != nil {
		logger.Error("Failed to create Cloudflare Pages project",
			"ProjectName", projectName,
			"BuildCommand", buildCommand,
			"BuildOutputDir", buildOutputDir,
			"Owner", s.cfg.GitHub.Owner,
			"RepoName", repoName,
			"Error", err.Error(),
		)
		return "", fmt.Errorf("failed to create Cloudflare Pages project: %w", err)
	}

	logger.Info("Successfully created Cloudflare Pages project",
		"ProjectName", projectName,
		"BuildCommand", buildCommand,
		"BuildOutputDir", buildOutputDir,
		"ProjectID", project.ID,
	)

	// 创建通知策略
	notificationService := NewNotificationService(s.cloudflareInfra, s.cfg)
	policyID, err := notificationService.CreateNotificationPolicy(ctx, projectName)
	if err != nil {
		logger.Error("Failed to create notification policy",
			"ProjectName", projectName,
			"ProjectID", project.ID,
			"Error", err.Error(),
		)
		return project.ID, fmt.Errorf("failed to create notification policy: %w", err)
	}

	logger.Info("Successfully created notification policy",
		"ProjectName", projectName,
		"ProjectID", project.ID,
		"PolicyID", policyID,
	)

	// 创建部署
	deployment, err := s.cloudflareInfra.Project.CreateDeployment(ctx, projectName)
	if err != nil {
		logger.Error("Failed to create Cloudflare Pages deployment",
			"ProjectName", projectName,
			"ProjectID", project.ID,
			"Error", err.Error(),
		)
		return project.ID, fmt.Errorf("failed to create Cloudflare Pages deployment: %w", err)
	}

	logger.Info("Successfully created Cloudflare Pages deployment",
		"ProjectName", projectName,
		"ProjectID", project.ID,
		"DeploymentID", deployment.ID,
	)

	return project.ID, nil
}

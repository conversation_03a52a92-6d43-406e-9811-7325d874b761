package cloudflare_service

import (
	"context"
	"fmt"

	cf "github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/alerting"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// NotificationService 提供了在 Cloudflare 项目上管理通知策略的应用服务。
type NotificationService struct {
	cloudflareInfraService *cloudflare.Service
	webhookID              string
}

// NewNotificationService 创建一个新的 NotificationService 实例。
// cloudflareInfraService: Cloudflare 基础设施服务实例。
// cfg: 应用配置，用于获取 Webhook ID。
func NewNotificationService(cloudflareInfraService *cloudflare.Service, cfg *config.Config) *NotificationService {
	return &NotificationService{
		cloudflareInfraService: cloudflareInfraService,
		webhookID:              cfg.Cloudflare.WebhookID,
	}
}

// CreateNotificationPolicy 为指定的 Cloudflare 项目创建通知策略。
// ctx: 上下文。
// projectName: Cloudflare Pages 项目名称。
//
// 返回:
// - string: 成功创建的通知策略 ID。
// - error: 如果在过程中发生任何配置错误或致命错误。
func (s *NotificationService) CreateNotificationPolicy(ctx context.Context, projectName string) (string, error) {
	if s.webhookID == "" {
		logger.Error("Cloudflare webhook ID is not configured. Cannot create notification policy.")
		return "", fmt.Errorf("cloudflare webhook ID is not configured")
	}
	if projectName == "" {
		logger.Error("Cloudflare project name is empty. Cannot create notification policy.")
		return "", fmt.Errorf("cloudflare project name cannot be empty")
	}

	logger.Info("Starting to create notification policy", "ProjectName", projectName)

	// 获取项目信息
	project, err := s.cloudflareInfraService.Project.GetProjectByName(ctx, projectName)
	if err != nil {
		logger.Error("Failed to get project information", "ProjectName", projectName, "Error", err.Error())
		return "", fmt.Errorf("failed to get project information: %w", err)
	}

	// 准备通知策略参数
	description := "Default notification policy for deployment events"
	alertType := alerting.PolicyNewParamsAlertTypePagesEventAlert
	mechanisms := alerting.MechanismParam{
		Webhooks: cf.F([]alerting.MechanismWebhookParam{
			{
				ID: cf.F(s.webhookID),
			},
		}),
	}
	enabled := true

	// 构建过滤器
	filters := alerting.PolicyFilterParam{
		ProjectID:   cf.F([]string{project.ID}),
		Event:       cf.F([]string{"EVENT_DEPLOYMENT_STARTED", "EVENT_DEPLOYMENT_FAILED", "EVENT_DEPLOYMENT_SUCCESS"}),
		Environment: cf.F([]string{"ENVIRONMENT_PRODUCTION", "ENVIRONMENT_PREVIEW"}),
	}

	// 创建通知策略
	logger.Info("Creating notification policy", "ProjectName", projectName, "ProjectID", project.ID, "WebhookID", s.webhookID)
	policyID, err := s.cloudflareInfraService.Alerting.CreateNotificationPolicy(
		ctx,
		projectName,
		description,
		alertType,
		mechanisms,
		enabled,
		filters,
	)
	if err != nil {
		logger.Error("Failed to create notification policy", "ProjectName", projectName, "Error", err.Error())
		return "", fmt.Errorf("failed to create notification policy: %w", err)
	}

	logger.Info("Successfully created notification policy", "ProjectName", projectName, "PolicyID", policyID)
	return policyID, nil
}

// DeleteNotificationPolicy 删除指定的 Cloudflare 通知策略。
// ctx: 上下文。
// policyID: 要删除的通知策略 ID。
//
// 返回:
// - error: 如果在过程中发生任何错误。
func (s *NotificationService) DeleteNotificationPolicy(ctx context.Context, policyID string) error {
	if policyID == "" {
		logger.Error("Cloudflare policy ID is empty. Cannot delete notification policy.")
		return fmt.Errorf("cloudflare policy ID cannot be empty")
	}

	logger.Info("Starting to delete notification policy", "PolicyID", policyID)

	err := s.cloudflareInfraService.Alerting.DeleteNotificationPolicy(ctx, policyID)
	if err != nil {
		logger.Error("Failed to delete notification policy", "PolicyID", policyID, "Error", err.Error())
		return fmt.Errorf("failed to delete notification policy: %w", err)
	}

	logger.Info("Successfully deleted notification policy", "PolicyID", policyID)
	return nil
}

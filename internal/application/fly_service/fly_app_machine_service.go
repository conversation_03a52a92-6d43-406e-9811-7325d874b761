package fly_service

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/pointer"
)

// FlyAppMachineService 提供创建 app 并创建 machine 的一站式服务
type FlyAppMachineService struct {
	flyInfra          *actualFly.Service
	cfg               *config.Config
	projectStatusRepo repository.ProjectStatusRepository
}

// NewFlyAppMachineService 构造方法
func NewFlyAppMachineService(flyInfra *actualFly.Service, cfg *config.Config, projectStatusRepo repository.ProjectStatusRepository) *FlyAppMachineService {
	return &FlyAppMachineService{
		flyInfra:          flyInfra,
		cfg:               cfg,
		projectStatusRepo: projectStatusRepo,
	}
}

// CreateAppAndMachine 创建 app 并创建 machine
// appName: 应用名
// 返回创建的 machine 结构体和错误
func (s *FlyAppMachineService) CreateAppAndMachine(ctx context.Context, appName string) (*actualFly.Machine, error) {
	// 1. 创建 app
	appReq := &actualFly.CreateAppRequest{
		AppName:          appName,
		EnableSubdomains: false,
		Network:          "",
	}
	if err := s.flyInfra.Apps.CreateApp(ctx, appReq); err != nil {
		return nil, err
	}

	// 2. 创建 volume
	region := "hkg" // 暂时硬编码
	// 生成合法的 volume 名称
	sanitizeVolumeName := func(name string) string {
		name = strings.ToLower(name)
		re := regexp.MustCompile(`[^a-z0-9_]`)
		name = re.ReplaceAllString(name, "_")
		if len(name) > 30 {
			name = name[:30]
		}
		return name
	}
	volumeName := sanitizeVolumeName(appName + "_data")
	logger.Info("Creating volume for app", "AppName", appName, "VolumeName", volumeName, "Region", region)
	vol, err := s.flyInfra.Volumes.CreateVolume(ctx, appName, &actualFly.CreateVolumeRequest{
		Name:   volumeName,
		Region: region,
		SizeGB: 1, // 默认 1GB
	})
	if err != nil {
		logger.Error("Failed to create volume", "AppName", appName, "VolumeName", volumeName, "Error", err)
		return nil, err
	}
	logger.Info("Successfully created volume", "AppName", appName, "VolumeID", vol.ID, "VolumeName", vol.Name)

	// 3. 分配 IP 地址
	logger.Info("Allocating IP address for app", "AppName", appName)
	ipAddress, err := s.flyInfra.IPAddresses.AllocateIPAddress(ctx, appName, "shared_v4")
	if err != nil {
		logger.Error("Failed to allocate IP address", "AppName", appName, "Error", err)
		return nil, err
	}
	logger.Info("Successfully allocated IP address", "AppName", appName, "IPAddress", ipAddress)

	// 4. 创建 machine
	machineReq := &actualFly.CreateMachineRequest{
		Name:   pointer.PtrString(appName + "-machine"),
		Region: pointer.PtrString(region),
		Config: &actualFly.FlyMachineConfig{
			Image: pointer.PtrString("registry.fly.io/fly-agent-base:latest"),
			Guest: &actualFly.FlyMachineGuest{
				CpuKind:  pointer.PtrString("shared"),
				Cpus:     pointer.PtrInt32(2),
				MemoryMb: pointer.PtrInt32(2048),
			},
			Mounts: []actualFly.FlyMachineMount{
				{
					Volume: pointer.PtrString(vol.ID),
					Path:   pointer.PtrString("/data"),
				},
			},
			Services: []actualFly.FlyMachineService{
				{
					Autostart:    pointer.PtrBool(true),
					Autostop:     pointer.PtrBool(true),
					InternalPort: pointer.PtrInt32(80),
					Protocol:     pointer.PtrString("tcp"),
					Ports: []actualFly.FlyMachinePort{
						{
							Port:     pointer.PtrInt32(443),
							Handlers: []string{"tls", "http"},
						},
						{
							Port:     pointer.PtrInt32(80),
							Handlers: []string{"http"},
						},
					},
				},
			},
			Env: &map[string]string{
				"WSS_URL":                                s.cfg.Fly.WSSURL,
				"__VITE_ADDITIONAL_SERVER_ALLOWED_HOSTS": ".fly.dev",
				"WB_SCRIPT_URL":                          "https://webbuilder.site/iframe-bridge.js",
			},
		},
	}
	machine, err := s.flyInfra.Machines.CreateMachine(ctx, appName, machineReq)
	if err != nil {
		return nil, err
	}
	return machine, nil
}

// CheckAndEnsureAppExists 检查 fly.io 应用是否存在，如果不存在则创建
// projectID: 项目ID，也是应用名称
// 返回应用是否已存在和错误
func (s *FlyAppMachineService) CheckAndEnsureAppExists(ctx context.Context, projectID string) (bool, error) {
	logger.Info("Checking if Fly.io app exists", "ProjectID", projectID)

	// 1. 检查应用是否存在
	_, err := s.flyInfra.Apps.GetApp(ctx, projectID)
	if err != nil {
		// 检查是否是应用不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			logger.Info("Fly.io app does not exist, will create it", "ProjectID", projectID)

			// 2. 更新项目状态为 in_progress
			updates := map[string]interface{}{
				"status": entity.ProjectStatusInProgress,
			}
			if _, err := s.projectStatusRepo.UpdateByProjectID(ctx, projectID, updates); err != nil {
				logger.Error("Failed to update project status to in_progress", "ProjectID", projectID, "Error", err)
				return false, fmt.Errorf("failed to update project status: %w", err)
			}
			logger.Info("Updated project status to in_progress", "ProjectID", projectID)

			// 3. 创建 Fly.io 应用和机器，异步
			go func() {
				ctx := context.Background() // 使用新的 context
				_, err := s.CreateAppAndMachine(ctx, projectID)
				if err != nil {
					logger.Error("Failed to create Fly.io app and machine", "ProjectID", projectID, "Error", err)
					return
				}
				logger.Info("Successfully created Fly.io app and machine", "ProjectID", projectID)
			}()
			return false, nil // 应用之前不存在，现在已创建
		}

		// 其他错误
		logger.Error("Failed to check Fly.io app existence", "ProjectID", projectID, "Error", err)
		return false, fmt.Errorf("failed to check app existence: %w", err)
	}

	// 应用已存在
	logger.Info("Fly.io app already exists", "ProjectID", projectID)
	return true, nil
}

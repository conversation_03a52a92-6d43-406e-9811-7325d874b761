package netlify_service

import (
	"context"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// HookService 提供了在 Netlify 站点上设置 Webhook 的应用服务。
type HookService struct {
	netlifyInfraService *netlify.Service
	netlifyHookURL      string
}

// NewHookService 创建一个新的 HookService 实例。
// netlifyInfraService: Netlify 基础设施服务实例。
// cfg: 应用配置，用于获取 Netlify Hook URL。
func NewHookService(netlifyInfraService *netlify.Service, cfg *config.Config) *HookService {
	return &HookService{
		netlifyInfraService: netlifyInfraService,
		netlifyHookURL:      cfg.Netlify.HookURL,
	}
}

// SetupDefaultHooks 为指定的 Netlify 站点设置一组默认的 Webhook。
// ctx: 上下文。
// netlifySiteID: Netlify 站点的 ID。
//
// 返回:
// - []*netlify.Hook: 成功创建的 Netlify Hook 对象列表。
// - error: 如果在过程中发生任何配置错误或致命错误。部分创建成功时也会返回已创建的hooks。
func (s *HookService) SetupDefaultHooks(ctx context.Context, netlifySiteID string) ([]*netlify.Hook, error) {
	if s.netlifyHookURL == "" {
		logger.Error("Netlify hook URL is not configured. Cannot setup default hooks.")
		return nil, fmt.Errorf("netlify hook URL is not configured")
	}
	if netlifySiteID == "" {
		logger.Error("Netlify site ID is empty. Cannot setup default hooks.")
		return nil, fmt.Errorf("netlify site ID cannot be empty")
	}

	logger.Info("Starting to set up default hooks", "NetlifySiteID", netlifySiteID)
	createdHooks := []*netlify.Hook{}
	hookType := "url"
	events := []string{"deploy_building", "deploy_created", "deploy_failed"}

	for _, event := range events {
		hookData := netlify.HookCreateRequestData{
			SiteID: netlifySiteID,
			Type:   hookType,
			Event:  event,
			Data: netlify.HookData{
				URL: s.netlifyHookURL, // 现在 URL 是 data 里的字段
			},
		}

		logger.Info("Creating hook for event", "Event", event, "SiteID", netlifySiteID, "TargetURL", s.netlifyHookURL)
		hookResponse, err := s.netlifyInfraService.Hooks.CreateHook(ctx, hookData)
		if err != nil {
			// 按照Python示例，记录错误并继续尝试创建其他hooks
			logger.Error("Failed to create hook for event", "Event", event, "SiteID", netlifySiteID, "Error", err.Error())
			// 可以在这里收集错误，或者只记录
		} else {
			createdHooks = append(createdHooks, hookResponse)
			logger.Info("Successfully created hook for event", "Event", event, "SiteID", netlifySiteID, "HookID", hookResponse.ID)
		}
	}

	if len(createdHooks) == len(events) {
		logger.Info("Successfully created all default hooks for site.", "SiteID", netlifySiteID, "Count", len(events))
	} else {
		logger.Warn("Encountered issues while creating default hooks for site.",
			"SiteID", netlifySiteID,
			"SuccessfullyCreated", len(createdHooks),
			"Expected", len(events),
		)
	}

	return createdHooks, nil // 即使部分失败，也返回成功创建的hooks
}

package netlify_service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// InitNetlifySiteService 提供了初始化 Netlify 站点的应用服务。
// 它负责编排创建部署密钥、创建站点、配置GitHub仓库、设置Webhook等一系列操作。
type InitNetlifySiteService struct {
	netlifyInfra   *netlify.Service
	githubInfra    *infraGitHub.Service
	hookAppService *HookService                 // 应用层的HookService
	projectRepo    repository.ProjectRepository // 项目仓库
	cfg            *config.Config
}

// NewInitNetlifySiteService 创建一个新的 InitNetlifySiteService 实例。
func NewInitNetlifySiteService(
	netlifyInfra *netlify.Service,
	githubInfra *infraGitHub.Service,
	hookAppService *HookService,
	projectRepo repository.ProjectRepository, // 新增项目仓库参数
	cfg *config.Config,
) *InitNetlifySiteService {
	return &InitNetlifySiteService{
		netlifyInfra:   netlifyInfra,
		githubInfra:    githubInfra,
		hookAppService: hookAppService,
		projectRepo:    projectRepo, // 赋值项目仓库
		cfg:            cfg,
	}
}

// SetupDeployKey 创建 Netlify 部署密钥并添加到指定的 GitHub 仓库
// ctx: 上下文
// newRepoName: GitHub 仓库名称
// githubOwner: GitHub 仓库所有者
// 返回创建的部署密钥和错误（如果有）
func (s *InitNetlifySiteService) SetupDeployKey(ctx context.Context, newRepoName string, githubOwner string) (*netlify.DeployKey, error) {
	// 1. 创建 Netlify 部署密钥
	deployKey, err := s.netlifyInfra.Deploys.CreateDeployKey(ctx)
	if err != nil {
		logger.Error("Failed to create Netlify deploy key during core init", "GitHubRepoName", newRepoName, "Error", err)
		return nil, fmt.Errorf("failed to create Netlify deploy key for repo %s: %w", newRepoName, err)
	}
	logger.Info("Successfully created Netlify deploy key", "GitHubRepoName", newRepoName, "DeployKeyID", deployKey.ID)

	// 2. 添加部署密钥到新 GitHub 仓库
	ghKeyData := &gh.Key{
		Title:    gh.Ptr(fmt.Sprintf("Netlify Deploy Key for %s", newRepoName)),
		Key:      gh.Ptr(strings.TrimSpace(deployKey.PublicKey)),
		ReadOnly: gh.Ptr(true),
	}
	_, _, err = s.githubInfra.Repositories.AddDeployKey(ctx, githubOwner, newRepoName, ghKeyData)
	if err != nil {
		logger.Error("Failed to add deploy key to GitHub repository during core init",
			"GitHubRepo", githubOwner+"/"+newRepoName, "Error", err,
			"Message", "Automated builds might fail. Please check repository deploy keys.")
	} else {
		logger.Info("Successfully added deploy key to GitHub repository", "GitHubRepo", githubOwner+"/"+newRepoName)
	}

	return deployKey, nil
}

// CreateNetlifySite 创建 Netlify 站点并关联到指定的 GitHub 仓库
// ctx: 上下文
// newRepoName: GitHub 仓库名称
// githubOwner: GitHub 仓库所有者
// deployKey: 已创建的部署密钥
// defaultBuildCmd: 默认构建命令
// defaultPublishDir: 默认发布目录
// 返回创建的站点对象和错误（如果有）
func (s *InitNetlifySiteService) CreateNetlifySite(ctx context.Context, newRepoName string, githubOwner string, deployKey *netlify.DeployKey, defaultBuildCmd string, defaultPublishDir string) (*netlify.Site, error) {
	// 生成一个相对独特的站点名称
	// 使用 repoName 的一部分和时间戳来生成 siteName，因为没有 projectID
	var siteNameIdentifier string
	if len(newRepoName) > 15 { // 取仓库名一部分避免过长
		siteNameIdentifier = newRepoName[:8] + newRepoName[len(newRepoName)-7:]
	} else {
		siteNameIdentifier = newRepoName
	}
	siteName := fmt.Sprintf("site-%s-%d", siteNameIdentifier, time.Now().Unix())

	siteBuildSettings := &netlify.SiteBuildSettingsInfo{
		RepoURL:         fmt.Sprintf("https://github.com/%s/%s", githubOwner, newRepoName),
		RepoBranch:      "main", // 假设默认分支为 main
		Provider:        "github",
		DeployKeyID:     deployKey.ID,
		AllowedBranches: []string{"main"},
		Cmd:             defaultBuildCmd,
		Dir:             defaultPublishDir,
	}

	siteCreateOpts := &netlify.CreateSiteOptions{
		Name:          siteName,
		BuildSettings: siteBuildSettings,
	}

	logger.Info("Attempting to create Netlify site during core init", "SiteName", siteName, "GitHubRepoName", newRepoName)
	createdSite, err := s.netlifyInfra.Sites.CreateSite(ctx, siteCreateOpts)
	if err != nil {
		logger.Error("Failed to create Netlify site during core init", "SiteName", siteName, "GitHubRepoName", newRepoName, "Error", err)
		return nil, fmt.Errorf("failed to create Netlify site %s for repo %s: %w", siteName, newRepoName, err)
	}

	// 4. 创建 Netlify 代码片段 (wb-develop-script.js)
	projectRoot, err := config.FindProjectRoot()
	if err != nil {
		logger.Error("Failed to find project root, cannot read wb-develop-script.js", "GitHubRepoName", newRepoName, "Error", err)
	} else {
		iframeBridgeJSPath := filepath.Join(projectRoot, "static", "wb-develop-script.js")
		jsContent, readErr := os.ReadFile(iframeBridgeJSPath)
		if readErr != nil {
			logger.Error("Failed to read wb-develop-script.js", "GitHubRepoName", newRepoName, "Path", iframeBridgeJSPath, "Error", readErr,
				"Message", "iframe-bridge snippet will not be created.")
		} else {
			logger.Info("Successfully read wb-develop-script.js", "GitHubRepoName", newRepoName, "Path", iframeBridgeJSPath)
			snippetData := &netlify.CreateSnippetRequest{
				Title:           "iframe-bridge",
				General:         string(jsContent),
				GeneralPosition: "footer",
			}
			err = s.netlifyInfra.Snippets.CreateSiteSnippet(ctx, createdSite.ID, snippetData)
			if err != nil {
				logger.Error("Failed to create Netlify snippet 'iframe-bridge'",
					"SiteID", createdSite.ID, "GitHubRepoName", newRepoName, "Error", err)
			} else {
				logger.Info("Successfully created Netlify snippet 'iframe-bridge'", "SiteID", createdSite.ID, "GitHubRepoName", newRepoName)
			}
		}
	}

	logger.Info("Successfully created Netlify site during core init", "SiteID", createdSite.ID, "SiteName", createdSite.Name, "GitHubRepoName", newRepoName)
	return createdSite, nil
}

// InitNetlifySiteCore 是 InitNetlifySite 的核心逻辑，不处理特定项目ID或数据库更新。
// newRepoName: 新创建的 GitHub 仓库的名称 (例如 "my-awesome-site")。
// githubOwner: GitHub 仓库的所有者。
// defaultBuildCmd: Netlify 站点的默认构建命令。
// defaultPublishDir: Netlify 站点的默认发布目录。
// 返回成功创建的 Netlify 站点的 ID 和错误（如果有）。
func (s *InitNetlifySiteService) InitNetlifySiteCore(ctx context.Context, newRepoName string, githubOwner string, defaultBuildCmd string, defaultPublishDir string) (string, error) {
	logger.Info("Starting core Netlify site initialization", "GitHubRepoName", newRepoName)
	var newNetlifySite *netlify.Site

	// 设置部署密钥
	deployKey, err := s.SetupDeployKey(ctx, newRepoName, githubOwner)
	if err != nil {
		return "", err
	}

	// 创建 Netlify 站点
	newNetlifySite, err = s.CreateNetlifySite(ctx, newRepoName, githubOwner, deployKey, defaultBuildCmd, defaultPublishDir)
	if err != nil {
		return "", err
	}
	newNetlifySiteID := newNetlifySite.ID

	// 5. 设置默认 Webhooks
	if s.hookAppService != nil {
		_, hookErr := s.hookAppService.SetupDefaultHooks(ctx, newNetlifySiteID)
		if hookErr != nil {
			logger.Warn("Failed to setup default webhooks for Netlify site",
				"SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName, "Error", hookErr,
				"Message", "This might affect notifications or integrations.")
		} else {
			logger.Info("Successfully setup default webhooks for Netlify site", "SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)
		}
	} else {
		logger.Warn("HookApplicationService is not initialized, skipping setup of default webhooks.", "SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)
	}

	// 6. 锁定初始部署
	logger.Info("Waiting briefly before attempting to lock initial deploy...", "SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)

	listDeployOpts := &netlify.ListSiteDeploysOptions{
		Page:    1,
		PerPage: 1, // 获取最新的一个部署
	}
	deploys, listErr := s.netlifyInfra.Deploys.ListSiteDeploys(ctx, newNetlifySiteID, listDeployOpts)
	if listErr != nil {
		logger.Warn("Failed to list deploys for Netlify site, cannot lock initial deploy.",
			"SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName, "Error", listErr)
	} else if len(deploys) > 0 {
		latestDeploy := deploys[0]
		logger.Info("Found latest deploy to lock", "SiteID", newNetlifySiteID, "DeployID", latestDeploy.ID, "GitHubRepoName", newRepoName)
		_, lockErr := s.netlifyInfra.Deploys.LockDeploy(ctx, latestDeploy.ID)
		if lockErr != nil {
			logger.Error("Failed to lock deploy for Netlify site",
				"SiteID", newNetlifySiteID, "DeployID", latestDeploy.ID, "GitHubRepoName", newRepoName, "Error", lockErr)
		} else {
			logger.Info("Successfully locked deploy for Netlify site", "SiteID", newNetlifySiteID, "DeployID", latestDeploy.ID, "GitHubRepoName", newRepoName)
		}
	} else {
		logger.Warn("No deploys found for Netlify site after creation, cannot lock initial deploy.", "SiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)
	}

	logger.Info("Core Netlify site initialization process completed.", "NetlifySiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)
	return newNetlifySiteID, nil
}

// InitNetlifySite 初始化 Netlify 站点，包括创建站点、配置部署密钥、设置钩子和锁定初始部署。
// targetProjectID: 目标项目的标识符，用于日志和资源命名。
// newRepoName: 新创建的 GitHub 仓库的名称 (例如 "my-awesome-site")。
//
// 返回:
// - string: 成功创建的 Netlify 站点的 ID。
// - error: 如果在初始化过程中发生严重错误。
func (s *InitNetlifySiteService) InitNetlifySite(ctx context.Context, targetProjectID string, newRepoName string) (string, error) {
	logger.Info("Starting to initialize Netlify site for project", "ProjectID", targetProjectID, "GitHubRepoName", newRepoName)

	// 从配置中获取 GitHub Owner 和默认构建配置
	githubOwner := s.cfg.GitHub.Owner
	// TODO: 考虑将默认构建命令和发布目录也加入配置，或作为参数传递
	defaultBuildCmd := "npm run build"
	defaultPublishDir := "dist"

	newNetlifySiteID, err := s.InitNetlifySiteCore(ctx, newRepoName, githubOwner, defaultBuildCmd, defaultPublishDir)
	if err != nil {
		// InitNetlifySiteCore 内部已经记录了错误细节
		logger.Error("Core Netlify site initialization failed for project", "ProjectID", targetProjectID, "GitHubRepoName", newRepoName, "Error", err)
		return "", err // 直接返回 InitNetlifySiteCore 的错误
	}

	// 更新目标项目记录 (with netlify_site_id)
	if newNetlifySiteID != "" && s.projectRepo != nil {
		logger.Info("Attempting to update project record with Netlify Site ID", "ProjectID", targetProjectID, "NetlifySiteID", newNetlifySiteID)
		projectEntity, getErr := s.projectRepo.GetByID(ctx, targetProjectID)
		if getErr != nil {
			logger.Error("Failed to get project entity for update after Netlify init",
				"ProjectID", targetProjectID, "Error", getErr,
				"Message", "Netlify Site ID will not be saved to project record at this stage.")
			// 即使更新失败，也返回已创建的 newNetlifySiteID，因为核心流程成功了
		} else {
			projectEntity.NetlifySiteID = newNetlifySiteID
			projectEntity.UpdatedAt = time.Now()
			updateErr := s.projectRepo.Update(ctx, projectEntity)
			if updateErr != nil {
				logger.Error("Failed to update project record with Netlify Site ID after Netlify init",
					"ProjectID", targetProjectID, "NetlifySiteID", newNetlifySiteID, "Error", updateErr)
				// 记录错误，但核心流程成功，所以仍然返回 newNetlifySiteID
			} else {
				logger.Info("Successfully updated project record with Netlify Site ID", "ProjectID", targetProjectID, "NetlifySiteID", newNetlifySiteID)
			}
		}
	} else if s.projectRepo == nil {
		logger.Warn("ProjectRepository is not initialized, cannot update project record.", "ProjectID", targetProjectID, "NetlifySiteID", newNetlifySiteID)
	}

	logger.Info("Netlify site initialization process completed for project.",
		"ProjectID", targetProjectID, "NetlifySiteID", newNetlifySiteID, "GitHubRepoName", newRepoName)

	return newNetlifySiteID, nil
}

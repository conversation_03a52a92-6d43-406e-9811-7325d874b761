package workflow_service

import (
	"context"
	"fmt"
	"strings"

	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// DeleteProjectResult 表示删除项目的结果
type DeleteProjectResult struct {
	ProjectID       string                  `json:"project_id"`
	DeletionResults *ProjectDeletionResults `json:"deletion_results"`
	Errors          []string                `json:"errors"`
}

// ProjectDeletionResults 表示各个组件的删除结果
type ProjectDeletionResults struct {
	FlyAppDeleted        bool `json:"fly_app_deleted"`
	NetlifySiteDeleted   bool `json:"netlify_site_deleted"`
	ProjectRecordDeleted bool `json:"project_record_deleted"`
	ProjectStatusDeleted bool `json:"project_status_deleted"`
}

// DeleteProjectService 提供删除项目的应用服务
type DeleteProjectService struct {
	projectRepo       repository.ProjectRepository
	projectStatusRepo repository.ProjectStatusRepository
	flyService        *infraFly.Service
	netlifyService    *infraNetlify.Service
}

// NewDeleteProjectService 创建一个新的 DeleteProjectService 实例
func NewDeleteProjectService(
	projectRepo repository.ProjectRepository,
	projectStatusRepo repository.ProjectStatusRepository,
	flyService *infraFly.Service,
	netlifyService *infraNetlify.Service,
) *DeleteProjectService {
	return &DeleteProjectService{
		projectRepo:       projectRepo,
		projectStatusRepo: projectStatusRepo,
		flyService:        flyService,
		netlifyService:    netlifyService,
	}
}

// DeleteProject 删除指定的项目及其相关资源
// 删除顺序：1. Fly.io资源 2. Netlify站点 3. 数据库记录
func (s *DeleteProjectService) DeleteProject(ctx context.Context, projectID string) (*DeleteProjectResult, error) {
	logger.Info("Starting project deletion process", "ProjectID", projectID)

	result := &DeleteProjectResult{
		ProjectID: projectID,
		DeletionResults: &ProjectDeletionResults{
			FlyAppDeleted:        false,
			NetlifySiteDeleted:   false,
			ProjectRecordDeleted: false,
			ProjectStatusDeleted: false,
		},
		Errors: []string{},
	}

	// 1. 获取项目信息
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("Failed to get project for deletion", "ProjectID", projectID, "Error", err)
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to get project: %v", err))
		return result, fmt.Errorf("failed to get project %s: %w", projectID, err)
	}

	logger.Info("Retrieved project for deletion", "ProjectID", projectID, "Title", project.Title, "NetlifySiteID", project.NetlifySiteID)

	// 2. 删除 Fly.io 应用（如果存在）
	if err := s.deleteFlyApp(ctx, projectID, result); err != nil {
		logger.Error("Failed to delete Fly.io app", "ProjectID", projectID, "Error", err)
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to delete Fly.io app: %v", err))
	}

	// 3. 删除 Netlify 站点（如果存在）
	if project.NetlifySiteID != "" {
		if err := s.deleteNetlifySite(ctx, project.NetlifySiteID, result); err != nil {
			logger.Error("Failed to delete Netlify site", "ProjectID", projectID, "NetlifySiteID", project.NetlifySiteID, "Error", err)
			result.Errors = append(result.Errors, fmt.Sprintf("Failed to delete Netlify site: %v", err))
		}
	} else {
		logger.Info("No Netlify site ID found for project, skipping Netlify deletion", "ProjectID", projectID)
		// 没有Netlify站点ID，视为删除成功（因为目标状态是删除）
		result.DeletionResults.NetlifySiteDeleted = true
	}

	// 4. 删除项目状态记录
	if err := s.deleteProjectStatus(ctx, projectID, result); err != nil {
		logger.Error("Failed to delete project status", "ProjectID", projectID, "Error", err)
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to delete project status: %v", err))
	}

	// 5. 删除项目记录
	if err := s.deleteProjectRecord(ctx, projectID, result); err != nil {
		logger.Error("Failed to delete project record", "ProjectID", projectID, "Error", err)
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to delete project record: %v", err))
	}

	// 检查是否有任何操作成功
	if result.DeletionResults.FlyAppDeleted || result.DeletionResults.NetlifySiteDeleted ||
		result.DeletionResults.ProjectRecordDeleted || result.DeletionResults.ProjectStatusDeleted {
		logger.Info("Project deletion completed with partial success",
			"ProjectID", projectID,
			"FlyAppDeleted", result.DeletionResults.FlyAppDeleted,
			"NetlifySiteDeleted", result.DeletionResults.NetlifySiteDeleted,
			"ProjectRecordDeleted", result.DeletionResults.ProjectRecordDeleted,
			"ProjectStatusDeleted", result.DeletionResults.ProjectStatusDeleted)
	} else {
		logger.Error("Project deletion failed completely", "ProjectID", projectID, "Errors", result.Errors)
	}
	return result, nil
}

// deleteFlyApp 删除 Fly.io 应用及其相关资源
func (s *DeleteProjectService) deleteFlyApp(ctx context.Context, projectID string, result *DeleteProjectResult) error {
	// 使用项目ID作为应用名称（假设应用名称与项目ID相同）
	appName := projectID

	logger.Info("Starting Fly.io app deletion", "AppName", appName)

	// 1. 列出应用的所有机器
	machines, err := s.flyService.Machines.ListMachines(ctx, appName)
	if err != nil {
		// 检查是否是应用不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			logger.Info("Fly.io app not found, considering deletion successful", "AppName", appName)
			result.DeletionResults.FlyAppDeleted = true
			return nil
		}
		logger.Error("Failed to list machines for Fly.io app", "AppName", appName, "Error", err)
		return fmt.Errorf("failed to list machines for app %s: %w", appName, err)
	}

	logger.Info("Found machines for Fly.io app", "AppName", appName, "MachineCount", len(machines))

	// 2. 删除所有机器
	if len(machines) > 0 {
		for _, machine := range machines {
			if machine.Id == nil {
				logger.Warn("Skipping machine with nil ID", "AppName", appName)
				continue
			}
			logger.Info("Deleting Fly.io machine", "AppName", appName, "MachineID", *machine.Id)
			err := s.flyService.Machines.DeleteMachine(ctx, appName, *machine.Id, true)
			if err != nil {
				logger.Error("Failed to delete Fly.io machine", "AppName", appName, "MachineID", *machine.Id, "Error", err)
				// 继续删除其他机器，不中断流程
			} else {
				logger.Info("Successfully deleted Fly.io machine", "AppName", appName, "MachineID", *machine.Id)
			}
		}
	}

	// 3. 列出应用的所有卷
	volumes, err := s.flyService.Volumes.ListVolumes(ctx, appName, false)
	if err != nil {
		logger.Error("Failed to list volumes for Fly.io app", "AppName", appName, "Error", err)
		return fmt.Errorf("failed to list volumes for app %s: %w", appName, err)
	}

	logger.Info("Found volumes for Fly.io app", "AppName", appName, "VolumeCount", len(volumes))

	// 4. 删除所有卷
	if len(volumes) > 0 {
		for _, volume := range volumes {
			logger.Info("Deleting Fly.io volume", "AppName", appName, "VolumeID", volume.ID)
			err := s.flyService.Volumes.DeleteVolume(ctx, appName, volume.ID)
			if err != nil {
				logger.Error("Failed to delete Fly.io volume", "AppName", appName, "VolumeID", volume.ID, "Error", err)
				// 继续删除其他卷，不中断流程
			} else {
				logger.Info("Successfully deleted Fly.io volume", "AppName", appName, "VolumeID", volume.ID)
			}
		}
	}

	// 5. 删除应用
	logger.Info("Deleting Fly.io app", "AppName", appName)
	err = s.flyService.Apps.DeleteApp(ctx, appName)
	if err != nil {
		// 检查是否是应用不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			logger.Info("Fly.io app not found during deletion, considering deletion successful", "AppName", appName)
			result.DeletionResults.FlyAppDeleted = true
			return nil
		}
		logger.Error("Failed to delete Fly.io app", "AppName", appName, "Error", err)
		return fmt.Errorf("failed to delete Fly.io app %s: %w", appName, err)
	}

	logger.Info("Successfully deleted Fly.io app", "AppName", appName)
	result.DeletionResults.FlyAppDeleted = true
	return nil
}

// deleteNetlifySite 删除 Netlify 站点
func (s *DeleteProjectService) deleteNetlifySite(ctx context.Context, siteID string, result *DeleteProjectResult) error {
	logger.Info("Deleting Netlify site", "SiteID", siteID)

	err := s.netlifyService.Sites.DeleteSite(ctx, siteID)
	if err != nil {
		// 检查是否是站点不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			logger.Info("Netlify site not found, considering deletion successful", "SiteID", siteID)
			result.DeletionResults.NetlifySiteDeleted = true
			return nil
		}
		logger.Error("Failed to delete Netlify site", "SiteID", siteID, "Error", err)
		return fmt.Errorf("failed to delete Netlify site %s: %w", siteID, err)
	}

	logger.Info("Successfully deleted Netlify site", "SiteID", siteID)
	result.DeletionResults.NetlifySiteDeleted = true
	return nil
}

// deleteProjectStatus 删除项目状态记录
func (s *DeleteProjectService) deleteProjectStatus(ctx context.Context, projectID string, result *DeleteProjectResult) error {
	logger.Info("Deleting project status", "ProjectID", projectID)

	err := s.projectStatusRepo.DeleteByProjectID(ctx, projectID)
	if err != nil {
		// 检查是否是记录不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "no rows") {
			logger.Info("Project status not found, considering deletion successful", "ProjectID", projectID)
			result.DeletionResults.ProjectStatusDeleted = true
			return nil
		}
		logger.Error("Failed to delete project status", "ProjectID", projectID, "Error", err)
		return fmt.Errorf("failed to delete project status for project %s: %w", projectID, err)
	}

	logger.Info("Successfully deleted project status", "ProjectID", projectID)
	result.DeletionResults.ProjectStatusDeleted = true
	return nil
}

// deleteProjectRecord 删除项目记录
func (s *DeleteProjectService) deleteProjectRecord(ctx context.Context, projectID string, result *DeleteProjectResult) error {
	logger.Info("Deleting project record", "ProjectID", projectID)

	err := s.projectRepo.Delete(ctx, projectID)
	if err != nil {
		// 检查是否是记录不存在的错误
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "no rows") {
			logger.Info("Project record not found, considering deletion successful", "ProjectID", projectID)
			result.DeletionResults.ProjectRecordDeleted = true
			return nil
		}
		logger.Error("Failed to delete project record", "ProjectID", projectID, "Error", err)
		return fmt.Errorf("failed to delete project record %s: %w", projectID, err)
	}

	logger.Info("Successfully deleted project record", "ProjectID", projectID)
	result.DeletionResults.ProjectRecordDeleted = true
	return nil
}

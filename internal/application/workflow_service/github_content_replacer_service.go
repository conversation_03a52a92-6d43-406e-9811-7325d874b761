package workflow_service

import (
	"context"
	"fmt"
	"strings"

	"github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/fileutils"
)

// GitHubContentReplacerService 提供了替换GitHub仓库文件内容的应用服务
type GitHubContentReplacerService struct {
	fileContentService *github_service.FileContentService
	githubInfra        *infraGitHub.Service
	config             *config.Config
	codeCommitService  *github_service.CodeCommitService
}

// NewGitHubContentReplacerService 创建一个新的 GitHubContentReplacerService 实例
func NewGitHubContentReplacerService(
	fileContentService *github_service.FileContentService,
	githubInfra *infraGitHub.Service,
	cfg *config.Config,
	codeCommitService *github_service.CodeCommitService,
) *GitHubContentReplacerService {
	return &GitHubContentReplacerService{
		fileContentService: fileContentService,
		githubInfra:        githubInfra,
		config:             cfg,
		codeCommitService:  codeCommitService,
	}
}

// ReplaceProjectIDResult 项目ID替换结果
type ReplaceProjectIDResult struct {
	TotalFiles        int      `json:"total_files"`
	ProcessedFiles    int      `json:"processed_files"`
	ModifiedFiles     int      `json:"modified_files"`
	ModifiedFilesList []string `json:"modified_files_list"`
	CommitSHA         string   `json:"commit_sha"`
}

// ReplaceProjectIDInRepository 替换仓库中所有文件的项目ID
func (s *GitHubContentReplacerService) ReplaceProjectIDInRepository(
	ctx context.Context,
	repoName string,
	oldProjectID string,
	newProjectID string,
) (*ReplaceProjectIDResult, error) {
	// 1. 获取仓库中所有文件
	files, err := s.fileContentService.GetRepositoryFileContents(ctx, repoName)
	if err != nil {
		return nil, fmt.Errorf("获取仓库文件列表失败: %w", err)
	}

	// 2. 过滤需要处理的文本文件
	// textFiles := s.filterTextFiles(files)
	// 不进行过滤，直接处理所有文件
	textFiles := files

	// 3. 替换文件内容
	modifiedFiles, err := s.processFileReplacements(textFiles, oldProjectID, newProjectID)
	if err != nil {
		return nil, fmt.Errorf("处理文件替换失败: %w", err)
	}

	result := &ReplaceProjectIDResult{
		TotalFiles:        len(files),
		ProcessedFiles:    len(textFiles),
		ModifiedFiles:     len(modifiedFiles),
		ModifiedFilesList: s.getFileNames(modifiedFiles),
	}

	// 4. 如果有修改的文件，提交到GitHub
	if len(modifiedFiles) > 0 {
		commitSHA, err := s.commitChangesToGitHub(ctx, repoName, modifiedFiles, oldProjectID, newProjectID)
		if err != nil {
			return nil, fmt.Errorf("提交修改到GitHub失败: %w", err)
		}
		result.CommitSHA = commitSHA
	}

	return result, nil
}

// filterTextFiles 过滤出需要处理的文本文件
func (s *GitHubContentReplacerService) filterTextFiles(files []infraGitHub.File) []infraGitHub.File {
	var textFiles []infraGitHub.File

	// 需要处理的文件扩展名
	textExtensions := map[string]bool{
		".go": true, ".js": true, ".ts": true, ".jsx": true, ".tsx": true,
		".py": true, ".java": true, ".sql": true, ".json": true,
		".yaml": true, ".yml": true, ".xml": true, ".html": true,
		".css": true, ".md": true, ".txt": true, ".env": true,
	}

	// 需要跳过的文件
	skipFiles := map[string]bool{
		".git": true, "node_modules": true, ".gitignore": true,
		"go.sum": true, "package-lock.json": true,
	}

	for _, file := range files {
		if file.Binary || skipFiles[file.Name] {
			continue
		}

		needsProcessing := false
		for ext := range textExtensions {
			if strings.HasSuffix(strings.ToLower(file.Name), ext) {
				needsProcessing = true
				break
			}
		}

		if needsProcessing {
			textFiles = append(textFiles, file)
		}
	}

	return textFiles
}

// processFileReplacements 处理文件内容替换
func (s *GitHubContentReplacerService) processFileReplacements(
	files []infraGitHub.File,
	oldProjectID string,
	newProjectID string,
) ([]infraGitHub.File, error) {
	var modifiedFiles []infraGitHub.File

	// 创建替换规则
	//TODO: 这里需要未来统一一下是使用下划线还是横线
	replaceRules := []fileutils.ReplaceRule{
		{Old: oldProjectID, New: newProjectID},
		// 增加下划线形式的ID替换规则
		{Old: strings.ReplaceAll(oldProjectID, "-", "_"), New: strings.ReplaceAll(newProjectID, "-", "_")},
		// {Old: `"` + oldProjectID + `"`, New: `"` + newProjectID + `"`},
		// {Old: '`' + oldProjectID + '`', New: '`' + newProjectID + '`'},
	}

	for _, file := range files {
		originalContent := file.Contents
		processedContent := fileutils.ReplaceContentBatch(originalContent, replaceRules)

		if processedContent != originalContent {
			modifiedFile := file
			modifiedFile.Contents = processedContent
			modifiedFiles = append(modifiedFiles, modifiedFile)
		}
	}

	return modifiedFiles, nil
}

// getFileNames 提取文件名列表
func (s *GitHubContentReplacerService) getFileNames(files []infraGitHub.File) []string {
	var names []string
	for _, file := range files {
		names = append(names, file.Name)
	}
	return names
}

// commitChangesToGitHub 将修改提交到GitHub
func (s *GitHubContentReplacerService) commitChangesToGitHub(
	ctx context.Context,
	repoName string,
	modifiedFiles []infraGitHub.File,
	oldProjectID string,
	newProjectID string,
) (string, error) {
	// 构造提交文件内容
	var files []dto.FileContent
	for _, f := range modifiedFiles {
		files = append(files, dto.FileContent{
			Path:    f.Name, // infraGitHub.File 的 Name 字段为完整路径
			Content: f.Contents,
		})
	}

	commitMsg := fmt.Sprintf("Replace project ID %s with %s", oldProjectID, newProjectID)
	commitReq := &dto.CommitRequest{
		Owner:         s.config.GitHub.Owner,
		Repository:    repoName,
		Branch:        "main",
		CommitMessage: commitMsg,
		Files:         files,
	}

	commit, err := s.codeCommitService.CommitChanges(ctx, commitReq)
	if err != nil {
		return "", fmt.Errorf("提交修改到GitHub失败: %w", err)
	}
	return commit.GetSHA(), nil
}

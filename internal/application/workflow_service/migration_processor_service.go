package workflow_service

import (
	"path/filepath"
	"strings"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/fileutils"
)

// MigrationProcessorService 提供了基于文件列表检测迁移需求并进行文本替换的应用服务
type MigrationProcessorService struct{}

// NewMigrationProcessorService 创建一个新的 MigrationProcessorService 实例
func NewMigrationProcessorService() *MigrationProcessorService {
	return &MigrationProcessorService{}
}

// ProcessResult 处理结果
type ProcessResult struct {
	NeedsUpdate      bool   `json:"needs_update"`      // 是否需要更新
	ProcessedContent string `json:"processed_content"` // 处理后的文本内容
}

// ProcessContentWithMigrationCheck 基于文件列表检测迁移需求并处理文本内容
func (s *MigrationProcessorService) ProcessContentWithMigrationCheck(
	fileNames []string,
	contentToProcess string,
	replacementRules map[bool][]fileutils.ReplaceRule,
) (*ProcessResult, error) {
	// 检测是否需要迁移
	needsMigration := s.detectMigrationNeed(fileNames)

	// 根据检测结果进行文本替换
	processedContent := s.processContentBasedOnMigrationNeed(
		contentToProcess,
		needsMigration,
		replacementRules,
	)

	return &ProcessResult{
		NeedsUpdate:      needsMigration,
		ProcessedContent: processedContent,
	}, nil
}

// DetectMigrationNeedOnly 仅检测是否需要迁移，不进行文本处理
func (s *MigrationProcessorService) DetectMigrationNeedOnly(fileNames []string) (bool, []string) {
	var migrationFiles []string

	for _, fileName := range fileNames {
		if IsMigrationFile(fileName) {
			migrationFiles = append(migrationFiles, fileName)
		}
	}

	needsMigration := len(migrationFiles) > 0
	return needsMigration, migrationFiles
}

// detectMigrationNeed 检测是否需要迁移
func (s *MigrationProcessorService) detectMigrationNeed(fileNames []string) bool {
	for _, fileName := range fileNames {
		if IsMigrationFile(fileName) {
			return true
		}
	}
	return false
}

// processContentBasedOnMigrationNeed 根据迁移需求处理文本内容
func (s *MigrationProcessorService) processContentBasedOnMigrationNeed(
	content string,
	needsMigration bool,
	replacementRules map[bool][]fileutils.ReplaceRule,
) string {
	// 获取对应的替换规则
	rules, exists := replacementRules[needsMigration]
	if !exists || len(rules) == 0 {
		return content
	}

	// 执行批量替换
	processedContent := fileutils.ReplaceContentBatch(content, rules)
	return processedContent
}

// IsMigrationFile 判断文件路径是否为迁移文件
func IsMigrationFile(filePath string) bool {
	// 标准化路径分隔符
	normalizedPath := filepath.ToSlash(filePath)

	// 检查是否在migration目录下（支持migrations和migration两种命名）
	if !strings.Contains(normalizedPath, "migration") {
		return false
	}

	// 检查是否为.sql文件
	if !strings.HasSuffix(strings.ToLower(normalizedPath), ".sql") {
		return false
	}

	// 确保是在migration相关目录下的文件
	parts := strings.Split(normalizedPath, "/")
	for i, part := range parts {
		// 支持 migration, migrations 等命名方式
		if strings.Contains(strings.ToLower(part), "migration") {
			// 检查migration目录后面是否还有路径部分且最后是.sql文件
			if i < len(parts)-1 && strings.HasSuffix(strings.ToLower(parts[len(parts)-1]), ".sql") {
				return true
			}
		}
	}

	return false
}

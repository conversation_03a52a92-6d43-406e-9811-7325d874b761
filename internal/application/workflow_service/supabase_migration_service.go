package workflow_service

import (
	"context"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/edge_functions"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// SupabaseMigrationService 提供了调用Supabase Edge Function执行迁移的应用服务
type SupabaseMigrationService struct {
	config             *config.Config
	edgeFuncService    *edge_functions.Service
	contentReplacerSvc *GitHubContentReplacerService
}

// NewSupabaseMigrationService 创建一个新的 SupabaseMigrationService 实例
func NewSupabaseMigrationService(cfg *config.Config, edgeFuncService *edge_functions.Service, contentReplacerSvc *GitHubContentReplacerService) *SupabaseMigrationService {
	return &SupabaseMigrationService{
		config:             cfg,
		edgeFuncService:    edgeFuncService,
		contentReplacerSvc: contentReplacerSvc,
	}
}

// MigrationRequest Edge Function请求体
type MigrationRequest struct {
	Name      string `json:"name"`
	ProjectID string `json:"project_id"`
	RepoName  string `json:"repo_name"`
}

// MigrationResponse Edge Function响应体
type MigrationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

// ExecuteMigrationAsync 异步调用Supabase Edge Function执行迁移
func (s *SupabaseMigrationService) ExecuteMigrationAsync(
	ctx context.Context,
	sqlFiles []infraGitHub.File,
	oldProjectID string,
	newProjectID string,
) {
	// 异步任务必须使用新的 context.Background()，避免主流程ctx被取消
	go func() {
		ctx = context.Background()
		logger.Info("ExecuteMigrationAsync")
		_ = s.ExecuteMigration(ctx, sqlFiles, oldProjectID, newProjectID)
	}()
}

// ExecuteMigration 同步调用Supabase Edge Function执行迁移
func (s *SupabaseMigrationService) ExecuteMigration(
	ctx context.Context,
	sqlFiles []infraGitHub.File,
	oldProjectID string,
	newProjectID string,
) error {
	// 记录迁移开始日志
	fileNames := make([]string, 0, len(sqlFiles))
	for _, f := range sqlFiles {
		fileNames = append(fileNames, f.Name)
	}
	logger.Info("Start migration", "OldProjectID", oldProjectID, "NewProjectID", newProjectID, "FileCount", len(sqlFiles), "FileNames", fileNames)

	// 1. 替换 SQL 文件内容
	logger.Debug("Start processFileReplacements", "FileCount", len(sqlFiles))
	modifiedFiles, err := s.contentReplacerSvc.processFileReplacements(sqlFiles, oldProjectID, newProjectID)
	if err != nil {
		logger.Error("processFileReplacements failed", "Error", err)
		return fmt.Errorf("processFileReplacements failed: %w", err)
	}
	logger.Debug("processFileReplacements finished", "ModifiedFileCount", len(modifiedFiles))

	// 2. 依次执行 SQL
	for _, file := range modifiedFiles {
		if file.Contents == "" {
			logger.Debug("Skip empty file", "FileName", file.Name)
			continue
		}
		logger.Info("Start migration for file", "FileName", file.Name)
		ok, err := s.edgeFuncService.Migration.CallMigrationWbFree(ctx, file.Contents)
		if err != nil || !ok {
			logger.Error("migration failed", "FileName", file.Name, "Error", err)
			return fmt.Errorf("migration failed: %w", err)
		}
		logger.Info("Migration success for file", "FileName", file.Name)
	}
	logger.Info("All migrations completed successfully", "FileCount", len(modifiedFiles))
	return nil
}

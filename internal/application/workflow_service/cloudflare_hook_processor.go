package workflow_service

import (
	"context"
	"fmt"
	"time"

	projectStatusRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// CloudflareDeployEvent 表示 Cloudflare Pages 部署事件的枚举。
type CloudflareDeployEvent string

// 定义 Cloudflare Pages 可能的部署事件为常量。
const (
	CloudflareDeployEventStarted CloudflareDeployEvent = "EVENT_DEPLOYMENT_STARTED"
	CloudflareDeployEventSuccess CloudflareDeployEvent = "EVENT_DEPLOYMENT_SUCCESS"
	CloudflareDeployEventFailure CloudflareDeployEvent = "EVENT_DEPLOYMENT_FAILURE"
)

// CloudflareDeployData 表示 Cloudflare Pages webhook 中的 data 字段。
type CloudflareDeployData struct {
	AccountTag     string    `json:"account_tag"`
	AlertName      string    `json:"alert_name"`
	BranchAliasURL string    `json:"branch_alias_url"`
	CommitHash     string    `json:"commit_hash"`
	CustomDomain   string    `json:"custom_domain"`
	DeploymentID   string    `json:"deployment_id"`
	Environment    string    `json:"environment"`
	Event          string    `json:"event"`
	PagesDevURL    string    `json:"pages_dev_url"`
	PreviewURL     string    `json:"preview_url"`
	ProjectID      string    `json:"project_id"`
	ProjectName    string    `json:"project_name"`
	Timestamp      time.Time `json:"timestamp"`
}

// CloudflareDetailedDeploy 表示 Cloudflare Pages 部署的详细模型。
type CloudflareDetailedDeploy struct {
	Name       string               `json:"name"`
	Text       string               `json:"text"`
	Data       CloudflareDeployData `json:"data"`
	Ts         int64                `json:"ts"`
	AccountID  string               `json:"account_id"`
	PolicyID   string               `json:"policy_id"`
	PolicyName string               `json:"policy_name"`
	AlertType  string               `json:"alert_type"`
}

// CloudflareHookProcessorService 处理来自 Cloudflare Pages 的 webhook。
type CloudflareHookProcessorService struct {
	statusRepo projectStatusRepo.ProjectStatusRepository
}

// NewCloudflareHookProcessorService 创建一个新的 CloudflareHookProcessorService 实例。
func NewCloudflareHookProcessorService(
	psr projectStatusRepo.ProjectStatusRepository,
) *CloudflareHookProcessorService {
	return &CloudflareHookProcessorService{
		statusRepo: psr,
	}
}

// ProcessDeployHook 处理与 Cloudflare Pages 部署相关的 webhook 负载。
func (s *CloudflareHookProcessorService) ProcessDeployHook(ctx context.Context, payload *CloudflareDetailedDeploy) error {
	projectID := payload.Data.ProjectName
	deploymentID := payload.Data.DeploymentID
	event := payload.Data.Event

	logger.Info("Processing Cloudflare Pages Webhook",
		"ProjectID", projectID,
		"DeploymentID", deploymentID,
		"Event", event,
	)

	// 获取现有的 ProjectStatus 实体
	existingProjectStatus, err := s.statusRepo.GetByProjectID(ctx, projectID)
	if err != nil {
		logger.Error("Failed to get project status by Project ID for webhook processing", "Error", err, "ProjectID", projectID)
		return fmt.Errorf("failed to get project status by Project ID %s: %w", projectID, err)
	}
	if existingProjectStatus == nil {
		logger.Error("No project status found for Project ID", "ProjectID", projectID)
		return fmt.Errorf("no project status found for Project ID %s", projectID)
	}

	updates := make(map[string]any)
	var internalStatus string

	switch event {
	case string(CloudflareDeployEventSuccess):
		internalStatus = "done"
		updates["status"] = internalStatus
		updates["error_msg"] = ""
		updates["error_code"] = ""
		updates["preview_link"] = payload.Data.PagesDevURL

	case string(CloudflareDeployEventFailure):
		internalStatus = "done"
		updates["status"] = internalStatus
		updates["error_code"] = "CLOUDFLARE_DEPLOY_ERROR"
		updates["error_msg"] = payload.Text // 使用 webhook 的 text 字段作为错误信息
		updates["preview_link"] = payload.Data.PagesDevURL

	default:
		logger.Warn("Received unknown or unhandled Cloudflare Pages deploy event", "Event", event, "ProjectID", projectID)
		return nil
	}

	// 检查 updates map 是否为空
	if len(updates) == 0 {
		logger.Info("No updates to apply for project status via Cloudflare Pages Webhook", "ProjectID", projectID, "Event", event)
		return nil
	}

	// 更新项目状态到数据库
	logger.Info("Preparing to update project status in DB", "ProjectID", projectID, "Updates", updates)
	updatedProjectStatus, err := s.statusRepo.UpdateByProjectID(ctx, projectID, updates)
	if err != nil {
		logger.Error("Failed to update project status via Cloudflare Pages Webhook", "Error", err, "ProjectID", projectID)
		return err
	}

	logger.Info("Successfully updated project status in DB", "ProjectID", updatedProjectStatus.ProjectID, "NewStatus", updatedProjectStatus.Status)
	return nil
}

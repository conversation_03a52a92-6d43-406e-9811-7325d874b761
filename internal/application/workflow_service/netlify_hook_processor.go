package workflow_service

import (
	"context"
	"fmt"
	"strings"

	// 假设 ProjectStatus 实体将定义在这里或其子包
	projectStatusRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository" // 假设 ProjectStatusRepository 接口定义在这里
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	// 下面是项目依赖的包，请根据实际情况取消注释或修改
	// "github.com/web-builder-dev/be-web-builder/internal/domain/project" // 用于 ProjectStatus
	// "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify" // 用于 SiteInfo
)

// NetlifyDeployState 表示 Netlify 部署状态的枚举。
type NetlifyDeployState string

// 定义 Netlify 可能的部署状态为常量。
const (
	NetlifyDeployStateNew           NetlifyDeployState = "new"
	NetlifyDeployStatePendingReview NetlifyDeployState = "pending_review"
	NetlifyDeployStateAccepted      NetlifyDeployState = "accepted"
	NetlifyDeployStateRejected      NetlifyDeployState = "rejected"
	NetlifyDeployStateEnqueued      NetlifyDeployState = "enqueued"
	NetlifyDeployStateBuilding      NetlifyDeployState = "building"
	NetlifyDeployStateUploading     NetlifyDeployState = "uploading"
	NetlifyDeployStateUploaded      NetlifyDeployState = "uploaded"
	NetlifyDeployStatePreparing     NetlifyDeployState = "preparing"
	NetlifyDeployStatePrepared      NetlifyDeployState = "prepared"
	NetlifyDeployStateProcessing    NetlifyDeployState = "processing"
	NetlifyDeployStateProcessed     NetlifyDeployState = "processed"
	NetlifyDeployStateReady         NetlifyDeployState = "ready"
	NetlifyDeployStateError         NetlifyDeployState = "error"
	NetlifyDeployStateRetrying      NetlifyDeployState = "retrying"
)

// NetlifyDeployLinks 表示 Webhook 负载中 links 对象的模型。
type NetlifyDeployLinks struct {
	Permalink *string `json:"permalink,omitempty"`
	Alias     *string `json:"alias,omitempty"`
	Branch    *string `json:"branch,omitempty"`
}

// NetlifyDetailedDeploy 表示 Netlify 部署的详细模型。
// 根据提供的 JSON 示例调整字段类型以更好地处理 null 值。
type NetlifyDetailedDeploy struct {
	ID                      string              `json:"id"`
	SiteID                  string              `json:"site_id"`
	BuildID                 string              `json:"build_id"`
	State                   NetlifyDeployState  `json:"state"`
	Name                    *string             `json:"name,omitempty"`
	URL                     *string             `json:"url,omitempty"`
	SSLURL                  *string             `json:"ssl_url,omitempty"`
	AdminURL                *string             `json:"admin_url,omitempty"`
	DeployURL               *string             `json:"deploy_url,omitempty"`
	DeploySSLURL            *string             `json:"deploy_ssl_url,omitempty"`
	CreatedAt               *string             `json:"created_at,omitempty"`
	UpdatedAt               *string             `json:"updated_at,omitempty"`
	UserID                  *string             `json:"user_id,omitempty"`
	ErrorMessage            *string             `json:"error_message,omitempty"`
	Required                []any               `json:"required,omitempty"`
	RequiredFunctions       *[]any              `json:"requiredFunctions,omitempty"`
	CommitRef               *string             `json:"commit_ref,omitempty"`
	ReviewID                any                 `json:"review_id,omitempty"`
	Branch                  *string             `json:"branch,omitempty"`
	CommitURL               *string             `json:"commit_url,omitempty"`
	Skipped                 *bool               `json:"skipped,omitempty"`
	Locked                  *bool               `json:"locked,omitempty"`
	Title                   *string             `json:"title,omitempty"`
	CommitMessage           *string             `json:"commit_message,omitempty"`
	ReviewURL               *string             `json:"review_url,omitempty"`
	PublishedAt             *string             `json:"published_at,omitempty"`
	Context                 *string             `json:"context,omitempty"`
	DeployTime              *int                `json:"deploy_time,omitempty"`
	AvailableFunctions      []any               `json:"available_functions,omitempty"`
	ScreenshotURL           *string             `json:"screenshot_url,omitempty"`
	Committer               *string             `json:"committer,omitempty"`
	SkippedLog              *string             `json:"skipped_log,omitempty"`
	ManualDeploy            *bool               `json:"manual_deploy,omitempty"`
	PluginState             *string             `json:"plugin_state,omitempty"`
	LighthousePluginScores  map[string]any      `json:"lighthouse_plugin_scores,omitempty"`
	Links                   *NetlifyDeployLinks `json:"links,omitempty"`
	Framework               *string             `json:"framework,omitempty"`
	EntryPath               *string             `json:"entry_path,omitempty"`
	ViewsCount              *int                `json:"views_count,omitempty"`
	FunctionSchedules       []any               `json:"function_schedules,omitempty"`
	PublicRepo              *bool               `json:"public_repo,omitempty"`
	PendingReviewReason     *string             `json:"pending_review_reason,omitempty"`
	Lighthouse              map[string]any      `json:"lighthouse,omitempty"`
	EdgeFunctionsPresent    *bool               `json:"edge_functions_present,omitempty"`
	ExpiresAt               *string             `json:"expires_at,omitempty"`
	BlobsRegion             *string             `json:"blobs_region,omitempty"`
	DeployValidationsReport map[string]any      `json:"deploy_validations_report,omitempty"`
}

// 依赖项及其结构的占位符:
// 这些应该被相应包中的实际定义替换。

// ProjectStatus 表示项目状态的简化结构。
// 请将其替换为 domain/project 中实际的 ProjectStatus 结构。
type ProjectStatus struct {
	ProjectID    string
	Status       string
	PublishLink  *string // 已发布站点的链接
	PreviewLink  *string // 预览链接
	ErrorCode    *string
	ErrorMessage *string
	// ... 其他字段
}

// NetlifySiteInfo 表示 Netlify 站点信息的简化结构。
// 请将其替换为 netlify infrastructure client 中实际的 Site (或 SiteInfo) 结构。
type NetlifySiteInfo struct {
	ID            string
	Name          string
	ScreenshotURL *string
	// ... 其他字段
}

// ProjectStatusService 定义了与项目状态交互的接口。
type ProjectStatusService interface {
	SelectStatusByNetlifySiteID(ctx context.Context, netlifySiteID string) (*ProjectStatus, error)
	UpdateStatusByNetlifySiteID(ctx context.Context, netlifySiteID string, updateData map[string]any) (*ProjectStatus, error)
}

// NetlifyAdapterService 定义了与 Netlify API 交互的接口。
type NetlifyAdapterService interface {
	GetSite(ctx context.Context, siteID string) (*NetlifySiteInfo, error)
	StreamBuildLogs(ctx context.Context, deployID, siteID string) ([]map[string]any, error)
}

// NetlifyHookProcessorService 处理来自 Netlify 的 webhook。
// 现在依赖 repository.ProjectStatusRepository 和 infraNetlify.Service。
type NetlifyHookProcessorService struct {
	statusRepo        projectStatusRepo.ProjectStatusRepository // 使用 ProjectStatusRepository
	netlifyInfra      *infraNetlify.Service
	screenshotService *ScreenshotService // 新增截图服务
}

// NewNetlifyHookProcessorService 创建一个新的 NetlifyHookProcessorService 实例。
func NewNetlifyHookProcessorService(
	psr projectStatusRepo.ProjectStatusRepository, // 接收 ProjectStatusRepository
	ni *infraNetlify.Service,
	screenshotSvc *ScreenshotService, // 新增参数
) *NetlifyHookProcessorService {
	return &NetlifyHookProcessorService{
		statusRepo:        psr,
		netlifyInfra:      ni,
		screenshotService: screenshotSvc,
	}
}

// ProcessDeployHook 处理与 Netlify 部署相关的 webhook 负载。
func (s *NetlifyHookProcessorService) ProcessDeployHook(ctx context.Context, payload *NetlifyDetailedDeploy) error {
	netlifySiteID := payload.SiteID
	netlifyState := payload.State
	deployID := payload.ID
	var screenshotURLStr string
	if payload.ScreenshotURL != nil {
		screenshotURLStr = *payload.ScreenshotURL
	}

	logger.Info("Processing Netlify Webhook",
		"SiteID", netlifySiteID,
		"DeployID", deployID,
		"State", netlifyState,
		"ScreenshotURL", screenshotURLStr,
	)

	// 获取现有的 ProjectStatus 实体，我们仍然需要它来获取 ProjectID
	// 并且某些逻辑（如判断 PublishLink 是否已存在）可能依赖于当前状态
	existingProjectStatus, err := s.statusRepo.GetByNetlifySiteID(ctx, netlifySiteID)
	if err != nil {
		logger.Error("Failed to get project status by Netlify Site ID for webhook processing", "Error", err, "NetlifySiteID", netlifySiteID)
		return fmt.Errorf("failed to get project status by Netlify Site ID %s: %w", netlifySiteID, err)
	}
	if existingProjectStatus == nil { // 防御性检查，尽管GetByNetlifySiteID在找不到时应该返回错误
		logger.Error("No project status found for Netlify Site ID", "NetlifySiteID", netlifySiteID)
		return fmt.Errorf("no project status found for Netlify Site ID %s", netlifySiteID)
	}

	updates := make(map[string]any)
	var internalStatus string

	switch netlifyState {
	case NetlifyDeployStateBuilding:
		internalStatus = "in_progress"
		updates["status"] = internalStatus
	case NetlifyDeployStateReady:
		internalStatus = "done"
		updates["status"] = internalStatus
		updates["error_msg"] = ""  // 使用 ErrorMsg 字段名，与用户修改一致
		updates["error_code"] = "" // 如果有 ErrorCode 字段

		if payload.Context != nil && *payload.Context == "production" {
			if payload.SSLURL != nil {
				updates["publish_link"] = *payload.SSLURL
			}
			if payload.Links != nil && payload.Links.Permalink != nil {
				updates["preview_link"] = *payload.Links.Permalink
			}
		} else {
			if payload.Links != nil && payload.Links.Permalink != nil {
				updates["preview_link"] = *payload.Links.Permalink
			}
		}

		// 异步获取截图并更新site_thumbnail
		s.screenshotService.AsyncUpdateSiteThumbnailByScreenshot(ctx, netlifySiteID, *payload.Links.Permalink, s.statusRepo)

	case NetlifyDeployStateError:
		internalStatus = "done"
		updates["status"] = internalStatus
		updates["error_code"] = "NETLIFY_DEPLOY_ERROR" // 如果有 ErrorCode 字段

		logger.Info("Deployment failed, attempting to stream build logs", "DeployID", deployID, "SiteID", netlifySiteID)
		logs, logErr := s.netlifyInfra.Builds.StreamBuildLogs(ctx, deployID, netlifySiteID)
		if logErr != nil {
			logger.Error("Failed to stream build logs for failed deployment", "Error", logErr, "DeployID", deployID, "SiteID", netlifySiteID)
			updates["error_msg"] = "Netlify deployment failed and failed to retrieve logs."
		} else {
			var messages []string
			for _, logEntry := range logs {
				if msg, ok := logEntry["message"].(string); ok {
					messages = append(messages, msg)
				}
			}
			if len(messages) > 0 {
				updates["error_msg"] = strings.Join(messages, "\n")
			} else {
				updates["error_msg"] = "Netlify deployment failed. No specific messages found in logs."
			}
		}
		if payload.Links != nil && payload.Links.Permalink != nil {
			updates["preview_link"] = *payload.Links.Permalink
		}

	default:
		logger.Warn("Received unknown or unhandled Netlify deploy state", "State", netlifyState, "SiteID", netlifySiteID)
		return nil
	}

	// 检查 updates map 是否为空，如果为空则无需更新
	if len(updates) == 0 {
		logger.Info("No updates to apply for project status via Netlify Webhook", "NetlifySiteID", netlifySiteID, "State", netlifyState)
		return nil
	}

	// 更新项目状态到数据库
	logger.Info("Preparing to update project status in DB using UpdateByNetlifySiteID", "NetlifySiteID", netlifySiteID, "ProjectID", existingProjectStatus.ProjectID, "Updates", updates)
	updatedProjectStatus, err := s.statusRepo.UpdateByNetlifySiteID(ctx, netlifySiteID, updates)
	if err != nil {
		logger.Error("Failed to update project status via Netlify Webhook using UpdateByNetlifySiteID", "Error", err, "NetlifySiteID", netlifySiteID, "ProjectID", existingProjectStatus.ProjectID)
		return nil
	}

	logger.Info("Successfully updated project status in DB", "ProjectID", updatedProjectStatus.ProjectID, "NewStatus", updatedProjectStatus.Status, "NetlifySiteID", netlifySiteID)
	return nil
}

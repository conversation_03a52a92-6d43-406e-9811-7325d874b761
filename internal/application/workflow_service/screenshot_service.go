package workflow_service

import (
	"context"
	"time"

	projectStatusRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/screenshot"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// ScreenshotService 网页截图服务
type ScreenshotService struct {
	client *screenshot.Client
}

// NewScreenshotService 创建网页截图服务
// endpoint: 截图API地址
// 返回: ScreenshotService实例
func NewScreenshotService(endpoint string) *ScreenshotService {
	return &ScreenshotService{
		client: screenshot.NewClient(endpoint),
	}
}

// AsyncUpdateSiteThumbnailByScreenshot 异步获取网页截图img_url并更新数据库site_thumbnail字段
// ctx: 上下文
// netlifySiteID: Netlify站点ID
// siteURL: 需要截图的网页地址
// statusRepo: 项目状态仓储接口，需实现UpdateByNetlifySiteID
func (s *ScreenshotService) AsyncUpdateSiteThumbnailByScreenshot(ctx context.Context, netlifySiteID string, siteURL string, statusRepo projectStatusRepo.ProjectStatusRepository) {
	go func() {
		asyncCtx := context.Background()
		imgURL, err := s.client.Screenshot(siteURL)
		if err != nil {
			// 只记录日志，不影响主流程
			logger.Warn("Failed to get screenshot", "Error", err, "SiteID", netlifySiteID, "URL", siteURL)
			return
		}
		updates := map[string]any{"site_thumbnail": imgURL}
		_, updateErr := statusRepo.UpdateByNetlifySiteID(asyncCtx, netlifySiteID, updates)
		if updateErr != nil {
			logger.Warn("Failed to update site_thumbnail after screenshot", "Error", updateErr, "SiteID", netlifySiteID)
			return
		}
		logger.Info("Successfully updated site_thumbnail after screenshot", "SiteID", netlifySiteID, "ImgURL", imgURL)
	}()
}

// AsyncUpdateSiteThumbnailByScreenshotWithProjectID 异步获取网页截图img_url并更新数据库site_thumbnail字段
// ctx: 上下文
// projectID: 项目ID
// siteURL: 需要截图的网页地址
// statusRepo: 项目状态仓储接口，需实现UpdateByProjectID
func (s *ScreenshotService) AsyncUpdateSiteThumbnailByScreenshotWithProjectID(ctx context.Context, projectID string, siteURL string, statusRepo projectStatusRepo.ProjectStatusRepository) {
	go func() {
		asyncCtx := context.Background()
		//增加随机等待时间，避免站点未部署完成
		time.Sleep(10 * time.Second)
		imgURL, err := s.client.Screenshot(siteURL)
		if err != nil {
			// 只记录日志，不影响主流程
			logger.Warn("Failed to get screenshot", "Error", err, "ProjectID", projectID, "URL", siteURL)
			return
		}
		updates := map[string]any{"site_thumbnail": imgURL}
		_, updateErr := statusRepo.UpdateByProjectID(asyncCtx, projectID, updates)
		if updateErr != nil {
			logger.Warn("Failed to update site_thumbnail after screenshot", "Error", updateErr, "ProjectID", projectID)
			return
		}
		logger.Info("Successfully updated site_thumbnail after screenshot", "ProjectID", projectID, "ImgURL", imgURL)
	}()
}

package workflow_service

import (
	"context"
	"fmt"
	"time"

	agentSvc "github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/netlify_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	agentEntity "github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	projectRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	// DefaultPollInterval 轮询部署状态的默认间隔时间。
	DefaultPollInterval = 10 * time.Second
	// DefaultPollTimeout 轮询部署状态的默认总超时时间。
	DefaultPollTimeout = 5 * time.Minute // 5 分钟超时
)

// PublishService 提供了发布 Netlify 站点的工作流。
type PublishService struct {
	netlifyInfra    *infraNetlify.Service
	statusRepo      projectRepo.ProjectStatusRepository
	projectRepo     projectRepo.ProjectRepository
	agentManagerSvc *agentSvc.AgentManagerService
	initSiteService *netlify_service.InitNetlifySiteService
	workerKVService *cloudflare_service.WorkerKVService
	cfg             *config.Config
}

// NewPublishService 创建一个新的 PublishService 实例。
func NewPublishService(
	netlifyInfra *infraNetlify.Service,
	statusRepo projectRepo.ProjectStatusRepository,
	projectRepo projectRepo.ProjectRepository,
	agentManagerSvc *agentSvc.AgentManagerService,
	initSiteService *netlify_service.InitNetlifySiteService,
	workerKVService *cloudflare_service.WorkerKVService,
	cfg *config.Config,
) *PublishService {
	return &PublishService{
		netlifyInfra:    netlifyInfra,
		statusRepo:      statusRepo,
		projectRepo:     projectRepo,
		agentManagerSvc: agentManagerSvc,
		initSiteService: initSiteService,
		workerKVService: workerKVService,
		cfg:             cfg,
	}
}

// findLatestLockedProductionDeployID 查找指定站点最新锁定的生产部署ID。
// siteID: Netlify 站点 ID。
// 返回找到的部署 ID，如果未找到则返回空字符串和nil错误，如果查找过程出错则返回错误。
func (s *PublishService) findLatestLockedProductionDeployID(ctx context.Context, siteID string) (string, error) {
	logger.Debug("Finding latest locked production deploy", "SiteID", siteID)
	// Use netlifyInfra.Deploys for DeployService methods
	deploysList, err := s.netlifyInfra.Deploys.ListSiteDeploys(ctx, siteID, &infraNetlify.ListSiteDeploysOptions{PerPage: 10})
	if err != nil {
		logger.Error("Failed to list site deploys", "SiteID", siteID, "Error", err)
		return "", fmt.Errorf("listing site deploys for site %s failed: %w", siteID, err)
	}

	// ListSiteDeploys returns []infraNetlify.Deploy
	for _, deploy := range deploysList { // infraNetlify.Deploy has Context, Locked, ID fields
		if deploy.Context == "production" && deploy.Locked {
			logger.Info("Found locked production deploy", "SiteID", siteID, "DeployID", deploy.ID)
			return deploy.ID, nil
		}
	}

	logger.Info("No locked production deploy found", "SiteID", siteID)
	return "", nil
}

// tryRelockPreviousDeploy 尝试重新锁定先前解锁的部署ID（如果存在）。
// 此操作的失败不应中断主流程，仅记录错误。
func (s *PublishService) tryRelockPreviousDeploy(ctx context.Context, deployID string) {
	if deployID == "" {
		logger.Info("No previous deploy ID to re-lock.")
		return
	}

	logger.Info("Attempting to re-lock previous deploy", "DeployID", deployID)
	// Use netlifyInfra.Deploys for DeployService methods
	_, err := s.netlifyInfra.Deploys.LockDeploy(ctx, deployID)
	if err != nil {
		logger.Error("Failed to re-lock previous deploy", "DeployID", deployID, "Error", err)
	} else {
		logger.Info("Successfully re-locked previous deploy", "DeployID", deployID)
	}
}

// PublishWorkflow 执行发布工作流：解锁旧部署 -> 触发构建 -> 轮询状态 -> 锁定新部署。
// netlifySiteID: Netlify 站点的 ID。
// 现在使用包内定义的 DefaultPollInterval 和 DefaultPollTimeout 常量。
// 返回: 如果部署成功，返回 nil。如果部署失败或发生其他错误，返回包含错误信息的 error 对象。
func (s *PublishService) PublishWorkflow(ctx context.Context, netlifySiteID string) error {
	logger.Info("Starting publish workflow", "NetlifySiteID", netlifySiteID)

	// 1. 查找并解锁当前锁定的生产部署
	lockedDeployID, err := s.findLatestLockedProductionDeployID(ctx, netlifySiteID)
	if err != nil {
		return fmt.Errorf("failed to find latest locked production deploy for site %s: %w", netlifySiteID, err)
	}

	if lockedDeployID != "" {
		logger.Info("Attempting to unlock old deploy", "DeployID", lockedDeployID)
		// Use netlifyInfra.Deploys for DeployService methods
		_, err = s.netlifyInfra.Deploys.UnlockDeploy(ctx, lockedDeployID)
		if err != nil {
			logger.Error("Failed to unlock old deploy, but continuing with build trigger", "DeployID", lockedDeployID, "Error", err)
		} else {
			logger.Info("Successfully unlocked old deploy", "DeployID", lockedDeployID)
		}
	} else {
		logger.Info("No locked deploy found to unlock, skipping unlock step.", "SiteID", netlifySiteID)
	}

	logger.Info("Triggering new site build", "SiteID", netlifySiteID)
	// Use netlifyInfra.Builds for BuildService methods. Corrected to remove third argument.
	buildInfo, err := s.netlifyInfra.Builds.CreateSiteBuild(ctx, netlifySiteID)
	if err != nil {
		logger.Error("Failed to trigger new site build", "SiteID", netlifySiteID, "Error", err)
		s.tryRelockPreviousDeploy(ctx, lockedDeployID)
		return fmt.Errorf("triggering new site build for site %s failed: %w", netlifySiteID, err)
	}

	newDeployID := buildInfo.DeployID
	newBuildID := buildInfo.ID
	if newDeployID == "" {
		logger.Error("Failed to get associated deploy_id after triggering site build.", "SiteID", netlifySiteID, "BuildID", newBuildID)
		s.tryRelockPreviousDeploy(ctx, lockedDeployID)
		return fmt.Errorf("could not retrieve deploy ID after triggering site build for site %s (BuildID: %s)", netlifySiteID, newBuildID)
	}
	logger.Info("New site build triggered successfully", "SiteID", netlifySiteID, "BuildID", newBuildID, "NewDeployID", newDeployID)

	logger.Info("Starting to poll deploy status", "DeployID", newDeployID, "PollInterval", DefaultPollInterval.String(), "PollTimeout", DefaultPollTimeout.String())
	pollingCtx, cancelPolling := context.WithTimeout(ctx, DefaultPollTimeout)
	defer cancelPolling()

	ticker := time.NewTicker(DefaultPollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pollingCtx.Done():
			logger.Error("Polling deploy status timed out", "DeployID", newDeployID, "Timeout", DefaultPollTimeout.String())
			s.tryRelockPreviousDeploy(ctx, lockedDeployID)
			return fmt.Errorf("polling deploy status for %s timed out after %s", newDeployID, DefaultPollTimeout.String())
		case <-ticker.C:
			// Use netlifyInfra.Deploys for DeployService methods
			deployStatus, err := s.netlifyInfra.Deploys.GetDeploy(ctx, newDeployID)
			if err != nil {
				logger.Error("Error getting deploy status during polling", "DeployID", newDeployID, "Error", err)
				continue
			}

			logger.Debug("Current deploy status", "DeployID", newDeployID, "State", deployStatus.State)
			currentNetlifyState := NetlifyDeployState(deployStatus.State)

			switch currentNetlifyState {
			case NetlifyDeployStateReady:
				logger.Info("Deploy successful!", "DeployID", newDeployID)
				logger.Info("Attempting to lock new deploy", "DeployID", newDeployID)
				// Use netlifyInfra.Deploys for DeployService methods
				if _, lockErr := s.netlifyInfra.Deploys.LockDeploy(ctx, newDeployID); lockErr != nil {
					logger.Error("Failed to lock new deploy, but deployment was successful", "DeployID", newDeployID, "Error", lockErr)
				} else {
					logger.Info("Successfully locked new deploy", "DeployID", newDeployID)
				}
				return nil
			case NetlifyDeployStateError:
				errMsg := "Deployment failed with an unknown error."
				if deployStatus.ErrorMessage != "" {
					errMsg = deployStatus.ErrorMessage
				}
				logger.Error("Deploy failed", "DeployID", newDeployID, "ErrorMessage", errMsg)
				s.tryRelockPreviousDeploy(ctx, lockedDeployID)
				return fmt.Errorf("netlify deploy %s failed: %s", newDeployID, errMsg)
			case NetlifyDeployStateBuilding,
				NetlifyDeployStateEnqueued,
				NetlifyDeployStateProcessing,
				NetlifyDeployStateNew,
				NetlifyDeployStateUploading,
				NetlifyDeployStateUploaded,
				NetlifyDeployStatePreparing,
				NetlifyDeployStatePrepared,
				NetlifyDeployStateProcessed,
				NetlifyDeployStatePendingReview,
				NetlifyDeployStateAccepted,
				NetlifyDeployStateRejected,
				NetlifyDeployStateRetrying:
				logger.Debug("Deploy in progress, continuing to poll", "DeployID", newDeployID, "State", currentNetlifyState)
			default:
				logger.Warn("Encountered unhandled deploy state during polling", "DeployID", newDeployID, "State", currentNetlifyState)
			}
		}
	}
}

// PublishWorkflowWithFly 通过 agent 执行发布工作流。
// projectID: 项目的 ID。
// 返回: 发布的站点 URL 和错误。
func (s *PublishService) PublishWorkflowWithFly(ctx context.Context, projectID string) (string, error) {
	logger.Info("Starting publish workflow with Fly.io agent", "ProjectID", projectID)

	// --- 步骤 1: 发送 publish 命令给 Agent ---
	logger.Info("Step 1: Sending publish command to agent", "ProjectID", projectID)
	agentID := projectID // 直接用 ProjectID 作为 AgentID

	cmd := agentEntity.NewPublishCommand()
	response, err := s.agentManagerSvc.SendCommandToAgentAndWait(ctx, agentID, &cmd)
	if err != nil {
		return "", fmt.Errorf("failed to send publish command to agent %s: %w", agentID, err)
	}

	if !response.Success {
		return "", fmt.Errorf("agent command 'publish' failed: %s", response.Message)
	}
	logger.Info("Successfully received response from agent for publish command", "ProjectID", projectID, "AgentID", agentID, "Response.Message", response.Message, "Response.Data", response.Data)

	// --- 步骤 2: 解析响应并获取 URL ---
	var staticWebsiteURL string
	if data, ok := response.Data.(map[string]interface{}); ok {
		if url, ok := data["url"].(string); ok {
			staticWebsiteURL = url
		}
	}

	if staticWebsiteURL == "" {
		return "", fmt.Errorf("could not extract static website URL from agent response for project %s", projectID)
	}
	logger.Info("Extracted static website URL from agent response", "ProjectID", projectID, "URL", staticWebsiteURL)

	// --- 步骤 3: 保存真实URL到WorkerKV并更新代理地址到数据库 ---
	logger.Info("Step 3: Saving real URL to WorkerKV and updating proxy URL in DB", "ProjectID", projectID)

	// 生成代理地址
	proxyPublishURL := fmt.Sprintf("https://%s.webbuilder.world", projectID)

	// 保存真实地址到 WorkerKV
	if err := s.workerKVService.CreateOrUpdateURLConfig(ctx, projectID, nil, &staticWebsiteURL); err != nil {
		logger.Error("Failed to save real publish URL to WorkerKV", "ProjectID", projectID, "Error", err)
	} else {
		logger.Info("Saved real publish URL to WorkerKV", "ProjectID", projectID, "URL", staticWebsiteURL)
	}

	// 更新代理地址到数据库
	updateStatusData := map[string]interface{}{
		"publish_link": proxyPublishURL,
	}
	if _, err := s.statusRepo.UpdateByProjectID(ctx, projectID, updateStatusData); err != nil {
		logger.Error("Failed to update project status with proxy publish_link", "ProjectID", projectID, "Error", err)
	} else {
		logger.Info("Updated project status with proxy publish URL", "ProjectID", projectID, "ProxyURL", proxyPublishURL)
	}

	updateProjectData := map[string]interface{}{
		"is_published": true,
	}
	if _, err := s.projectRepo.UpdateFields(ctx, projectID, updateProjectData); err != nil {
		logger.Error("Failed to update project with is_published flag", "ProjectID", projectID, "Error", err)
	}
	logger.Info("Successfully updated project status and fields in DB", "ProjectID", projectID)
	//增加延迟，等待worker kv的写入，写入操作需要全球同步，可能存在30-60秒的延迟
	time.Sleep(30 * time.Second)
	// --- 步骤 4: 返回代理 URL ---
	return proxyPublishURL, nil
}

// PublishWorkflowWithNetlify 检查项目是否已有关联的 Netlify 站点。
// 如果没有，则创建新站点；否则，触发对现有站点的发布工作流。
// projectID: 目标项目的ID。
// 返回: 如果工作流成功，返回 deployID (对于现有站点) 或 siteID (对于新站点)。否则返回错误。
func (s *PublishService) PublishWorkflowWithNetlify(ctx context.Context, projectID string) (string, error) {
	logger.Info("Starting smart publish workflow", "ProjectID", projectID)

	// 1. 获取项目实体并检查 NetlifySiteID
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("Failed to get project for smart publish workflow", "ProjectID", projectID, "Error", err)
		return "", fmt.Errorf("failed to get project %s: %w", projectID, err)
	}

	// 2. 如果 NetlifySiteID 已存在，则直接触发新构建
	if project.NetlifySiteID != "" {
		logger.Info("Netlify site already exists, triggering a new build.", "ProjectID", projectID, "NetlifySiteID", project.NetlifySiteID)
		buildInfo, err := s.netlifyInfra.Builds.CreateSiteBuild(ctx, project.NetlifySiteID)
		if err != nil {
			logger.Error("Failed to trigger new site build", "SiteID", project.NetlifySiteID, "Error", err)
			return "", fmt.Errorf("triggering new site build for site %s failed: %w", project.NetlifySiteID, err)
		}
		logger.Info("New site build triggered successfully", "SiteID", project.NetlifySiteID, "BuildID", buildInfo.ID, "NewDeployID", buildInfo.DeployID)
		//sleep 10 seconds
		time.Sleep(30 * time.Second)
		return "", nil
	}

	// 3. 如果 NetlifySiteID 不存在，则执行站点创建流程
	logger.Info("Netlify site not found for project, starting creation process", "ProjectID", projectID)

	// 从配置中获取GitHub所有者，并从项目实体中获取仓库名称
	githubOwner := s.cfg.GitHub.Owner
	repoName := project.RepoID // 使用项目实体中的 RepoID 作为仓库名

	// TODO: 默认构建命令和发布目录应该从配置或项目设置中获取
	defaultBuildCmd := "npm run build"
	defaultPublishDir := "dist"

	// 调用 SetupDeployKey 创建并添加部署密钥
	deployKey, err := s.initSiteService.SetupDeployKey(ctx, repoName, githubOwner)
	if err != nil {
		return "", fmt.Errorf("failed to setup deploy key for project %s: %w", projectID, err)
	}

	// 调用 CreateNetlifySite 创建站点
	newSite, err := s.initSiteService.CreateNetlifySite(ctx, repoName, githubOwner, deployKey, defaultBuildCmd, defaultPublishDir)
	if err != nil {
		return "", fmt.Errorf("failed to create netlify site for project %s: %w", projectID, err)
	}

	// 关键步骤：将新创建的 NetlifySiteID 更新回项目实体
	project.NetlifySiteID = newSite.ID
	project.IsPublished = true
	if err := s.projectRepo.Update(ctx, project); err != nil {
		// 这是一个严重错误，站点已创建但未与项目关联
		logger.Error("CRITICAL: Failed to update project with new NetlifySiteID after site creation",
			"ProjectID", projectID, "NewNetlifySiteID", newSite.ID, "Error", err)
		return "", fmt.Errorf("site created (%s) but failed to link to project %s: %w", newSite.ID, projectID, err)
	}
	//将sslurl更新到项目状态实体中
	updateStatusData := map[string]interface{}{
		"publish_link": newSite.SslURL,
	}
	if _, err := s.statusRepo.UpdateByProjectID(ctx, projectID, updateStatusData); err != nil {
		logger.Error("Failed to update project status with publish_link", "ProjectID", projectID, "Error", err)
	}

	logger.Info("Successfully created and linked new Netlify site. Build is auto-triggered.", "ProjectID", projectID, "NetlifySiteID", newSite.ID)
	// 创建站点会自动触发第一次部署，所以这里直接返回新站点的ID
	// 增加随机等待时间，避免站点未部署完成，第一次部署可能需要30多秒
	time.Sleep(40 * time.Second)
	return newSite.SslURL, nil
}

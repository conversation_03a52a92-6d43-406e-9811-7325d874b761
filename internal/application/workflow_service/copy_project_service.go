package workflow_service

import (
	"context"
	"fmt"
	"strings"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/web-builder-dev/be-web-builder/internal/application/fly_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/netlify_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// MigrationsDirectory 表示migrations目录常量
const MigrationsDirectory = "migrations"

// CopyProjectService 提供了复制项目工作流的应用服务。
// 它编排了从源项目复制GitHub仓库，并异步初始化新项目的Netlify站点的过程。
type CopyProjectService struct {
	projectRepo           repository.ProjectRepository
	githubInfra           *infraGitHub.Service
	initNetlifySiteSvc    *netlify_service.InitNetlifySiteService // 用于异步初始化Netlify站点
	fileContentService    *github_service.FileContentService      // 用于获取仓库文件内容
	migrationProcessorSvc *MigrationProcessorService              // 用于检测和处理迁移
	supabaseMigrationSvc  *SupabaseMigrationService               // 用于调用Supabase Edge Function
	contentReplacerSvc    *GitHubContentReplacerService           // 用于替换仓库文件内容
	flyAppMachineSvc      *fly_service.FlyAppMachineService       // 用于异步创建Fly应用和机器
	cfg                   *config.Config
}

// NewCopyProjectService 创建一个新的 CopyProjectService 实例。
func NewCopyProjectService(
	projectRepo repository.ProjectRepository,
	githubInfra *infraGitHub.Service,
	initNetlifySiteSvc *netlify_service.InitNetlifySiteService,
	fileContentService *github_service.FileContentService,
	migrationProcessorSvc *MigrationProcessorService,
	supabaseMigrationSvc *SupabaseMigrationService,
	contentReplacerSvc *GitHubContentReplacerService,
	flyAppMachineSvc *fly_service.FlyAppMachineService,
	cfg *config.Config,
) *CopyProjectService {
	return &CopyProjectService{
		projectRepo:           projectRepo,
		githubInfra:           githubInfra,
		initNetlifySiteSvc:    initNetlifySiteSvc,
		fileContentService:    fileContentService,
		migrationProcessorSvc: migrationProcessorSvc,
		supabaseMigrationSvc:  supabaseMigrationSvc,
		contentReplacerSvc:    contentReplacerSvc,
		flyAppMachineSvc:      flyAppMachineSvc,
		cfg:                   cfg,
	}
}

// CopyProjectResult 包含 CopyProject 方法同步返回的结果。
type CopyProjectResult struct {
	NewRepoName string `json:"new_repo_id"` // 沿用Python示例中的字段名 new_repo_id
}

// CopyProject 复制项目工作流。
// 1. 获取源项目信息（主要是repo_id）。
// 2. 使用源项目的repo作为模板创建新的GitHub仓库，并将其也更新为模板仓库。
// 3. 更新目标项目的repo_id。
// 4. 去除这一步，(异步) 将新仓库部署到新的Netlify站点，设置相关配置，并更新目标项目记录的 netlify_site_id。
//
// Args:
//
//	sourceProjectID: 源项目的 UUID 字符串。
//	targetProjectID: 目标项目的 UUID 字符串。
//
// Returns:
//
//	包含新 GitHub 仓库名称的 CopyProjectResult。
//	Netlify 站点创建和关联将在后台异步进行。
func (s *CopyProjectService) CopyProject(ctx context.Context, sourceProjectID string, targetProjectID string) (*CopyProjectResult, error) {
	overallStartTime := time.Now()
	currentStepStartTime := overallStartTime
	timings := make(map[string]string) // 使用 string 来存储 duration 的字符串表示，更易于日志输出

	logger.Info("开始复制项目流程", "SourceProjectID", sourceProjectID, "TargetProjectID", targetProjectID)

	// 1. 获取源项目信息
	logger.Info("获取源项目信息", "SourceProjectID", sourceProjectID)
	sourceProject, err := s.projectRepo.GetByID(ctx, sourceProjectID)
	if err != nil {
		logger.Error("获取源项目信息失败", "SourceProjectID", sourceProjectID, "Error", err)
		return nil, fmt.Errorf("获取源项目 %s 失败: %w", sourceProjectID, err)
	}
	if sourceProject.RepoID == "" {
		logger.Error("源项目没有关联的 repo_id", "SourceProjectID", sourceProjectID)
		return nil, fmt.Errorf("源项目 %s 没有关联的 repo_id", sourceProjectID)
	}
	logger.Info("获取源项目信息成功", "SourceProjectID", sourceProjectID, "SourceRepoName", sourceProject.RepoID)
	timings["1_get_source_project_info_duration"] = time.Since(currentStepStartTime).String()
	currentStepStartTime = time.Now()

	// 2. 基于模板创建新的 GitHub 仓库
	sourceRepoName := sourceProject.RepoID
	newRepoName := fmt.Sprintf("project-%s-%d", targetProjectID[:8], time.Now().Unix())
	logger.Info("准备基于模板创建新仓库", "TemplateOwner", s.cfg.GitHub.Owner, "TemplateRepoName", sourceRepoName, "NewRepoName", newRepoName)

	templateRequest := &gh.TemplateRepoRequest{
		Name:               gh.Ptr(newRepoName),
		Owner:              gh.Ptr(s.cfg.GitHub.Owner),
		Private:            gh.Ptr(true),
		Description:        gh.Ptr(fmt.Sprintf("Repository for project %s, copied from %s", targetProjectID, sourceRepoName)),
		IncludeAllBranches: gh.Ptr(false),
	}

	createdRepo, _, err := s.githubInfra.Repositories.CreateRepositoryFromTemplate(ctx, s.cfg.GitHub.Owner, sourceRepoName, templateRequest)
	if err != nil {
		logger.Error("从模板创建 GitHub 仓库失败", "TemplateRepoName", sourceRepoName, "NewRepoName", newRepoName, "Error", err)
		return nil, fmt.Errorf("从模板 %s 创建仓库 %s 失败: %w", sourceRepoName, newRepoName, err)
	}
	actualNewRepoName := createdRepo.GetName()
	logger.Info("新 GitHub 仓库创建成功", "NewRepoName", actualNewRepoName, "NewRepoFullName", createdRepo.GetFullName())
	timings["2_create_github_repo_duration"] = time.Since(currentStepStartTime).String()

	// 尝试获取新仓库的提交历史 (不包含在这步的计时中)
	maxRetries := 5
	commitsFetched := false
	for attempt := range [5]struct{}{} {
		time.Sleep(time.Duration(attempt*2) * time.Second)
		commits, _, listErr := s.githubInfra.Repositories.ListRepositoryCommits(ctx, s.cfg.GitHub.Owner, actualNewRepoName, nil)
		if listErr == nil && len(commits) > 0 && commits[0].GetSHA() != "" {
			logger.Info("成功获取到新 GitHub 仓库的初始提交历史", "Attempt", attempt+1, "MaxAttempts", maxRetries, "NewRepoName", actualNewRepoName, "InitialCommitSHA", commits[0].GetSHA())
			commitsFetched = true
			break
		} else if listErr != nil {
			logger.Info("尝试获取仓库提交历史失败 (API错误)", "Attempt", attempt+1, "MaxAttempts", maxRetries, "NewRepoName", actualNewRepoName, "Error", listErr)
		} else {
			logger.Info("新 GitHub 仓库暂时没有提交记录或返回为空，稍后重试...", "Attempt", attempt+1, "MaxAttempts", maxRetries, "NewRepoName", actualNewRepoName)
		}
	}
	if !commitsFetched {
		logger.Warn("在多次尝试后仍未能获取仓库的提交历史，但不影响后续流程。", "NewRepoName", actualNewRepoName, "MaxAttempts", maxRetries)
	}
	// 3. 检测迁移需求并处理
	logger.Info("开始检测迁移需求", "NewRepoName", actualNewRepoName)
	migrationStartTime := time.Now()

	migrationDetected, err := s.detectAndProcessMigrations(ctx, actualNewRepoName, sourceProjectID, targetProjectID)
	if err != nil {
		logger.Warn("迁移检测失败，但不影响主要复制流程", "NewRepoName", actualNewRepoName, "Error", err)
	} else if migrationDetected {
		logger.Info("检测到迁移需求，已触发迁移处理", "NewRepoName", actualNewRepoName, "TargetProjectID", targetProjectID)
	} else {
		logger.Info("未检测到迁移需求", "NewRepoName", actualNewRepoName)
	}

	timings["3_migration_detection_duration"] = time.Since(migrationStartTime).String()
	currentStepStartTime = time.Now()

	// 4. 更新目标项目的 repo_id
	targetProject, err := s.projectRepo.GetByID(ctx, targetProjectID)
	if err != nil {
		logger.Error("获取目标项目实体失败，无法更新 repo_id", "TargetProjectID", targetProjectID, "Error", err)
		return nil, fmt.Errorf("获取目标项目 %s 用于更新 repo_id 失败: %w", targetProjectID, err)
	}
	targetProject.RepoID = actualNewRepoName
	err = s.projectRepo.Update(ctx, targetProject)
	if err != nil {
		logger.Error("更新目标项目 repo_id 失败", "TargetProjectID", targetProjectID, "NewRepoName", actualNewRepoName, "Error", err)
		return nil, fmt.Errorf("更新目标项目 %s 的 repo_id 为 %s 失败: %w", targetProjectID, actualNewRepoName, err)
	}
	logger.Info("目标项目 repo_id 更新成功", "TargetProjectID", targetProjectID, "NewRepoName", actualNewRepoName)
	timings["4_update_project_repo_id_duration"] = time.Since(currentStepStartTime).String()
	currentStepStartTime = time.Now()

	// 5. 将新仓库更新为模板仓库
	updateData := &gh.Repository{IsTemplate: gh.Ptr(true)}
	_, _, err = s.githubInfra.Repositories.UpdateRepository(ctx, s.cfg.GitHub.Owner, actualNewRepoName, updateData)
	if err != nil {
		logger.Warn("将新仓库更新为模板失败，但不影响主要复制流程", "NewRepoName", actualNewRepoName, "Error", err)
	} else {
		logger.Info("新仓库已成功更新为模板仓库", "NewRepoName", actualNewRepoName)
	}
	timings["4_update_repo_as_template_duration"] = time.Since(currentStepStartTime).String()
	// currentStepStartTime = time.Now()

	// 5. (异步) 设置 Netlify 站点
	logger.Info("GitHub 仓库设置完成。", "TargetProjectID", targetProjectID, "NewRepoName", actualNewRepoName)
	// go s.asyncSetupNetlifyForCopiedProject(targetProjectID, actualNewRepoName)
	// timings["5_schedule_async_netlify_setup_duration"] = time.Since(currentStepStartTime).String()
	go s.asyncSetupFlyAppAndMachineForCopiedProject(targetProjectID)
	timings["5_schedule_async_fly_app_and_machine_setup_duration"] = time.Since(currentStepStartTime).String()

	timings["total_synchronous_duration"] = time.Since(overallStartTime).String()
	logger.Info("项目复制流程同步阶段各步骤耗时", "TargetProjectID", targetProjectID, "Timings", timings)

	logger.Info("项目复制流程同步阶段完成", "TargetProjectID", targetProjectID, "NewRepoName", actualNewRepoName)
	return &CopyProjectResult{NewRepoName: actualNewRepoName}, nil
}

// asyncSetupNetlifyForCopiedProject 是一个内部函数，用于在新goroutine中异步处理Netlify站点的创建和配置。
// 它会调用 InitNetlifySiteService.InitNetlifySite。
// targetProjectIDForLog: 用于日志和资源命名的原始目标项目ID (string)。
// newRepoName: 新创建并关联到目标项目的GitHub仓库名称。
func (s *CopyProjectService) asyncSetupNetlifyForCopiedProject(targetProjectIDForLog string, newRepoName string) {
	// 创建一个新的上下文，因为它在不同的goroutine中运行，不应直接复用原始请求的上下文
	// 可以考虑使用 context.Background() 或根据需要创建一个带超时的上下文
	asyncCtx := context.Background() // 或者 context.TODO() 如果不确定

	logger.Info("[异步] 开始为复制的项目设置 Netlify", "TargetProjectID", targetProjectIDForLog, "NewRepoName", newRepoName)

	// InitNetlifySite 已经处理了在其内部更新项目记录中的 netlify_site_id
	// 所以这里不需要再次获取和更新项目实体来设置 NetlifySiteID
	_, err := s.initNetlifySiteSvc.InitNetlifySite(asyncCtx, targetProjectIDForLog, newRepoName)
	if err != nil {
		logger.Error("[异步] Netlify 站点初始化失败",
			"TargetProjectID", targetProjectIDForLog,
			"NewRepoName", newRepoName,
			"Error", err)
		// 这里的错误是异步的，不会直接返回给 CopyProject 的调用者。
		// 可以考虑更复杂的错误通知机制，例如通过channel，或写入数据库状态。
		// 目前，仅记录错误。
		return
	}

	logger.Info("[异步] Netlify 站点初始化成功完成", "TargetProjectID", targetProjectIDForLog, "NewRepoName", newRepoName)
	// InitNetlifySiteService 内部已经更新了 targetProjectIDForLog 对应的项目实体的 NetlifySiteID
}

// detectAndProcessMigrations 检测迁移需求并进行处理
func (s *CopyProjectService) detectAndProcessMigrations(ctx context.Context, repoName string, sourceProjectID string, targetProjectID string) (bool, error) {
	logger.Info("开始检测仓库迁移需求", "RepoName", repoName, "SourceProjectID", sourceProjectID, "TargetProjectID", targetProjectID)

	// 1. 获取仓库文件列表
	sql_files, err := s.fileContentService.GetRepositoryFileContentsInDirectory(ctx, repoName, MigrationsDirectory)
	if err != nil {
		return false, fmt.Errorf("获取仓库文件列表失败: %w", err)
	}

	// 2. 提取文件名列表
	var fileNames []string
	for _, file := range sql_files {
		fileNames = append(fileNames, file.Name)
	}

	logger.Info("获取仓库文件列表成功", "RepoName", repoName, "FileCount", len(fileNames))

	// 3. 检测migrations目录下是否有SQL文件
	needsMigration := s.detectMigrationsInDirectory(fileNames)

	if !needsMigration {
		logger.Info("未在migrations目录检测到SQL文件", "RepoName", repoName)
		return false, nil
	}

	logger.Info("在migrations目录检测到SQL文件，需要进行迁移处理", "RepoName", repoName)

	// 4. 先替换仓库中的项目ID

	logger.Info("开始替换仓库文件中的项目ID", "RepoName", repoName, "SourceProjectID", sourceProjectID, "TargetProjectID", targetProjectID)

	replaceResult, err := s.contentReplacerSvc.ReplaceProjectIDInRepository(ctx, repoName, sourceProjectID, targetProjectID)
	if err != nil {
		logger.Error("替换项目ID失败", "RepoName", repoName, "Error", err)
		return false, fmt.Errorf("替换项目ID失败: %w", err)
	}

	logger.Info("项目ID替换完成",
		"RepoName", repoName,
		"ModifiedFiles", replaceResult.ModifiedFiles,
		"CommitSHA", replaceResult.CommitSHA)

	// 5. 异步调用Supabase Edge Function执行迁移
	if s.supabaseMigrationSvc != nil {
		logger.Info("开始异步执行迁移", "RepoName", repoName, "TargetProjectID", targetProjectID)
		s.supabaseMigrationSvc.ExecuteMigrationAsync(ctx, sql_files, sourceProjectID, targetProjectID)
	} else {
		logger.Warn("Supabase Migration Service未初始化，跳过迁移执行", "RepoName", repoName)
	}

	return true, nil
}

// detectMigrationsInDirectory 判断目录下是否有 SQL 文件
func (s *CopyProjectService) detectMigrationsInDirectory(fileNames []string) bool {
	for _, fileName := range fileNames {
		if strings.HasSuffix(strings.ToLower(fileName), ".sql") {
			return true
		}
	}
	return false
}

// asyncSetupFlyAppAndMachineForCopiedProject 是一个内部函数，用于在新goroutine中异步处理Fly应用的创建和机器配置。
// 它会调用 FlyAppMachineService.CreateAppAndMachine。
// targetProjectIDForLog: 用于日志和资源命名的原始目标项目ID (string)。
// appName: 要创建的Fly应用名称。
func (s *CopyProjectService) asyncSetupFlyAppAndMachineForCopiedProject(targetProjectID string) {
	// 创建一个新的上下文，因为它在不同的goroutine中运行，不应直接复用原始请求的上下文
	// 可以考虑使用 context.Background() 或根据需要创建一个带超时的上下文
	asyncCtx := context.Background() // 或者 context.TODO() 如果不确定

	logger.Info("[异步] 开始为复制的项目创建Fly应用和机器", "TargetProjectID", targetProjectID)

	// CreateAppAndMachine 已经处理了在其内部创建app、分配IP地址和创建machine
	// 所以这里不需要再次获取和更新项目实体来设置相关状态
	_, err := s.flyAppMachineSvc.CreateAppAndMachine(asyncCtx, targetProjectID)
	if err != nil {
		logger.Error("[异步] Fly应用和机器创建失败",
			"TargetProjectID", targetProjectID,
			"AppName", targetProjectID,
			"Error", err)
		// 这里的错误是异步的，不会直接返回给 CopyProject 的调用者。
		// 可以考虑更复杂的错误通知机制，例如通过channel，或写入数据库状态。
		// 目前，仅记录错误。
		return
	}

	logger.Info("[异步] Fly应用和机器创建成功完成", "TargetProjectID", targetProjectID)
	// CreateAppAndMachine 内部已经完成了app创建、IP分配和machine创建
}

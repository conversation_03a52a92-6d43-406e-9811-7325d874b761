package github_service

import (
	"context"
	"fmt"
	"time"

	gh "github.com/google/go-github/v72/github"
	infragh "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github" // infra层github服务
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"                 // DTOs
)

// CodeCommitService 提供了提交代码变更到 GitHub 仓库的应用服务。
type CodeCommitService struct {
	ghService *infragh.Service // 依赖底层的 GitHub 基础设施服务
}

// NewCodeCommitService 创建一个新的 CodeCommitService 实例。
func NewCodeCommitService(ghService *infragh.Service) *CodeCommitService {
	return &CodeCommitService{ghService: ghService}
}

// CommitChanges 处理代码提交请求，包括创建/更新文件、删除文件，并将其提交到指定分支。
// ctx: 上下文。
// req: 包含提交详情的 CommitRequest DTO。
//
// 返回:
// - *gh.Commit: 成功创建的 GitHub Commit 对象 (Git 数据层面的 Commit)。
// - error: 如果在过程中发生任何错误。
func (s *CodeCommitService) CommitChanges(ctx context.Context, req *dto.CommitRequest) (*gh.Commit, error) {
	// 0. 设置默认分支 (如果未提供)
	branchName := req.Branch
	if branchName == "" {
		branchName = "main"
	}

	// 1. 获取当前分支的最新 commit SHA 和 tree SHA (优化方案)
	branch, _, err := s.ghService.Branches.GetBranch(ctx, req.Owner, req.Repository, branchName, 1 /* maxRedirects */)
	if err != nil {
		// 如果分支不存在，处理方式同之前，通常期望分支已存在。
		return nil, fmt.Errorf("failed to get branch '%s': %w", branchName, err)
	}

	parentRepoCommit := branch.GetCommit() // This is *github.RepositoryCommit
	if parentRepoCommit == nil || parentRepoCommit.GetSHA() == "" {
		return nil, fmt.Errorf("branch '%s' does not have a valid commit SHA", branchName)
	}
	parentCommitSHA := parentRepoCommit.GetSHA()

	// 从 GetBranch 返回的 Commit 对象中直接获取 Tree SHA
	if parentRepoCommit.GetCommit() == nil || parentRepoCommit.GetCommit().GetTree() == nil || parentRepoCommit.GetCommit().GetTree().GetSHA() == "" {
		return nil, fmt.Errorf("commit '%s' on branch '%s' does not have a valid tree SHA", parentCommitSHA, branchName)
	}
	baseTreeSHA := parentRepoCommit.GetCommit().GetTree().GetSHA()

	// 2. 准备 Tree Entries
	var treeEntries []*gh.TreeEntry

	// 处理要创建/更新的文件
	for _, file := range req.Files {
		treeEntries = append(treeEntries, &gh.TreeEntry{
			Path:    gh.Ptr(file.Path),
			Type:    gh.Ptr("blob"),
			Mode:    gh.Ptr("100644"), // 普通文件
			Content: gh.Ptr(file.Content),
		})
	}

	// 处理要删除的文件: 在 GitHub API 中，从树中删除文件意味着在新树中不包含该文件的条目。
	// 如果我们基于一个 baseTree 创建树，并且新的 entries 列表中省略了 baseTree 中的某个文件，
	// 那么该文件在新生成的树中就会被移除。
	// CreateTree API 自身不直接接受"删除"标记的条目。
	// 因此，我们需要确保从 baseTree 加载现有文件，然后只添加/更新我们想要保留的。
	// 或者，如果 req.DeletedFiles 提供了路径，确保这些路径不在最终的 treeEntries 中。
	//
	// 一个更直接的实现方式（如果允许递归获取整个树，但可能代价高昂）:
	// 1. 获取 baseTree 的所有条目。
	// 2. 从中移除 req.DeletedFiles 中指定的条目。
	// 3. 添加或更新 req.Files 中的条目。
	//
	// 简单策略：当前的 go-github CreateTree 接受一个 base_tree 和一个新 entry 列表。
	// 新的 entry 会覆盖或添加到 base_tree。
	// 要删除文件，需要确保新 entry 列表中不包含它。
	// 如果 base_tree 包含 file_to_delete.txt，而你的 entries 不包含它，它就会被删除。
	// 如果你的 entries 包含一个 path，但 SHA 设置为 nil (或 content 为 nil 且 type 为 blob)，行为可能不确定或API会拒绝。
	// 最安全的方法是，如果 baseTreeSHA 被使用，API 会处理合并。
	// 对于被删除的文件，我们只需要确保它们不出现在传递给 CreateTree 的 `entries` 数组中，
	// 并且它们也不应该通过 `baseTreeSHA` 继承过来（如果它们在 `baseTreeSHA` 中本就不存在则没问题，
	// 但如果它们存在于 `baseTreeSHA` 指向的树中，而我们又想删除它们，就需要更复杂的树操作，
	// 或者创建一个不包含它们的新树）。
	//
	// **简化处理**：当前版本的 GitHub API，如果提供 baseTreeSHA，然后提供的 entries 中
	// 的路径与 baseTreeSHA 中的路径冲突，会使用新 entry。
	// 如果新 entries 中省略了 baseTreeSHA 中的某个路径，该路径会从最终的树中移除。
	// 所以，对于 DeletedFiles，我们只需要保证它们不被添加到上面的 `treeEntries` 即可（如果它们和 Files 中有重合的话）。
	// 如果要明确地基于现有树删除，一个更 robust 的方法是获取旧树，过滤掉要删除的，然后添加新文件，最后创建没有 baseTreeSHA 的新树。
	// 但这会复杂化。这里，我们假设 `req.Files` 是最终状态，`req.DeletedFiles` 是提示性的，如果一个文件
	// 同时在 Files 和 DeletedFiles 中，Files 优先。
	// 最符合 API 的方式是：`entries` 参数只包含要存在于新树中的文件。
	// 如果 `baseTreeSHA` 被设置，它代表了基础版本。
	if len(req.DeletedFiles) > 0 {
		for _, filePath := range req.DeletedFiles {
			// 检查是否已在 req.Files 中（更新/添加优先于删除）
			foundInFiles := false
			for _, f := range req.Files {
				if f.Path == filePath {
					foundInFiles = true
					break
				}
			}
			if !foundInFiles {
				treeEntries = append(treeEntries, &gh.TreeEntry{
					Path: gh.Ptr(filePath),
					Mode: gh.Ptr("100644"), // Mode is technically needed for a deletion entry
					SHA:  nil,              // Setting SHA to null (or omitting it if type is blob & no content) signals deletion
					Type: gh.Ptr("blob"),   // Type is also needed
				})
			}
		}
	}
	// 3. 创建新的 Git Tree
	// 如果 entries 为空，且 baseTreeSHA 也有效，这可能意味着只删除了文件 (如果 baseTree 有内容)
	// 或者没有变化 (如果 baseTree 本身就是目标状态)。
	// 如果 entries 为空，且 baseTreeSHA 为空，则是一个空树。
	// GitHub API：如果提供了树参数，新树将基于该树。
	// 否则，它将从头开始创建新树。
	// 如果没有提供条目，树将基于base_tree。
	// 如果提供了条目，它们将与base_tree合并。
	// 具有null SHA（或路径和模式但没有SHA或内容）的条目将从基础树中删除。（这在使用基础树进行删除时是关键！）
	newTree, _, err := s.ghService.Trees.CreateTree(ctx, req.Owner, req.Repository, baseTreeSHA, treeEntries)
	if err != nil {
		return nil, fmt.Errorf("failed to create new tree based on tree '%s': %w", baseTreeSHA, err)
	}

	// 4. 创建新的 Git Commit
	commitData := &gh.Commit{
		Message: gh.Ptr(req.CommitMessage),
		Tree:    &gh.Tree{SHA: gh.Ptr(newTree.GetSHA())},
		Parents: []*gh.Commit{{SHA: gh.Ptr(parentCommitSHA)}},
	}

	if req.AuthorName != "" && req.AuthorEmail != "" {
		commitData.Author = &gh.CommitAuthor{
			Name:  gh.Ptr(req.AuthorName),
			Email: gh.Ptr(req.AuthorEmail),
			Date:  &gh.Timestamp{Time: time.Now()},
		}
	}

	newCommit, _, err := s.ghService.Commits.CreateCommit(ctx, req.Owner, req.Repository, commitData, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create new commit with tree '%s': %w", newTree.GetSHA(), err)
	}

	// 5. 更新分支引用 (Ref)
	refPath := "refs/heads/" + branchName // refPath 在第28行已经定义过了，这里重复定义了。
	_, _, err = s.ghService.References.UpdateReference(ctx, req.Owner, req.Repository, refPath, newCommit.GetSHA(), false /* force */)
	if err != nil {
		return nil, fmt.Errorf("failed to update reference '%s' to commit '%s': %w", refPath, newCommit.GetSHA(), err)
	}

	return newCommit, nil
}

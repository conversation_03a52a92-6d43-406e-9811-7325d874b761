package github_service

import (
	"context"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	infragh "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// FileContentService 提供了获取 GitHub 仓库文件内容的应用服务。
type FileContentService struct {
	ghService *infragh.Service // 依赖底层的 GitHub 基础设施服务
	config    *config.Config   // 配置信息
}

// NewFileContentService 创建一个新的 FileContentService 实例。
func NewFileContentService(ghService *infragh.Service, cfg *config.Config) *FileContentService {
	return &FileContentService{
		ghService: ghService,
		config:    cfg,
	}
}

// GetRepositoryFileContents 获取 GitHub 仓库中所有文件的内容。
// 参数:
//   - ctx: 上下文
//   - repoName: GitHub 仓库名称
//
// 返回:
//   - []infragh.File: 文件内容列表
//   - error: 如果在过程中发生任何错误
func (s *FileContentService) GetRepositoryFileContents(ctx context.Context, repoName string) ([]infragh.File, error) {
	// 直接调用目录方法，传空字符串表示根目录
	return s.GetRepositoryFileContentsInDirectory(ctx, repoName, "")
}

// GetRepositoryFileContentsInDirectory 获取 GitHub 仓库指定目录下所有文件的内容。
// 参数:
//   - ctx: 上下文
//   - repoName: GitHub 仓库名称
//   - dir: 目录名（如 "migrations"，可为空字符串表示根目录）
//
// 返回:
//   - []infragh.File: 文件内容列表
//   - error: 如果在过程中发生任何错误
func (s *FileContentService) GetRepositoryFileContentsInDirectory(ctx context.Context, repoName string, dir string) ([]infragh.File, error) {
	owner := s.config.GitHub.Owner
	if owner == "" {
		return nil, fmt.Errorf("GitHub owner not configured")
	}

	// 1. 构造 treeSHA，支持目录参数
	treeSHA := "main"
	if dir != "" {
		treeSHA = fmt.Sprintf("main:%s", dir)
	}

	tree, _, err := s.ghService.Trees.GetTree(ctx, owner, repoName, treeSHA, true) // recursive = true
	if err != nil {
		return nil, fmt.Errorf("failed to get tree for %s: %w", treeSHA, err)
	}

	if tree == nil || len(tree.Entries) == 0 {
		return []infragh.File{}, nil // 返回空列表，表示没有文件
	}

	// 2. 准备文件信息列表
	var fileInfos []infragh.FileInfo
	for _, entry := range tree.Entries {
		// 只处理文件（blob），跳过目录（tree）
		if entry.GetType() == "blob" {
			fileInfos = append(fileInfos, infragh.FileInfo{
				Path: entry.GetPath(),
				Sha:  entry.GetSHA(),
			})
		}
	}

	if len(fileInfos) == 0 {
		return []infragh.File{}, nil // 没有文件，返回空列表
	}

	// 3. 调用 FetchFileContents 方法批量获取文件内容
	batchSize := 50
	files, err := s.ghService.Files.FetchFileContents(ctx, owner, repoName, fileInfos, batchSize)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch file contents: %w", err)
	}

	return files, nil
}

package agent_service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	projectRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraWS "github.com/web-builder-dev/be-web-builder/internal/infrastructure/websocket"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	commandTimeout      = 5 * time.Minute
	healthCheckTimeout  = 30 * time.Second
	healthCheckRetries  = 3
	healthCheckInterval = 5 * time.Second
)

// AgentSetupService 负责处理新 agent 连接后的环境设置流程
type AgentSetupService struct {
	projectRepo       projectRepo.ProjectRepository
	projectStatusRepo projectRepo.ProjectStatusRepository
	workerKVService   *cloudflare_service.WorkerKVService
}

// NewAgentSetupService 创建 AgentSetupService 实例
func NewAgentSetupService(
	projectRepo projectRepo.ProjectRepository,
	projectStatusRepo projectRepo.ProjectStatusRepository,
	workerKVService *cloudflare_service.WorkerKVService,
) *AgentSetupService {
	return &AgentSetupService{
		projectRepo:       projectRepo,
		projectStatusRepo: projectStatusRepo,
		workerKVService:   workerKVService,
	}
}

// SetupAgentEnvironment 为指定的 appName 设置 agent 环境
func (s *AgentSetupService) SetupAgentEnvironment(ctx context.Context, agentConn *infraWS.AgentConnection, appName string) error {
	logger.Info("Starting agent environment setup for app:", appName)

	// 1. 获取项目信息
	project, err := s.projectRepo.GetByID(ctx, appName)
	if err != nil {
		return fmt.Errorf("failed to get project by id %s: %w", appName, err)
	}
	if project.RepoID == "" {
		return fmt.Errorf("project %s has no RepoID", appName)
	}
	logger.Info("Found project", appName, "with RepoID:", project.RepoID)

	// 2. 发送 clone 命令并等待响应
	cloneCmd := entity.NewCloneRepoCommand(project.RepoID)
	cmdCtx, cancel := context.WithTimeout(ctx, commandTimeout)
	cloneResp, err := s.sendCommandAndWait(cmdCtx, agentConn, cloneCmd)
	cancel()
	if err != nil {
		return fmt.Errorf("clone repo command failed for %s: %w", appName, err)
	}
	if !cloneResp.Success {
		return fmt.Errorf("clone repo failed for %s with message: %s", appName, cloneResp.Message)
	}
	logger.Info("Clone repository successful for", appName, ":", cloneResp.Message)

	// 3. 发送 run_vite 命令并等待响应
	viteCmd := entity.NewRunViteCommand()
	cmdCtx2, cancel2 := context.WithTimeout(ctx, commandTimeout)
	viteResp, err := s.sendCommandAndWait(cmdCtx2, agentConn, viteCmd)
	cancel2()
	if err != nil {
		return fmt.Errorf("run vite command failed for %s: %w", appName, err)
	}
	if !viteResp.Success {
		return fmt.Errorf("run vite failed for %s with message: %s", appName, viteResp.Message)
	}
	logger.Info("Vite server started successfully for", appName, ":", viteResp.Message)

	// 4. 验证服务是否真的启动成功
	// if err := s.verifyServiceHealth(ctx, agentConn, appName); err != nil {
	// 	logger.Warn("Service health check failed for", appName, ":", err)
	// 	// 健康检查失败是警告，不是致命错误，因此不返回 err
	// } else {
	// 	logger.Info("Service health check successful for", appName)
	// }

	// 5. 保存真实的 fly.dev 地址到 WorkerKV 并更新代理地址到 preview_link
	realFlyDevURL := fmt.Sprintf("https://%s.fly.dev", appName)
	proxyPreviewURL := fmt.Sprintf("https://id-preview-%s.webbuilder.world", appName)

	// 保存真实地址到 WorkerKV
	if err := s.workerKVService.CreateOrUpdateURLConfig(ctx, appName, &realFlyDevURL, nil); err != nil {
		logger.Error("Failed to save real URL to WorkerKV for", appName, ":", err)
		// 继续执行，不因为 KV 保存失败而中断整个流程
	} else {
		logger.Info("Saved real fly.dev URL to WorkerKV for", appName, ":", realFlyDevURL)
	}

	// 更新代理地址到 preview_link
	updates := map[string]interface{}{"preview_link": proxyPreviewURL, "status": "done"}
	if _, err := s.projectStatusRepo.UpdateByProjectID(ctx, appName, updates); err != nil {
		return fmt.Errorf("failed to update preview link for %s: %w", appName, err)
	}
	logger.Info("Updated preview link for", appName, "to proxy URL:", proxyPreviewURL)

	return nil
}

// sendCommandAndWait 封装了发送命令和从 channel 等待响应的逻辑
func (s *AgentSetupService) sendCommandAndWait(ctx context.Context, conn *infraWS.AgentConnection, cmd entity.Command) (*dto.Response, error) {
	if err := conn.SendJSON(ctx, cmd); err != nil {
		return nil, fmt.Errorf("failed to send command %s: %w", cmd.Type, err)
	}

	// 等待响应 channel 中的数据
	msg, err := conn.ReadResponse(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to read response for command %s from channel: %w", cmd.Type, err)
	}

	var response dto.Response
	if err := json.Unmarshal(msg, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response for command %s: %w", cmd.Type, err)
	}

	return &response, nil
}

// verifyServiceHealth 向 agent 发送健康检查命令，并带重试逻辑
func (s *AgentSetupService) verifyServiceHealth(ctx context.Context, agentConn *infraWS.AgentConnection, appName string) error {
	healthCmd := entity.NewHealthCheckCommand()
	var lastErr error

	for i := 0; i < healthCheckRetries; i++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		logger.Info("Performing health check for", appName, ", attempt", i+1)
		cmdCtx, cancel := context.WithTimeout(ctx, healthCheckTimeout)
		resp, err := s.sendCommandAndWait(cmdCtx, agentConn, healthCmd)
		cancel()

		if err == nil && resp.Success {
			return nil // 健康检查成功
		}

		if err != nil {
			lastErr = err
		} else {
			lastErr = fmt.Errorf("health check failed with message: %s", resp.Message)
		}

		// 等待一段时间再重试
		if i < healthCheckRetries-1 {
			time.Sleep(healthCheckInterval)
		}
	}

	return fmt.Errorf("service for %s is not healthy after %d attempts: %w", appName, healthCheckRetries, lastErr)
}

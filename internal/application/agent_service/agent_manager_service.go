package agent_service

import (
	"context"
	"encoding/json"
	"fmt"

	agentDto "github.com/web-builder-dev/be-web-builder/internal/interface/dto"

	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/repository"
)

// AgentManagerService 管理 agent 的连接和通信
type AgentManagerService struct {
	repo repository.AgentRepository
}

// NewAgentManagerService 创建一个新的 AgentManagerService
func NewAgentManagerService(repo repository.AgentRepository) *AgentManagerService {
	return &AgentManagerService{
		repo: repo,
	}
}

// RegisterAgent 注册 agent
func (s *AgentManagerService) RegisterAgent(ctx context.Context, agent *entity.Agent) error {
	return s.repo.Register(ctx, agent)
}

// UnregisterAgent 注销 agent
func (s *AgentManagerService) UnregisterAgent(ctx context.Context, agentID string) error {
	return s.repo.Unregister(ctx, agentID)
}

// SendCommandToAgent 向指定 agent 发送命令
func (s *AgentManagerService) SendCommandToAgent(ctx context.Context, agentID string, cmd *entity.Command) error {
	return s.repo.SendCommand(ctx, agentID, cmd)
}

// GetAgent 获取 agent 信息
func (s *AgentManagerService) GetAgent(ctx context.Context, agentID string) (*entity.Agent, error) {
	return s.repo.GetByID(ctx, agentID)
}

// ListAgents 获取所有 agent
func (s *AgentManagerService) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	return s.repo.ListAgents(ctx)
}

// SendCommandToAgentAndWait 向指定的 agent 发送命令并等待响应
func (s *AgentManagerService) SendCommandToAgentAndWait(ctx context.Context, agentID string, cmd *entity.Command) (*agentDto.Response, error) {
	rawResp, err := s.repo.SendCommandAndWait(ctx, agentID, cmd)
	if err != nil {
		return nil, fmt.Errorf("failed to send command and wait for agent %s: %w", agentID, err)
	}

	var response agentDto.Response
	if err := json.Unmarshal(rawResp, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response from agent %s: %w", agentID, err)
	}

	return &response, nil
}

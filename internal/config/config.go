package config

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// GitHubConfig 存储 GitHub相关的配置
type GitHubConfig struct {
	Token string `yaml:"token"`
	Owner string `yaml:"owner"`
}

// NetlifyConfig 存储 Netlify 相关的配置
type NetlifyConfig struct {
	Token   string `yaml:"token"`
	HookURL string `yaml:"hook_url"`
}

// SupabaseConfig 存储 Supabase 相关的配置
// url: Supabase 实例地址
// service_key: Supabase 服务密钥
// edge_functions_token: Supabase Edge Functions 调用令牌
type SupabaseConfig struct {
	URL                string `yaml:"url"`
	ServiceKey         string `yaml:"service_key"`
	EdgeFunctionsToken string `yaml:"edge_functions_token"`
}

// LogConfig 存储日志相关的配置
type LogConfig struct {
	Level string `yaml:"level"`
}

// CloudflareConfig 存储 Cloudflare 相关的配置
type CloudflareConfig struct {
	APIKey         string `yaml:"api_key"`
	APIEmail       string `yaml:"api_email"`
	AccountID      string `yaml:"account_id"`
	WebhookAuthKey string `yaml:"webhook_auth_key"`
	WebhookID      string `yaml:"webhook_id"`
	KVNamespaceID  string `yaml:"kv_namespace_id"`
}

// FlyConfig 存储 Fly.io 相关的配置
type FlyConfig struct {
	APIToken string `yaml:"api_token"`
	OrgSlug  string `yaml:"org_slug"`
	WSSURL   string `yaml:"wss_url"`
}

// Config 存储所有应用程序配置
type Config struct {
	GitHub                 GitHubConfig     `yaml:"github"`
	Log                    LogConfig        `yaml:"log"`
	Netlify                NetlifyConfig    `yaml:"netlify"`
	Supabase               SupabaseConfig   `yaml:"supabase"`
	Cloudflare             CloudflareConfig `yaml:"cloudflare"`
	Fly                    FlyConfig        `yaml:"fly"`
	BEScreenshotAPIBaseURL string           `yaml:"be_screenshot_api_base_url"`
}

// LoadConfig 加载指定环境的配置。
// 它首先从 conf/config_<env>.yaml 文件读取配置，
// 然后使用环境变量覆盖这些值（如果环境变量存在）。
func LoadConfig(env string) (*Config, error) {
	cfg := &Config{
		// 设置默认日志级别
		Log: LogConfig{Level: "info"},
	}
	root, err := FindProjectRoot()
	if err != nil {
		return nil, fmt.Errorf("failed to find project root: %w", err)
	}
	configFilePath := filepath.Join(root, "conf", fmt.Sprintf("config_%s.yaml", env))

	// 从 YAML 文件读取配置
	file, err := os.ReadFile(configFilePath)
	if err == nil { // 如果文件存在且可读
		err = yaml.Unmarshal(file, cfg)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling config file %s: %w", configFilePath, err)
		}
	} else if !os.IsNotExist(err) { // 如果是其他错误而不是文件不存在
		return nil, fmt.Errorf("error reading config file %s: %w", configFilePath, err)
	}

	// 从环境变量覆盖 GitHub 配置
	if githubToken := os.Getenv("GITHUB_TOKEN"); githubToken != "" {
		cfg.GitHub.Token = githubToken
	}
	if githubOwner := os.Getenv("GITHUB_OWNER"); githubOwner != "" {
		cfg.GitHub.Owner = githubOwner
	}

	// 从环境变量覆盖日志配置
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Log.Level = logLevel
	}

	// 从环境变量覆盖 Netlify 配置
	if netlifyToken := os.Getenv("NETLIFY_TOKEN"); netlifyToken != "" {
		cfg.Netlify.Token = netlifyToken
	}
	if netlifyHookURL := os.Getenv("NETLIFY_HOOK_URL"); netlifyHookURL != "" {
		cfg.Netlify.HookURL = netlifyHookURL
	}

	// 从环境变量覆盖 Supabase 配置
	if supabaseURL := os.Getenv("SUPABASE_URL"); supabaseURL != "" {
		cfg.Supabase.URL = supabaseURL
	}
	if supabaseServiceKey := os.Getenv("SUPABASE_SERVICE_KEY"); supabaseServiceKey != "" {
		cfg.Supabase.ServiceKey = supabaseServiceKey
	}
	if edgeFunctionsToken := os.Getenv("SUPABASE_EDGE_FUNCTIONS_TOKEN"); edgeFunctionsToken != "" {
		cfg.Supabase.EdgeFunctionsToken = edgeFunctionsToken
	}

	// 从环境变量覆盖 Cloudflare 配置
	if cloudflareAPIKey := os.Getenv("CLOUDFLARE_API_KEY"); cloudflareAPIKey != "" {
		cfg.Cloudflare.APIKey = cloudflareAPIKey
	}
	if cloudflareAPIEmail := os.Getenv("CLOUDFLARE_API_EMAIL"); cloudflareAPIEmail != "" {
		cfg.Cloudflare.APIEmail = cloudflareAPIEmail
	}
	if cloudflareAccountID := os.Getenv("CLOUDFLARE_ACCOUNT_ID"); cloudflareAccountID != "" {
		cfg.Cloudflare.AccountID = cloudflareAccountID
	}
	if cloudflareWebhookKey := os.Getenv("CLOUDFLARE_WEBHOOK_AUTH_KEY"); cloudflareWebhookKey != "" {
		cfg.Cloudflare.WebhookAuthKey = cloudflareWebhookKey
	}
	if cloudflareWebhookID := os.Getenv("CLOUDFLARE_WEBHOOK_ID"); cloudflareWebhookID != "" {
		cfg.Cloudflare.WebhookID = cloudflareWebhookID
	}
	if cloudflareKVNamespaceID := os.Getenv("CLOUDFLARE_KV_NAMESPACE_ID"); cloudflareKVNamespaceID != "" {
		cfg.Cloudflare.KVNamespaceID = cloudflareKVNamespaceID
	}
	// 新增：从环境变量覆盖截图API BaseURL
	if screenshotBaseURL := os.Getenv("BE_SCREENSHOT_API_BASE_URL"); screenshotBaseURL != "" {
		cfg.BEScreenshotAPIBaseURL = screenshotBaseURL
	}

	// 从环境变量覆盖 Fly.io 配置
	if flyAPIToken := os.Getenv("FLY_API_TOKEN"); flyAPIToken != "" {
		cfg.Fly.APIToken = flyAPIToken
	}
	if flyOrgSlug := os.Getenv("FLY_ORG_SLUG"); flyOrgSlug != "" {
		cfg.Fly.OrgSlug = flyOrgSlug
	}

	// 校验必要的 GitHub 配置是否存在
	yamlFileHasGithubSection := false
	if file != nil {
		var rawYaml map[string]any
		if yaml.Unmarshal(file, &rawYaml) == nil {
			_, yamlFileHasGithubSection = rawYaml["github"]
		}
	}
	if os.Getenv("GITHUB_TOKEN") != "" || os.Getenv("GITHUB_OWNER") != "" || yamlFileHasGithubSection {
		if cfg.GitHub.Token == "" {
			return nil, fmt.Errorf("GitHub token is not configured. Please set it in %s or via GITHUB_TOKEN environment variable", configFilePath)
		}
		if cfg.GitHub.Owner == "" {
			return nil, fmt.Errorf("GitHub owner is not configured. Please set it in %s or via GITHUB_OWNER environment variable", configFilePath)
		}
	}

	// 校验必要的 Supabase 配置是否存在
	yamlFileHasSupabaseSection := false
	if file != nil {
		var rawYaml map[string]any
		if yaml.Unmarshal(file, &rawYaml) == nil {
			_, yamlFileHasSupabaseSection = rawYaml["supabase"]
		}
	}
	if os.Getenv("SUPABASE_URL") != "" || os.Getenv("SUPABASE_SERVICE_KEY") != "" || os.Getenv("SUPABASE_EDGE_FUNCTIONS_TOKEN") != "" || yamlFileHasSupabaseSection {
		if cfg.Supabase.URL == "" {
			return nil, fmt.Errorf("supabase URL is not configured. Please set it in %s or via SUPABASE_URL environment variable", configFilePath)
		}
		if cfg.Supabase.ServiceKey == "" {
			return nil, fmt.Errorf("supabase Service Key is not configured. Please set it in %s or via SUPABASE_SERVICE_KEY environment variable", configFilePath)
		}
	}

	// 校验必要的 Cloudflare 配置是否存在
	yamlFileHasCloudflareSection := false
	if file != nil {
		var rawYaml map[string]any
		if yaml.Unmarshal(file, &rawYaml) == nil {
			_, yamlFileHasCloudflareSection = rawYaml["cloudflare"]
		}
	}
	if os.Getenv("CLOUDFLARE_API_KEY") != "" || os.Getenv("CLOUDFLARE_API_EMAIL") != "" || os.Getenv("CLOUDFLARE_ACCOUNT_ID") != "" || yamlFileHasCloudflareSection {
		if cfg.Cloudflare.APIKey == "" {
			return nil, fmt.Errorf("Cloudflare API Key is not configured. Please set it in %s or via CLOUDFLARE_API_KEY environment variable", configFilePath)
		}
		if cfg.Cloudflare.APIEmail == "" {
			return nil, fmt.Errorf("Cloudflare API Email is not configured. Please set it in %s or via CLOUDFLARE_API_EMAIL environment variable", configFilePath)
		}
		if cfg.Cloudflare.AccountID == "" {
			return nil, fmt.Errorf("Cloudflare Account ID is not configured. Please set it in %s or via CLOUDFLARE_ACCOUNT_ID environment variable", configFilePath)
		}
	}

	// 校验必要的 Fly.io 配置是否存在
	yamlFileHasFlySection := false
	if file != nil {
		var rawYaml map[string]any
		if yaml.Unmarshal(file, &rawYaml) == nil {
			_, yamlFileHasFlySection = rawYaml["fly"]
		}
	}
	if os.Getenv("FLY_API_TOKEN") != "" || os.Getenv("FLY_ORG_SLUG") != "" || yamlFileHasFlySection {
		if cfg.Fly.APIToken == "" {
			return nil, fmt.Errorf("Fly API Token is not configured. Please set it in %s or via FLY_API_TOKEN environment variable", configFilePath)
		}
		if cfg.Fly.OrgSlug == "" {
			return nil, fmt.Errorf("Fly Org Slug is not configured. Please set it in %s or via FLY_ORG_SLUG environment variable", configFilePath)
		}
	}

	return cfg, nil
}

// FindProjectRoot 查找项目的根目录。
// 优先使用 PROJECT_ROOT 环境变量。如果未设置，则向上查找 conf 文件夹。
func FindProjectRoot() (string, error) {
	// 1. 检查 PROJECT_ROOT 环境变量
	if projectRootEnv := os.Getenv("PROJECT_ROOT"); projectRootEnv != "" {
		// 验证环境变量指向的路径是否有效且包含 conf 文件夹
		confPath := filepath.Join(projectRootEnv, "conf")
		info, err := os.Stat(confPath)
		if err != nil {
			if os.IsNotExist(err) {
				return "", fmt.Errorf("PROJECT_ROOT environment variable is set to '%s', but 'conf' directory does not exist at this path", projectRootEnv)
			} else {
				return "", fmt.Errorf("PROJECT_ROOT environment variable is set to '%s', error accessing 'conf' directory: %w", projectRootEnv, err)
			}
		}
		if !info.IsDir() {
			return "", fmt.Errorf("PROJECT_ROOT environment variable is set to '%s', but 'conf' at this path is not a directory", projectRootEnv)
		}
		return projectRootEnv, nil // 环境变量有效且找到了 conf 文件夹
	}

	// 2. 如果环境变量未设置，则回退到查找 conf 文件夹 (适用于本地开发或环境变量未指定的情况)
	dir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to get current working directory: %w", err)
	}

	// 最多向上查找10层，避免无限循环或扫描整个文件系统
	for i := 0; i < 10; i++ {
		confPath := filepath.Join(dir, "conf")
		info, err := os.Stat(confPath)
		if err == nil && info.IsDir() {
			return dir, nil // Found conf directory
		}

		parent := filepath.Dir(dir)
		if parent == dir { // Reached the root directory
			break
		}
		dir = parent
	}

	return "", errors.New("project root could not be determined: PROJECT_ROOT env var not set (or 'conf' dir not found there) and 'conf' directory not found in parent directories of current working dir")
}

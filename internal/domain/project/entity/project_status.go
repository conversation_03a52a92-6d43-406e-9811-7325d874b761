package entity

import "time"

// ProjectStatus 代表项目的当前状态和相关信息。
// 对应数据库中的 public.project_status 表。
// 注意: ProjectID 也是此表的主键。
type ProjectStatus struct {
	ProjectID     string    `json:"project_id"`               // 项目ID (主键和外键)
	Status        string    `json:"status"`                   // 项目状态 ('started', 'in_progress', 'done')
	PreviewLink   string    `json:"preview_link,omitempty"`   // 预览链接
	PublishLink   string    `json:"publish_link,omitempty"`   // 发布链接
	SiteThumbnail string    `json:"site_thumbnail,omitempty"` // 站点缩略图链接
	ErrorCode     string    `json:"error_code,omitempty"`     // 错误代码
	ErrorMsg      string    `json:"error_msg,omitempty"`      // 错误信息
	CreatedAt     time.Time `json:"created_at,omitempty"`
	UpdatedAt     time.Time `json:"updated_at,omitempty"`
}

// 定义合法的项目状态常量
const (
	ProjectStatusStarted    = "started"
	ProjectStatusInProgress = "in_progress"
	ProjectStatusDone       = "done"
)

package entity

import "time"

// Project 代表一个用户的项目。
// 对应数据库中的 public.project 表。
type Project struct {
	ID                string    `json:"id"`                            // 项目唯一标识符 (主键)
	UserID            string    `json:"user_id"`                       // 项目所有者用户ID
	Title             string    `json:"title"`                         // 项目名称
	IsPublished       bool      `json:"is_published"`                  // 项目是否已发布
	SupabaseProjectID string    `json:"supabase_project_id,omitempty"` // 关联的Supabase项目ID
	RepoID            string    `json:"repo_id"`                       // GitHub 仓库地址/ID
	NetlifySiteID     string    `json:"netlify_site_id,omitempty"`     // Netlify 站点 ID
	CreatedAt         time.Time `json:"created_at,omitempty"`
	UpdatedAt         time.Time `json:"updated_at,omitempty"`
}

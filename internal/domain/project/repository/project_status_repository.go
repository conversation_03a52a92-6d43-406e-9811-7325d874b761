package repository

import (
	"context"

	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
)

// ProjectStatusRepository 定义了项目状态数据的存储和检索操作。
// 根据DDD原则，接口位于domain层，实现在infrastructure层。
type ProjectStatusRepository interface {
	// CreateOrUpdate 保存或更新项目的状态信息。
	// 由于 project_id 是主键，如果记录已存在则更新，否则创建。
	CreateOrUpdate(ctx context.Context, projectStatus *entity.ProjectStatus) error

	// GetByProjectID 根据项目ID检索其状态信息。
	GetByProjectID(ctx context.Context, projectID string) (*entity.ProjectStatus, error)

	// DeleteByProjectID 根据项目ID删除项目状态 (通常在项目被删除时级联删除)。
	// 但也可以提供一个单独的删除方法，以防特定场景需要。
	DeleteByProjectID(ctx context.Context, projectID string) error

	// GetByNetlifySiteID 根据 Netlify 站点 ID 检索项目状态信息。
	// 它首先从 project 表中找到 project_id，然后查询 project_status 表。
	GetByNetlifySiteID(ctx context.Context, netlifySiteID string) (*entity.ProjectStatus, error)

	// UpdateByNetlifySiteID 根据 Netlify 站点 ID 更新项目状态信息。
	// 它首先从 project 表中找到 project_id，然后更新 project_status 表中对应的记录。
	// updates 参数是一个 map，其中键是数据库列名，值是新的列值。
	UpdateByNetlifySiteID(ctx context.Context, netlifySiteID string, updates map[string]any) (*entity.ProjectStatus, error)

	// UpdateByProjectID 根据项目 ID 更新项目状态信息。
	// updates 参数是一个 map，其中键是数据库列名，值是新的列值。
	UpdateByProjectID(ctx context.Context, projectID string, updates map[string]any) (*entity.ProjectStatus, error)
}

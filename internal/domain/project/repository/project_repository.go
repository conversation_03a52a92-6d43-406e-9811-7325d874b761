package repository

import (
	"context"

	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
)

// ProjectRepository 定义了项目数据的存储和检索操作。
// 根据DDD原则，接口位于domain层，实现在infrastructure层。
type ProjectRepository interface {
	// Create 保存一个新的项目到数据库。
	Create(ctx context.Context, project *entity.Project) error

	// GetByID 根据项目ID检索项目。
	GetByID(ctx context.Context, id string) (*entity.Project, error)

	// GetByUserID 根据用户ID检索该用户的所有项目。
	// 可以考虑添加分页等参数。
	GetByUserID(ctx context.Context, userID string) ([]*entity.Project, error)

	// Update 更新现有项目的信息。
	Update(ctx context.Context, project *entity.Project) error

	// Delete 根据项目ID删除项目 (通常是软删除)。
	Delete(ctx context.Context, id string) error

	// UpdateFields 根据项目ID更新项目的部分字段。
	// updates 参数是一个 map，其中键是数据库列名（对应 entity.Project 中的JSON标签），值是新的列值。
	// 返回更新后的项目实体。
	UpdateFields(ctx context.Context, projectID string, updates map[string]any) (*entity.Project, error)

	// GetByNetlifySiteID 根据 Netlify 站点 ID 检索项目。
	GetByNetlifySiteID(ctx context.Context, netlifySiteID string) (*entity.Project, error)

	// TODO: 根据实际需求可以添加更多方法，例如：
	// List(ctx context.Context, filters map[string]any, limit, offset int) ([]*entity.Project, error)
	// Count(ctx context.Context, filters map[string]any) (int, error)
}

package repository

import (
	"context"

	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
)

// AgentRepository 定义 agent 连接的管理接口
// 负责 agent 的注册、注销、查找、命令下发等操作

type AgentRepository interface {
	// Register 注册一个新的 agent 连接
	Register(ctx context.Context, agent *entity.Agent) error

	// Unregister 注销一个 agent 连接
	Unregister(ctx context.Context, agentID string) error

	// GetByID 根据 ID 获取 agent 实体
	GetByID(ctx context.Context, id string) (*entity.Agent, error)

	// UpdateLastActive 更新 agent 的最后活跃时间
	UpdateLastActive(ctx context.Context, id string, lastActive int64) error

	// ListAgents 获取所有已注册的 agent
	ListAgents(ctx context.Context) ([]*entity.Agent, error)

	// SendCommand 向指定 agent 发送命令
	SendCommand(ctx context.Context, agentID string, cmd *entity.Command) error

	// SendCommandAndWait 向指定 agent 发送命令并等待响应
	SendCommandAndWait(ctx context.Context, agentID string, cmd *entity.Command) ([]byte, error)
}

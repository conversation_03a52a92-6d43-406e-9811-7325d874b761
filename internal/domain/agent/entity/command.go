package entity

// Command 表示从服务器下发给 agent 的命令
// Type 字段支持 "update_code"、"restart_app"、"get_status"、"clone_repo"、"run_vite"、"pnpm_install"
// Data 字段为通用数据字段

type Command struct {
	Type string      `json:"type"` // 命令类型
	Data interface{} `json:"data"` // 通用数据字段
}

// Command types
const (
	CommandTypeCloneRepo  = "clone_repo"
	CommandTypeRunVite    = "run_vite"
	CommandTypeUpdateCode = "update_code"
	CommandTypePublish    = "publish"
)

// NewCloneRepoCommand 创建一个新的克隆仓库命令
func NewCloneRepoCommand(repoID string) Command {
	return Command{
		Type: CommandTypeCloneRepo,
		Data: repoID,
	}
}

// NewUpdateCodeCommand 创建一个新的更新代码命令
func NewUpdateCodeCommand() Command {
	return Command{
		Type: CommandTypeUpdateCode,
		Data: nil,
	}
}

// NewRunViteCommand 创建一个新的运行 vite 命令
func NewRunViteCommand() Command {
	return Command{
		Type: CommandTypeRunVite,
		Data: nil,
	}
}

// NewHealthCheckCommand 创建一个新的 health_check 命令
func NewHealthCheckCommand() Command {
	return Command{
		Type: "health_check",
	}
}

// NewPublishCommand 创建一个新的发布命令
func NewPublishCommand() Command {
	return Command{
		Type: CommandTypePublish,
		Data: nil,
	}
}

# Fly.io Infrastructure Layer

本目录包含了与 Fly.io API 交互的基础设施层代码，遵循 DDD 架构模式，提供了完整的 Fly.io 服务集成功能。

## 目录结构

```
fly/
├── client.go      # 核心客户端和通用请求处理
├── app.go         # 应用管理服务
└── README.md      # 本文档
```

## 核心架构设计

### 1. 客户端设计模式

采用**组合模式**和**选项模式**设计客户端：

```go
// 选项模式配置客户端
client, err := NewClient(cfg, 
    WithHTTPClient(customHTTPClient)
)

// 服务组合模式
service := &Service{
    client: client,
    Apps:   newAppService(client),
}
```

### 2. 服务层设计

每个服务都嵌入 `service` 结构体，共享底层客户端：

```go
type AppService struct {
    common service // 嵌入通用服务，包含 client 引用
}

type service struct {
    client *Client
}
```

## 文件功能说明

### client.go - 核心客户端
- **功能**: 提供统一的 HTTP 客户端和请求处理
- **核心组件**:
  - `Client`: 主客户端结构体
  - `APIError`: 统一的错误处理
  - `makeRequest`: 通用请求方法
  - `Service`: 服务聚合器

**设计特点**:
- Base URL 写死为 `https://api.machines.dev/v1`
- 所有请求都必须在 HTTP 标头中包含 API 令牌：`Authorization: Bearer <fly_api_token>`
- API Token 和 Org Slug 从配置中自动获取
- Org Slug 作为公开字段供其他服务直接访问
- 统一的错误处理和日志记录
- 支持 204 No Content 响应

### app.go - 应用管理
- **功能**: 管理 Fly.io 应用的查询和操作
- **核心方法**:
  - `ListApps`: 列出指定组织的所有应用

**设计特点**:
- 通过 service 层访问客户端的 Org Slug 字段
- 完整的应用信息模型
- 灵活的查询参数支持

## 代码风格特点

### 1. 错误处理
- 统一的 `APIError` 结构体
- 详细的错误日志记录
- 优雅的错误包装和传播

```go
if err != nil {
    logger.Error("Failed to list Fly.io apps", "OrgSlug", orgSlug, "Error", err)
    return nil, fmt.Errorf("failed to send list_apps request: %w", err)
}
```

### 2. 日志记录
- 使用结构化日志
- 英文日志消息（符合项目规范）
- 详细的调试信息

```go
logger.Info("Listing Fly.io apps", "OrgSlug", orgSlug)
logger.Debug("Fly.io request", "Method", method, "URL", fullURL.String())
```

### 3. 参数验证
- 严格的空值检查
- 清晰的错误消息
- 防御性编程

```go
if orgSlug == "" {
    return nil, fmt.Errorf("org slug cannot be empty for listing apps")
}
```

### 4. 类型安全
- 强类型的数据结构
- JSON 标签映射
- 可选字段使用 `omitempty`

### 5. 上下文支持
- 所有方法都支持 `context.Context`
- 支持超时和取消

## 使用示例

```go
// 加载配置
cfg, err := config.LoadConfig("dev")
if err != nil {
    log.Fatal(err)
}

// 创建服务实例
flyService, err := fly.NewService(cfg)
if err != nil {
    log.Fatal(err)
}

// 列出应用
apps, err := flyService.Apps.ListApps(ctx)
if err != nil {
    log.Fatal(err)
}

for _, app := range apps {
    fmt.Printf("App: %s (ID: %s, Status: %s)\n", app.Name, app.ID, app.Status)
}
```

## 配置要求

在配置文件中需要设置以下 Fly.io 相关配置：

```yaml
fly:
  api_token: "your_fly_api_token"
  org_slug: "your_fly_org_slug"
```

或者通过环境变量设置：

```bash
export FLY_API_TOKEN="your_fly_api_token"
export FLY_ORG_SLUG="your_fly_org_slug"
```

## 设计原则

1. **单一职责**: 每个服务只负责特定的功能领域
2. **依赖注入**: 通过构造函数注入依赖
3. **接口隔离**: 每个服务都有明确的职责边界
4. **错误处理**: 统一的错误处理策略
5. **可测试性**: 支持自定义 HTTP 客户端
6. **可扩展性**: 易于添加新的 API 端点和服务

## 注意事项

1. **API 令牌**: 所有请求都需要有效的 Fly.io API 令牌
2. **组织标识**: 所有操作都需要有效的组织标识符 (Org Slug)
3. **错误重试**: 当前实现没有自动重试机制，需要在上层应用处理
4. **并发安全**: 客户端实例不是并发安全的，需要为每个 goroutine 创建独立的实例 
package fly

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// VolumeService 提供了操作 Fly.io Volume 的相关方法
// 参考 AppService 设计
type VolumeService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newVolumeService 创建一个新的 VolumeService 实例
func newVolumeService(client *Client) *VolumeService {
	return &VolumeService{common: service{client: client}}
}

// Volume 表示卷的详细信息
// 结构体字段参考 OpenAPI Volume schema
// 只列出常用字段，后续可补充
// id, name, region, size_gb, state, created_at, ...
type Volume struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Region            string `json:"region"`
	SizeGB            int    `json:"size_gb"`
	State             string `json:"state"`
	CreatedAt         string `json:"created_at"`
	AutoBackupEnabled bool   `json:"auto_backup_enabled,omitempty"`
	Encrypted         bool   `json:"encrypted,omitempty"`
	Fstype            string `json:"fstype,omitempty"`
	HostStatus        string `json:"host_status,omitempty"`
	SnapshotRetention int    `json:"snapshot_retention,omitempty"`
	Zone              string `json:"zone,omitempty"`
}

// CreateVolumeRequest 创建卷的请求体
// 参考 OpenAPI CreateVolumeRequest
// 只列出常用字段
// name, region, size_gb, encrypted, fstype, ...
type CreateVolumeRequest struct {
	Name              string `json:"name"`
	Region            string `json:"region"`
	SizeGB            int    `json:"size_gb"`
	Encrypted         bool   `json:"encrypted,omitempty"`
	Fstype            string `json:"fstype,omitempty"`
	RequireUniqueZone bool   `json:"require_unique_zone,omitempty"`
	SnapshotID        string `json:"snapshot_id,omitempty"`
	SnapshotRetention int    `json:"snapshot_retention,omitempty"`
	SourceVolumeID    string `json:"source_volume_id,omitempty"`
	UniqueZoneAppWide bool   `json:"unique_zone_app_wide,omitempty"`
}

// UpdateVolumeRequest 更新卷的请求体
// 参考 OpenAPI UpdateVolumeRequest
// auto_backup_enabled, snapshot_retention
type UpdateVolumeRequest struct {
	AutoBackupEnabled bool `json:"auto_backup_enabled,omitempty"`
	SnapshotRetention int  `json:"snapshot_retention,omitempty"`
}

// ExtendVolumeRequest 扩容卷的请求体
// 参考 OpenAPI ExtendVolumeRequest
// size_gb
type ExtendVolumeRequest struct {
	SizeGB int `json:"size_gb"`
}

// ExtendVolumeResponse 扩容卷的响应体
// 参考 OpenAPI ExtendVolumeResponse
// needs_restart, volume
type ExtendVolumeResponse struct {
	NeedsRestart bool   `json:"needs_restart"`
	Volume       Volume `json:"volume"`
}

// VolumeSnapshot 表示卷快照
// 参考 OpenAPI VolumeSnapshot
// id, created_at, size, status, ...
type VolumeSnapshot struct {
	ID            string `json:"id"`
	CreatedAt     string `json:"created_at"`
	Digest        string `json:"digest,omitempty"`
	RetentionDays int    `json:"retention_days,omitempty"`
	Size          int    `json:"size,omitempty"`
	Status        string `json:"status,omitempty"`
}

// ListVolumes 列出指定应用下的所有卷
// API文档: GET /apps/{app_name}/volumes
func (s *VolumeService) ListVolumes(ctx context.Context, appName string, summary bool) ([]*Volume, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for list volumes")
	}
	logger.Info("Listing Fly.io volumes", "AppName", appName, "Summary", summary)
	path := fmt.Sprintf("apps/%s/volumes", appName)
	if summary {
		path += "?summary=true"
	}
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to list Fly.io volumes", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to list volumes: %w", err)
	}
	var volumes []*Volume
	if err := json.Unmarshal(respBody, &volumes); err != nil {
		logger.Error("Failed to unmarshal Fly.io volumes response", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal list_volumes response: %w", err)
	}
	logger.Info("Successfully listed Fly.io volumes", "AppName", appName, "Count", len(volumes))
	return volumes, nil
}

// CreateVolume 创建一个新卷
// API文档: POST /apps/{app_name}/volumes
func (s *VolumeService) CreateVolume(ctx context.Context, appName string, req *CreateVolumeRequest) (*Volume, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for create volume")
	}
	logger.Info("Creating Fly.io volume", "AppName", appName, "VolumeName", req.Name)
	path := fmt.Sprintf("apps/%s/volumes", appName)
	respBody, err := s.common.client.makeRequest(ctx, "POST", path, req)
	if err != nil {
		logger.Error("Failed to create Fly.io volume", "AppName", appName, "VolumeName", req.Name, "Error", err)
		return nil, fmt.Errorf("failed to create volume: %w", err)
	}
	var volume Volume
	if err := json.Unmarshal(respBody, &volume); err != nil {
		logger.Error("Failed to unmarshal Fly.io volume response", "AppName", appName, "VolumeName", req.Name, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal create_volume response: %w", err)
	}
	logger.Info("Successfully created Fly.io volume", "AppName", appName, "VolumeID", volume.ID)
	return &volume, nil
}

// GetVolume 获取卷详情
// API文档: GET /apps/{app_name}/volumes/{volume_id}
func (s *VolumeService) GetVolume(ctx context.Context, appName, volumeID string) (*Volume, error) {
	if appName == "" || volumeID == "" {
		return nil, fmt.Errorf("app name and volume id cannot be empty for get volume")
	}
	logger.Info("Getting Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s", appName, volumeID)
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to get Fly.io volume", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to get volume: %w", err)
	}
	var volume Volume
	if err := json.Unmarshal(respBody, &volume); err != nil {
		logger.Error("Failed to unmarshal Fly.io volume response", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal get_volume response: %w", err)
	}
	logger.Info("Successfully got Fly.io volume", "AppName", appName, "VolumeID", volume.ID)
	return &volume, nil
}

// UpdateVolume 更新卷配置
// API文档: PUT /apps/{app_name}/volumes/{volume_id}
func (s *VolumeService) UpdateVolume(ctx context.Context, appName, volumeID string, req *UpdateVolumeRequest) (*Volume, error) {
	if appName == "" || volumeID == "" {
		return nil, fmt.Errorf("app name and volume id cannot be empty for update volume")
	}
	logger.Info("Updating Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s", appName, volumeID)
	respBody, err := s.common.client.makeRequest(ctx, "PUT", path, req)
	if err != nil {
		logger.Error("Failed to update Fly.io volume", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to update volume: %w", err)
	}
	var volume Volume
	if err := json.Unmarshal(respBody, &volume); err != nil {
		logger.Error("Failed to unmarshal Fly.io volume response", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal update_volume response: %w", err)
	}
	logger.Info("Successfully updated Fly.io volume", "AppName", appName, "VolumeID", volume.ID)
	return &volume, nil
}

// DeleteVolume 删除卷
// API文档: DELETE /apps/{app_name}/volumes/{volume_id}
func (s *VolumeService) DeleteVolume(ctx context.Context, appName, volumeID string) error {
	if appName == "" || volumeID == "" {
		return fmt.Errorf("app name and volume id cannot be empty for delete volume")
	}
	logger.Info("Deleting Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s", appName, volumeID)
	_, err := s.common.client.makeRequest(ctx, "DELETE", path, nil)
	if err != nil {
		logger.Error("Failed to delete Fly.io volume", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return fmt.Errorf("failed to delete volume: %w", err)
	}
	logger.Info("Successfully deleted Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	return nil
}

// ExtendVolume 扩容卷
// API文档: PUT /apps/{app_name}/volumes/{volume_id}/extend
func (s *VolumeService) ExtendVolume(ctx context.Context, appName, volumeID string, req *ExtendVolumeRequest) (*ExtendVolumeResponse, error) {
	if appName == "" || volumeID == "" {
		return nil, fmt.Errorf("app name and volume id cannot be empty for extend volume")
	}
	logger.Info("Extending Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s/extend", appName, volumeID)
	respBody, err := s.common.client.makeRequest(ctx, "PUT", path, req)
	if err != nil {
		logger.Error("Failed to extend Fly.io volume", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to extend volume: %w", err)
	}
	var resp ExtendVolumeResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		logger.Error("Failed to unmarshal Fly.io extend volume response", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal extend_volume response: %w", err)
	}
	logger.Info("Successfully extended Fly.io volume", "AppName", appName, "VolumeID", volumeID)
	return &resp, nil
}

// ListSnapshots 列出卷的所有快照
// API文档: GET /apps/{app_name}/volumes/{volume_id}/snapshots
func (s *VolumeService) ListSnapshots(ctx context.Context, appName, volumeID string) ([]*VolumeSnapshot, error) {
	if appName == "" || volumeID == "" {
		return nil, fmt.Errorf("app name and volume id cannot be empty for list snapshots")
	}
	logger.Info("Listing Fly.io volume snapshots", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s/snapshots", appName, volumeID)
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to list Fly.io volume snapshots", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to list volume snapshots: %w", err)
	}
	var snapshots []*VolumeSnapshot
	if err := json.Unmarshal(respBody, &snapshots); err != nil {
		logger.Error("Failed to unmarshal Fly.io volume snapshots response", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal list_snapshots response: %w", err)
	}
	logger.Info("Successfully listed Fly.io volume snapshots", "AppName", appName, "VolumeID", volumeID, "Count", len(snapshots))
	return snapshots, nil
}

// CreateSnapshot 创建卷快照
// API文档: POST /apps/{app_name}/volumes/{volume_id}/snapshots
func (s *VolumeService) CreateSnapshot(ctx context.Context, appName, volumeID string) error {
	if appName == "" || volumeID == "" {
		return fmt.Errorf("app name and volume id cannot be empty for create snapshot")
	}
	logger.Info("Creating Fly.io volume snapshot", "AppName", appName, "VolumeID", volumeID)
	path := fmt.Sprintf("apps/%s/volumes/%s/snapshots", appName, volumeID)
	_, err := s.common.client.makeRequest(ctx, "POST", path, nil)
	if err != nil {
		logger.Error("Failed to create Fly.io volume snapshot", "AppName", appName, "VolumeID", volumeID, "Error", err)
		return fmt.Errorf("failed to create volume snapshot: %w", err)
	}
	logger.Info("Successfully created Fly.io volume snapshot", "AppName", appName, "VolumeID", volumeID)
	return nil
}

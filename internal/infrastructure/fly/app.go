package fly

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// AppService 提供了操作 Fly.io 应用的相关方法
type AppService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newAppService 创建一个新的 AppService 实例
func newAppService(client *Client) *AppService {
	return &AppService{common: service{client: client}}
}

// Organization 表示应用所属组织
// 对应 GetApp 响应中的 organization 字段
// name: string, slug: string
type Organization struct {
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// ListApp 表示 ListApps 响应中的单个应用
// id(string), name(string), machine_count(int), network(object)
type ListApp struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	MachineCount int             `json:"machine_count"`
	Network      json.RawMessage `json:"network"`
}

// ListAppsResponse 表示 ListApps API 的响应
type ListAppsResponse struct {
	Apps      []*ListApp `json:"apps"`
	TotalApps int        `json:"total_apps"`
}

// GetAppResponse 表示 GetApp API 的响应
// id(string), name(string), organization(object), status(string)
type GetAppResponse struct {
	ID           string        `json:"id"`
	Name         string        `json:"name"`
	Organization *Organization `json:"organization"`
	Status       string        `json:"status"`
}

// ListApps 列出指定组织的所有应用
// API 文档: GET /apps?org_slug={org_slug}
func (s *AppService) ListApps(ctx context.Context) (*ListAppsResponse, error) {
	orgSlug := s.common.client.OrgSlug
	if orgSlug == "" {
		return nil, fmt.Errorf("org slug cannot be empty for listing apps")
	}

	logger.Info("Listing Fly.io apps", "OrgSlug", orgSlug)

	// 构建请求路径，包含 org_slug 查询参数
	path := fmt.Sprintf("apps?org_slug=%s", orgSlug)

	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to list Fly.io apps", "OrgSlug", orgSlug, "Error", err)
		return nil, fmt.Errorf("failed to send list_apps request: %w", err)
	}

	var response ListAppsResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		logger.Error("Failed to unmarshal Fly.io apps response", "OrgSlug", orgSlug, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal list_apps response: %w", err)
	}

	logger.Info("Successfully listed Fly.io apps", "OrgSlug", orgSlug, "Count", len(response.Apps), "TotalApps", response.TotalApps)
	return &response, nil
}

// CreateAppRequest 创建应用的请求体
// 只包含API定义的字段
// org_slug 必须，app_name可选，enable_subdomains可选，network可选
// network 字段类型为 string（API定义如此）
type CreateAppRequest struct {
	AppName          string `json:"app_name,omitempty"`
	EnableSubdomains bool   `json:"enable_subdomains,omitempty"`
	Network          string `json:"network,omitempty"`
}

// CreateApp 创建一个新应用
// API文档: POST /apps
func (s *AppService) CreateApp(ctx context.Context, req *CreateAppRequest) error {
	orgSlug := s.common.client.OrgSlug
	logger.Info("Creating Fly.io app", "OrgSlug", orgSlug, "AppName", req.AppName)
	reqMap := map[string]interface{}{
		"app_name":          req.AppName,
		"enable_subdomains": req.EnableSubdomains,
		"network":           req.Network,
		"org_slug":          orgSlug,
	}
	respBody, err := s.common.client.makeRequest(ctx, "POST", "apps", reqMap)
	if err != nil {
		logger.Error("Failed to create Fly.io app", "OrgSlug", orgSlug, "AppName", req.AppName, "Error", err)
		return fmt.Errorf("failed to create app: %w", err)
	}
	logger.Info("Successfully created Fly.io app", "OrgSlug", orgSlug, "AppName", req.AppName, "Response", string(respBody))
	return nil
}

// GetApp 获取应用详情
// API文档: GET /apps/{app_name}
func (s *AppService) GetApp(ctx context.Context, appName string) (*GetAppResponse, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for get app")
	}
	logger.Info("Getting Fly.io app", "AppName", appName)
	path := fmt.Sprintf("apps/%s", appName)
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to get Fly.io app", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to get app: %w", err)
	}
	var app GetAppResponse
	if err := json.Unmarshal(respBody, &app); err != nil {
		logger.Error("Failed to unmarshal Fly.io app response", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal get_app response: %w", err)
	}
	logger.Info("Successfully got Fly.io app", "AppName", appName, "ID", app.ID)
	return &app, nil
}

// DeleteApp 删除应用
// API文档: DELETE /apps/{app_name}
func (s *AppService) DeleteApp(ctx context.Context, appName string) error {
	if appName == "" {
		return fmt.Errorf("app name cannot be empty for delete app")
	}
	logger.Info("Deleting Fly.io app", "AppName", appName)
	path := fmt.Sprintf("apps/%s", appName)
	_, err := s.common.client.makeRequest(ctx, "DELETE", path, nil)
	if err != nil {
		logger.Error("Failed to delete Fly.io app", "AppName", appName, "Error", err)
		return fmt.Errorf("failed to delete app: %w", err)
	}
	logger.Info("Successfully deleted Fly.io app", "AppName", appName)
	return nil
}

// CreateAppDeployTokenRequest 创建deploy token的请求体
// 只包含API定义的字段
// expiry可选
// API文档: POST /apps/{app_name}/deploy_token
type CreateAppDeployTokenRequest struct {
	Expiry string `json:"expiry,omitempty"`
}

// CreateAppDeployTokenResponse 创建deploy token的响应体
type CreateAppDeployTokenResponse struct {
	Token string `json:"token"`
}

// CreateAppDeployToken 创建应用deploy token
// API文档: POST /apps/{app_name}/deploy_token
func (s *AppService) CreateAppDeployToken(ctx context.Context, appName string, req *CreateAppDeployTokenRequest) (*CreateAppDeployTokenResponse, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for create deploy token")
	}
	logger.Info("Creating Fly.io app deploy token", "AppName", appName)
	path := fmt.Sprintf("apps/%s/deploy_token", appName)
	respBody, err := s.common.client.makeRequest(ctx, "POST", path, req)
	if err != nil {
		logger.Error("Failed to create Fly.io app deploy token", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to create app deploy token: %w", err)
	}
	var resp CreateAppDeployTokenResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		logger.Error("Failed to unmarshal Fly.io app deploy token response", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal create_app_deploy_token response: %w", err)
	}
	logger.Info("Successfully created Fly.io app deploy token", "AppName", appName)
	return &resp, nil
}

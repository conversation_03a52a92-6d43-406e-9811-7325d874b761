package fly

import (
	"context"
	"fmt"

	"github.com/hasura/go-graphql-client"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// IPAddressService 提供 IP 地址分配相关的功能
type IPAddressService struct {
	graphqlClient *graphql.Client
}

// newIPAddressService 创建并返回一个新的 IPAddressService 实例
func newIPAddressService(graphqlClient *graphql.Client) *IPAddressService {
	return &IPAddressService{
		graphqlClient: graphqlClient,
	}
}

// AllocateIPAddressInput 表示分配 IP 地址的输入参数
type AllocateIPAddressInput struct {
	AppID string `json:"appId"`
	Type  string `json:"type"`
}

// AllocateIPAddressResponse 表示分配 IP 地址的响应
type AllocateIPAddressResponse struct {
	AllocateIPAddress struct {
		IPAddress struct {
			Address string `json:"address"`
		} `json:"ipAddress"`
	} `json:"allocateIpAddress" graphql:"allocateIpAddress(input: $input)"`
}

// AllocateIPAddress 使用 GraphQL 分配 IP 地址
// appId: 应用 ID
// ipType: IP 类型（如 "shared_v4"）
// 返回分配的 IP 地址和错误
func (s *IPAddressService) AllocateIPAddress(ctx context.Context, appId, ipType string) (string, error) {
	logger.Info("Allocating IP address", "AppID", appId, "Type", ipType)

	// 构建变量
	variables := map[string]interface{}{
		"input": AllocateIPAddressInput{
			AppID: appId,
			Type:  ipType,
		},
	}

	// 执行 GraphQL 请求
	var response AllocateIPAddressResponse
	err := s.graphqlClient.Mutate(ctx, &response, variables)

	if err != nil {
		logger.Error("Failed to allocate IP address", "AppID", appId, "Type", ipType, "Error", err)
		return "", fmt.Errorf("failed to allocate IP address: %w", err)
	}

	ipAddress := response.AllocateIPAddress.IPAddress.Address
	logger.Info("Successfully allocated IP address", "AppID", appId, "Type", ipType, "IPAddress", ipAddress)
	return ipAddress, nil
}

package fly

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/hasura/go-graphql-client"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/retry"
)

const (
	// BaseURL 是 Fly.io API 的基础 URL，写死为 https://api.machines.dev/v1/
	BaseURL = "https://api.machines.dev/v1/"
	// GraphQLURL 是 Fly.io GraphQL API 的 URL
	GraphQLURL     = "https://api.fly.io/graphql"
	defaultTimeout = 60 * time.Second
)

// APIError 表示 Fly.io API 返回的错误
type APIError struct {
	StatusCode   int    `json:"-"` // HTTP 状态码
	ErrorMessage string `json:"error,omitempty"`
	Code         any    `json:"code,omitempty"` // Fly.io 特有的错误码
}

func (e *APIError) Error() string {
	return fmt.Sprintf("Fly.io API error (status %d): %s (code: %v)", e.StatusCode, e.ErrorMessage, e.Code)
}

// ClientOption 定义了用于配置 Fly.io 客户端的选项函数类型
type ClientOption func(*Client)

// WithHTTPClient 允许用户提供自定义的 http.Client
func WithHTTPClient(httpClient *http.Client) ClientOption {
	return func(c *Client) {
		c.httpClient = httpClient
	}
}

// Client 是与 Fly.io API 交互的客户端
type Client struct {
	baseURL    *url.URL
	httpClient *http.Client
	apiToken   string
	OrgSlug    string // 改为公开字段，供其他服务直接访问
}

// NewClient 创建一个新的 Fly.io 客户端实例
// 从配置中自动获取 API Token 和 Org Slug
func NewClient(cfg *config.Config) (*Client, error) {
	if cfg.Fly.APIToken == "" {
		return nil, fmt.Errorf("fly API token cannot be empty")
	}
	if cfg.Fly.OrgSlug == "" {
		return nil, fmt.Errorf("fly org slug cannot be empty")
	}

	baseURL, err := url.Parse(BaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse base URL: %w", err)
	}

	c := &Client{
		baseURL:    baseURL,
		apiToken:   cfg.Fly.APIToken,
		OrgSlug:    cfg.Fly.OrgSlug,
		httpClient: &http.Client{Timeout: defaultTimeout},
	}
	return c, nil
}

// makeRequest 是一个通用的辅助函数，用于向 Fly.io API 发出 HTTP 请求
// 它处理 URL 构建、认证、JSON 序列化和错误处理
func (c *Client) makeRequest(ctx context.Context, method, path string, body any) (json.RawMessage, error) {
	retryConfig := retry.NetworkRetryConfig()

	requestFunc := func() (json.RawMessage, error) {
		cleanedPath := strings.TrimLeft(path, "/")
		rel, err := url.Parse(cleanedPath)
		if err != nil {
			return nil, fmt.Errorf("failed to parse relative path '%s': %w", cleanedPath, err)
		}

		fullURL := c.baseURL.ResolveReference(rel)

		var reqBody io.Reader
		if body != nil {
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal request body: %w", err)
			}
			reqBody = bytes.NewBuffer(jsonBody)
			logger.Debug("Fly.io request body", "Method", method, "URL", fullURL.String(), "Body", string(jsonBody))
		} else {
			logger.Debug("Fly.io request", "Method", method, "URL", fullURL.String())
		}

		req, err := http.NewRequestWithContext(ctx, method, fullURL.String(), reqBody)
		if err != nil {
			return nil, fmt.Errorf("failed to create HTTP request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+c.apiToken)
		if body != nil {
			req.Header.Set("Content-Type", "application/json")
		}

		resp, err := c.httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("failed to execute HTTP request: %w", err)
		}
		defer resp.Body.Close()

		respBodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response body: %w", err)
		}

		logger.Debug("Fly.io response", "Status", resp.Status, "BodyLength", len(respBodyBytes))
		if len(respBodyBytes) > 0 {
			logger.Debug("Fly.io response body (raw)", "Body", string(respBodyBytes))
		}

		if resp.StatusCode >= 400 {
			apiErr := &APIError{StatusCode: resp.StatusCode}
			if jsonErr := json.Unmarshal(respBodyBytes, apiErr); jsonErr != nil {
				apiErr.ErrorMessage = string(respBodyBytes)
				logger.Error("Failed to unmarshal Fly.io API error response, using raw body", "UnmarshalError", jsonErr, "RawBody", string(respBodyBytes))
			}
			logger.Error("Fly.io API error", "URL", fullURL.String(), "Status", resp.Status, "ErrorCode", apiErr.Code, "ErrorMessage", apiErr.ErrorMessage)
			return nil, apiErr
		}

		if resp.StatusCode == http.StatusNoContent && len(respBodyBytes) == 0 {
			return json.RawMessage("{}"), nil
		}

		return respBodyBytes, nil
	}

	return retry.DoWithResult(ctx, retryConfig, requestFunc)
}

// service 是一个嵌入式结构体，用于共享底层的 Fly.io Client 实例
type service struct {
	client *Client
}

// Service 是访问不同 Fly.io API 服务的主入口点
// 每个内嵌的服务都共享同一个底层 HTTP 客户端和认证信息
type Service struct {
	client *Client // 底层客户端，主要用于内部或直接调用 makeRequest

	Apps        *AppService
	Machines    *MachineService
	IPAddresses *IPAddressService // IP 地址分配服务
	Volumes     *VolumeService    // 卷服务
	GraphQL     *graphql.Client   // GraphQL 客户端
}

// NewService 创建并初始化所有 Fly.io API 服务
// cfg: 配置对象，包含 API Token 和 Org Slug
// opts: ClientOption，例如 WithHTTPClient
func NewService(cfg *config.Config) (*Service, error) {
	client, err := NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create fly.io base client: %w", err)
	}

	// 创建带有认证的 HTTP 客户端用于 GraphQL
	httpClient := &http.Client{
		Transport: &authTransport{
			token: client.apiToken,
			base:  http.DefaultTransport,
		},
		Timeout: client.httpClient.Timeout,
	}

	// 创建 GraphQL 客户端
	graphqlClient := graphql.NewClient(GraphQLURL, httpClient)

	s := &Service{
		client:  client,
		GraphQL: graphqlClient,
	}
	s.Apps = newAppService(client)
	s.Machines = newMachineService(client)
	s.IPAddresses = newIPAddressService(graphqlClient)
	s.Volumes = newVolumeService(client)
	return s, nil
}

// authTransport 是一个自定义的 HTTP 传输器，用于为 GraphQL 请求添加认证头
type authTransport struct {
	token string
	base  http.RoundTripper
}

// RoundTrip 实现 http.RoundTripper 接口，为请求添加认证头
func (t *authTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Set("Authorization", "Bearer "+t.token)
	return t.base.RoundTrip(req)
}

package supabase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	projectStatusTableName = "project_status" // Supabase中的project_status表名
	projectsTableName      = "project"        // Supabase中的project表名
)

// SupabaseProjectStatusRepository 是 ProjectStatusRepository 接口的Supabase实现。
type SupabaseProjectStatusRepository struct {
	client *Client
}

// NewSupabaseProjectStatusRepository 创建一个新的 SupabaseProjectStatusRepository 实例。
func NewSupabaseProjectStatusRepository(client *Client) repository.ProjectStatusRepository {
	if client == nil {
		logger.Fatal("Supabase client cannot be nil for ProjectStatusRepository")
		return nil
	}
	return &SupabaseProjectStatusRepository{client: client}
}

// CreateOrUpdate 保存或更新项目的状态信息。
// 使用 POST 和 Prefer: resolution=merge-duplicates 来实现 "upsert" 行为。
// Supabase PostgREST: POST /rest/v1/project_status
func (r *SupabaseProjectStatusRepository) CreateOrUpdate(ctx context.Context, projectStatus *entity.ProjectStatus) error {
	logger.Info("Creating or updating project status in Supabase", "ProjectID", projectStatus.ProjectID, "Status", projectStatus.Status)

	if projectStatus.ProjectID == "" {
		return fmt.Errorf("project ID cannot be empty for create/update status")
	}

	preferHeaders := []string{
		"return=representation",
		"resolution=merge-duplicates", // 如果 project_id (主键) 已存在，则合并（更新）记录
	}

	// PostgREST 的 merge-duplicates 通常用于 POST 请求，并且需要指定 on_conflict 参数。
	// 然而，对于 Supabase，有时可以直接通过 Prefer 头部和主键冲突来实现更新。
	// 如果这种简单方式无效，则需要明确指定 on_conflict 列，通常是主键：
	// queryParams := url.Values{}
	// queryParams.Set("on_conflict", "project_id")
	// _, _, err := r.client.makeRequest(ctx, http.MethodPost, projectStatusTableName, queryParams, preferHeaders, projectStatus)

	// 先尝试简单方式，只用 Prefer 头部
	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodPost, projectStatusTableName, nil, preferHeaders, projectStatus)
	if err != nil {
		logger.Error("Failed to create/update project status in Supabase", "Error", err, "ProjectID", projectStatus.ProjectID)
		return fmt.Errorf("supabase API error on create/update project status: %w", err)
	}

	var upsertedStatus []entity.ProjectStatus // PostgREST INSERT/UPSERT with return=representation returns an array
	if err := json.Unmarshal(rawResponse, &upsertedStatus); err != nil {
		logger.Error("Failed to unmarshal create/update project status response", "Error", err, "RawResponse", string(rawResponse))
		return fmt.Errorf("failed to unmarshal Supabase response for project status: %w", err)
	}

	if len(upsertedStatus) == 0 {
		logger.Error("Supabase create/update project status returned empty array", "ProjectID", projectStatus.ProjectID)
		return fmt.Errorf("supabase returned no data for project status ID %s", projectStatus.ProjectID)
	}

	*projectStatus = upsertedStatus[0]
	logger.Info("Successfully created/updated project status in Supabase", "ProjectID", projectStatus.ProjectID, "Status", projectStatus.Status, "UpdatedAt", projectStatus.UpdatedAt)
	return nil
}

// GetByProjectID 根据项目ID检索其状态信息。
// Supabase PostgREST: GET /rest/v1/project_status?project_id=eq.{projectID}&select=*
func (r *SupabaseProjectStatusRepository) GetByProjectID(ctx context.Context, projectID string) (*entity.ProjectStatus, error) {
	logger.Info("Getting project status by ProjectID from Supabase", "ProjectID", projectID)
	if projectID == "" {
		return nil, fmt.Errorf("project ID cannot be empty for getting status")
	}

	queryParams := url.Values{}
	queryParams.Set("project_id", "eq."+projectID)
	queryParams.Set("select", "*")

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectStatusTableName, queryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get project status by ProjectID from Supabase", "Error", err, "ProjectID", projectID)
		return nil, fmt.Errorf("supabase API error on get project status for ProjectID %s: %w", projectID, err)
	}

	var statuses []entity.ProjectStatus
	if err := json.Unmarshal(rawResponse, &statuses); err != nil {
		logger.Error("Failed to unmarshal get project status response", "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal Supabase response for project status %s: %w", projectID, err)
	}

	if len(statuses) == 0 {
		logger.Warn("Project status not found in Supabase", "ProjectID", projectID)
		return nil, fmt.Errorf("project status for ProjectID %s not found", projectID) // TODO: 标准化 NotFoundError
	}

	logger.Info("Successfully fetched project status from Supabase", "ProjectID", statuses[0].ProjectID, "Status", statuses[0].Status)
	return &statuses[0], nil
}

// DeleteByProjectID 根据项目ID删除项目状态。
// Supabase PostgREST: DELETE /rest/v1/project_status?project_id=eq.{projectID}
func (r *SupabaseProjectStatusRepository) DeleteByProjectID(ctx context.Context, projectID string) error {
	logger.Info("Deleting project status from Supabase", "ProjectID", projectID)
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty for deleting status")
	}

	queryParams := url.Values{}
	queryParams.Set("project_id", "eq."+projectID)

	preferHeaders := []string{"return=representation"} // 尝试获取被删除的记录

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodDelete, projectStatusTableName, queryParams, preferHeaders, nil)
	if err != nil {
		if apiErr, ok := err.(*SupabaseAPIError); ok && (apiErr.StatusCode == http.StatusNotFound || (apiErr.StatusCode == http.StatusOK && string(rawResponse) == "[]")) {
			logger.Warn("Project status not found for deletion, or already deleted", "ProjectID", projectID)
			return nil // 如果记录不存在，则认为删除成功
		}
		logger.Error("Failed to delete project status from Supabase", "Error", err, "ProjectID", projectID)
		return fmt.Errorf("supabase API error on delete project status for ProjectID %s: %w", projectID, err)
	}

	logger.Info("Successfully requested project status deletion from Supabase", "ProjectID", projectID)
	return nil
}

// GetByNetlifySiteID 根据 Netlify 站点 ID 检索项目状态信息。
// 它首先从 projects 表中找到 project_id，然后查询 project_status 表。
// 1. GET /rest/v1/projects?netlify_site_id=eq.{netlifySiteID}&select=id
// 2. Use retrieved id to call GetByProjectID
func (r *SupabaseProjectStatusRepository) GetByNetlifySiteID(ctx context.Context, netlifySiteID string) (*entity.ProjectStatus, error) {
	logger.Info("Getting project status by NetlifySiteID", "NetlifySiteID", netlifySiteID)
	if netlifySiteID == "" {
		return nil, fmt.Errorf("netlify site ID cannot be empty")
	}

	// 1. 从 projects 表获取 project_id
	projectQueryParams := url.Values{}
	projectQueryParams.Set("netlify_site_id", "eq."+netlifySiteID)
	projectQueryParams.Set("select", "id") // 只选择 project_id (假设列名为 id)

	logger.Debug("Querying projects table for project_id by netlify_site_id", "NetlifySiteID", netlifySiteID, "QueryParams", projectQueryParams.Encode())
	rawProjectResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectsTableName, projectQueryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get project_id by NetlifySiteID from Supabase", "Error", err, "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("supabase API error on get project_id for NetlifySiteID %s: %w", netlifySiteID, err)
	}

	// 期望返回格式: [{"id": "actual-project-id"}]
	var projectsWithID []struct {
		ID string `json:"id"`
	}
	if err := json.Unmarshal(rawProjectResponse, &projectsWithID); err != nil {
		logger.Error("Failed to unmarshal project_id response from Supabase", "Error", err, "RawResponse", string(rawProjectResponse), "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("failed to unmarshal Supabase response for project_id by NetlifySiteID %s: %w", netlifySiteID, err)
	}

	if len(projectsWithID) == 0 {
		logger.Warn("No project found for NetlifySiteID in Supabase", "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("no project found for NetlifySiteID %s", netlifySiteID) // TODO: 标准化 NotFoundError
	}
	if len(projectsWithID) > 1 {
		// 这不应该发生，如果 netlify_site_id 在 projects 表中有唯一约束
		logger.Error("Multiple projects found for the same NetlifySiteID", "NetlifySiteID", netlifySiteID, "Count", len(projectsWithID))
		return nil, fmt.Errorf("multiple projects found for NetlifySiteID %s, data integrity issue?", netlifySiteID)
	}

	projectID := projectsWithID[0].ID
	logger.Info("Successfully retrieved project_id for NetlifySiteID", "NetlifySiteID", netlifySiteID, "ProjectID", projectID)

	// 2. 使用获取到的 projectID 调用现有的 GetByProjectID 方法
	return r.GetByProjectID(ctx, projectID)
}

// UpdateByNetlifySiteID 根据 Netlify 站点 ID 更新项目状态信息。
// 1. 从 projects 表获取 project_id。
// 2. 使用 project_id 和 updates 数据 PATCH project_status 表。
func (r *SupabaseProjectStatusRepository) UpdateByNetlifySiteID(ctx context.Context, netlifySiteID string, updates map[string]any) (*entity.ProjectStatus, error) {
	logger.Info("Updating project status by NetlifySiteID", "NetlifySiteID", netlifySiteID, "Updates", updates)
	if netlifySiteID == "" {
		return nil, fmt.Errorf("netlify site ID cannot be empty for update")
	}
	if len(updates) == 0 {
		return nil, fmt.Errorf("updates map cannot be empty for update by netlify site ID")
	}

	// 1. 从 projects 表获取 project_id (与 GetByNetlifySiteID 逻辑类似)
	projectQueryParams := url.Values{}
	projectQueryParams.Set("netlify_site_id", "eq."+netlifySiteID)
	projectQueryParams.Set("select", "id")

	rawProjectResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectsTableName, projectQueryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get project_id by NetlifySiteID for update", "Error", err, "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("supabase API error on get project_id for NetlifySiteID %s (for update): %w", netlifySiteID, err)
	}

	var projectsWithID []struct {
		ID string `json:"id"`
	}
	if err := json.Unmarshal(rawProjectResponse, &projectsWithID); err != nil {
		logger.Error("Failed to unmarshal project_id response for update", "Error", err, "RawResponse", string(rawProjectResponse), "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("failed to unmarshal Supabase response for project_id by NetlifySiteID %s (for update): %w", netlifySiteID, err)
	}

	if len(projectsWithID) == 0 {
		logger.Warn("No project found for NetlifySiteID to update status", "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("no project found for NetlifySiteID %s to update status", netlifySiteID)
	}
	if len(projectsWithID) > 1 {
		logger.Error("Multiple projects found for the same NetlifySiteID during status update", "NetlifySiteID", netlifySiteID, "Count", len(projectsWithID))
		return nil, fmt.Errorf("multiple projects found for NetlifySiteID %s, data integrity issue during status update", netlifySiteID)
	}

	projectID := projectsWithID[0].ID
	logger.Info("Retrieved project_id for status update via NetlifySiteID", "NetlifySiteID", netlifySiteID, "ProjectID", projectID)

	// 2. 更新 project_status 表
	statusQueryParams := url.Values{}
	statusQueryParams.Set("project_id", "eq."+projectID)

	// Supabase PATCH 请求需要 Prefer: return=representation 头部才能返回更新后的记录
	preferHeaders := []string{"return=representation"}

	rawStatusResponse, _, err := r.client.makeRequest(ctx, http.MethodPatch, projectStatusTableName, statusQueryParams, preferHeaders, updates)
	if err != nil {
		// 检查是否因为记录不存在而导致的错误 (例如 404 or 空响应的 200/204)
		// PostgREST PATCH 在找不到匹配行时，若未设置 Prefer: handling=strict，通常返回 HTTP 204 No Content 并且响应体为空数组 `[]`。
		// 如果设置了 Prefer: return=representation，且没有行匹配，它可能会返回一个空的 JSON 数组 `[]` 且状态码为 200 OK。
		if apiErr, ok := err.(*SupabaseAPIError); ok && (apiErr.StatusCode == http.StatusNotFound || (apiErr.StatusCode == http.StatusOK && string(rawStatusResponse) == "[]")) {
			logger.Warn("Project status not found for update by ProjectID (derived from NetlifySiteID)", "ProjectID", projectID, "NetlifySiteID", netlifySiteID)
			return nil, fmt.Errorf("project status for ProjectID %s (derived from NetlifySiteID %s) not found for update", projectID, netlifySiteID)
		}
		logger.Error("Failed to update project status by ProjectID (derived from NetlifySiteID)", "Error", err, "ProjectID", projectID, "NetlifySiteID", netlifySiteID, "Updates", updates)
		return nil, fmt.Errorf("supabase API error on update project status for ProjectID %s (NetlifySiteID %s): %w", projectID, netlifySiteID, err)
	}

	var updatedStatuses []entity.ProjectStatus // PATCH with return=representation returns an array
	if err := json.Unmarshal(rawStatusResponse, &updatedStatuses); err != nil {
		logger.Error("Failed to unmarshal update project status response", "Error", err, "RawResponse", string(rawStatusResponse), "ProjectID", projectID)
		return nil, fmt.Errorf("failed to unmarshal Supabase response for updated project status %s: %w", projectID, err)
	}

	if len(updatedStatuses) == 0 {
		// 这也可能表示记录未找到，或者更新未应用到任何行
		logger.Warn("Project status update by ProjectID (derived from NetlifySiteID) returned empty array, possibly no matching record found or no change made", "ProjectID", projectID, "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("project status for ProjectID %s (NetlifySiteID %s) not found for update or no change made", projectID, netlifySiteID)
	}

	logger.Info("Successfully updated project status by NetlifySiteID", "ProjectID", updatedStatuses[0].ProjectID, "NetlifySiteID", netlifySiteID, "NewStatus", updatedStatuses[0].Status)
	return &updatedStatuses[0], nil
}

// UpdateByProjectID 根据项目 ID 更新项目状态信息。
func (r *SupabaseProjectStatusRepository) UpdateByProjectID(ctx context.Context, projectID string, updates map[string]any) (*entity.ProjectStatus, error) {
	logger.Info("Updating project status by ProjectID", "ProjectID", projectID, "Updates", updates)
	if projectID == "" {
		return nil, fmt.Errorf("project ID cannot be empty for update")
	}
	if len(updates) == 0 {
		return nil, fmt.Errorf("updates map cannot be empty for update by project ID")
	}

	// 更新 project_status 表
	statusQueryParams := url.Values{}
	statusQueryParams.Set("project_id", "eq."+projectID)

	// Supabase PATCH 请求需要 Prefer: return=representation 头部才能返回更新后的记录
	preferHeaders := []string{"return=representation"}

	rawStatusResponse, _, err := r.client.makeRequest(ctx, http.MethodPatch, projectStatusTableName, statusQueryParams, preferHeaders, updates)
	if err != nil {
		// 检查是否因为记录不存在而导致的错误
		if apiErr, ok := err.(*SupabaseAPIError); ok && (apiErr.StatusCode == http.StatusNotFound || (apiErr.StatusCode == http.StatusOK && string(rawStatusResponse) == "[]")) {
			logger.Warn("Project status not found for update by ProjectID", "ProjectID", projectID)
			return nil, fmt.Errorf("project status for ProjectID %s not found for update", projectID)
		}
		logger.Error("Failed to update project status by ProjectID", "Error", err, "ProjectID", projectID, "Updates", updates)
		return nil, fmt.Errorf("supabase API error on update project status for ProjectID %s: %w", projectID, err)
	}

	var updatedStatuses []entity.ProjectStatus // PATCH with return=representation returns an array
	if err := json.Unmarshal(rawStatusResponse, &updatedStatuses); err != nil {
		logger.Error("Failed to unmarshal update project status response", "Error", err, "RawResponse", string(rawStatusResponse), "ProjectID", projectID)
		return nil, fmt.Errorf("failed to unmarshal Supabase response for updated project status %s: %w", projectID, err)
	}

	if len(updatedStatuses) == 0 {
		logger.Warn("Project status update by ProjectID returned empty array, possibly no matching record found or no change made", "ProjectID", projectID)
		return nil, fmt.Errorf("project status for ProjectID %s not found for update or no change made", projectID)
	}

	logger.Info("Successfully updated project status by ProjectID", "ProjectID", updatedStatuses[0].ProjectID, "NewStatus", updatedStatuses[0].Status)
	return &updatedStatuses[0], nil
}

package supabase

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	defaultTimeout = 60 * time.Second
	restAPIPrefix  = "/rest/v1/"
)

// Client是与Supabase PostgREST API交互的客户端。
type Client struct {
	BaseURL    *url.URL // 例如 https://<project_ref>.supabase.co/rest/v1/
	httpClient *http.Client
	serviceKey string // Supabase 服务角色密钥 (service_role key)
}

// NewClient 创建一个新的Supabase客户端实例。
// supabaseURL: 你的Supabase项目的URL (例如 https://<project_ref>.supabase.co)
// serviceKey: 你的Supabase服务角色密钥。
func NewClient(supabaseURL, serviceKey string, httpClient *http.Client) (*Client, error) {
	if supabaseURL == "" {
		return nil, fmt.Errorf("supabase URL cannot be empty")
	}
	if serviceKey == "" {
		return nil, fmt.Errorf("supabase service key cannot be empty")
	}

	parsedURL, err := url.Parse(strings.TrimRight(supabaseURL, "/") + restAPIPrefix)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Supabase URL: %w", err)
	}

	if httpClient == nil {
		httpClient = &http.Client{Timeout: defaultTimeout}
	}

	return &Client{
		BaseURL:    parsedURL,
		httpClient: httpClient,
		serviceKey: serviceKey,
	}, nil
}

// SupabaseAPIError 代表从Supabase API返回的错误。
type SupabaseAPIError struct {
	StatusCode int    `json:"-"` // HTTP 状态码
	Message    string `json:"message"`
	Code       string `json:"code,omitempty"`
	Hint       string `json:"hint,omitempty"`
	Details    string `json:"details,omitempty"`
}

func (e *SupabaseAPIError) Error() string {
	return fmt.Sprintf("Supabase API error (status %d): %s (code: %s, hint: %s, details: %s)",
		e.StatusCode, e.Message, e.Code, e.Hint, e.Details)
}

// makeRequest 是一个通用的辅助函数，用于向Supabase PostgREST API发出HTTP请求。
// method: HTTP方法 (GET, POST, PATCH, DELETE 等)。
// path: API路径 (通常是表名，例如 "project")。
// queryParams: URL查询参数。
// preferHeaders: PostgREST Prefer header 值 (例如 "return=representation", "count=exact")
// body: 请求体，对于POST和PATCH请求。
func (c *Client) makeRequest(
	ctx context.Context,
	method string,
	path string,
	queryParams url.Values,
	preferHeaders []string,
	body any,
) (json.RawMessage, http.Header, error) {
	rel, err := url.Parse(strings.TrimLeft(path, "/"))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse relative path '%s': %w", path, err)
	}

	fullURL := c.BaseURL.ResolveReference(rel)
	if queryParams != nil {
		fullURL.RawQuery = queryParams.Encode()
	}

	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
		logger.Debug("Supabase request body", "Method", method, "URL", fullURL.String(), "Body", string(jsonBody))
	} else {
		logger.Debug("Supabase request", "Method", method, "URL", fullURL.String())
	}

	req, err := http.NewRequestWithContext(ctx, method, fullURL.String(), reqBody)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置必要的头部
	req.Header.Set("apikey", c.serviceKey)
	req.Header.Set("Authorization", "Bearer "+c.serviceKey)
	req.Header.Set("Content-Type", "application/json")
	if len(preferHeaders) > 0 {
		req.Header.Set("Prefer", strings.Join(preferHeaders, ","))
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to execute HTTP request: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Error("Failed to close response body", "Error", closeErr)
		}
	}()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, resp.Header, fmt.Errorf("failed to read response body: %w", err)
	}

	logger.Debug("Supabase response", "Status", resp.Status, "Headers", resp.Header, "BodyLength", len(respBodyBytes))
	if len(respBodyBytes) > 0 {
		logger.Debug("Supabase response body (raw)", "Body", string(respBodyBytes)) // 仅在必要时开启此日志，可能包含敏感信息
	}

	if resp.StatusCode >= 400 {
		apiErr := &SupabaseAPIError{StatusCode: resp.StatusCode}
		if jsonErr := json.Unmarshal(respBodyBytes, apiErr); jsonErr != nil {
			// 如果解析到APIError失败，使用原始响应体作为错误信息
			apiErr.Message = string(respBodyBytes)
			logger.Error("Failed to unmarshal Supabase API error response, using raw body", "UnmarshalError", jsonErr, "RawBody", string(respBodyBytes))
		}
		logger.Error("Supabase API error", "URL", fullURL.String(), "Status", resp.Status, "ErrorCode", apiErr.Code, "ErrorMessage", apiErr.Message)
		return nil, resp.Header, apiErr
	}

	// 对于 204 No Content，respBodyBytes可能是空的，这通常是正常的
	if resp.StatusCode == http.StatusNoContent && len(respBodyBytes) == 0 {
		return json.RawMessage("{}"), resp.Header, nil // 返回一个空的JSON对象，避免Unmarshal(nil)错误
	}

	return respBodyBytes, resp.Header, nil
}

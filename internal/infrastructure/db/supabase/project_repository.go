package supabase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/google/uuid"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	projectTableName = "project" // Supabase中的project表名
)

// SupabaseProjectRepository 是 ProjectRepository 接口的Supabase实现。
type SupabaseProjectRepository struct {
	client *Client
}

// NewSupabaseProjectRepository 创建一个新的 SupabaseProjectRepository 实例。
func NewSupabaseProjectRepository(client *Client) repository.ProjectRepository {
	if client == nil {
		logger.Fatal("Supabase client cannot be nil for ProjectRepository")
		return nil // 或者 panic，取决于错误处理策略
	}
	return &SupabaseProjectRepository{client: client}
}

// Create 保存一个新的项目到数据库。
// Supabase PostgREST: POST /rest/v1/project
func (r *SupabaseProjectRepository) Create(ctx context.Context, project *entity.Project) error {
	logger.Info("Creating project in Supabase", "Title", project.Title, "UserID", project.UserID)

	// 为新项目生成UUID (如果ID为空)
	if project.ID == "" {
		project.ID = uuid.NewString()
		logger.Info("Generated new UUID for project", "ID", project.ID)
	}

	// PostgREST 通常期望一个包含单个对象的数组进行插入，或者直接一个对象也可以。
	// 为了安全和明确，可以封装在数组中，但单个对象通常也有效。
	// Prefer: return=representation 会在成功创建后返回创建的对象。
	preferHeaders := []string{"return=representation"}

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodPost, projectTableName, nil, preferHeaders, project)
	if err != nil {
		logger.Error("Failed to create project in Supabase", "Error", err, "ProjectID", project.ID)
		return fmt.Errorf("supabase API error on create project: %w", err)
	}

	// 由于设置了 return=representation, API会返回创建的对象数组 (即使只创建一个)
	var createdProjects []entity.Project
	if err := json.Unmarshal(rawResponse, &createdProjects); err != nil {
		logger.Error("Failed to unmarshal create project response from Supabase", "Error", err, "RawResponse", string(rawResponse))
		return fmt.Errorf("failed to unmarshal Supabase response for create project: %w", err)
	}

	if len(createdProjects) == 0 {
		logger.Error("Supabase create project returned empty array despite return=representation", "ProjectID", project.ID)
		return fmt.Errorf("supabase returned no data for created project ID %s", project.ID)
	}

	// 更新传入的project对象，确保所有字段（如数据库生成的created_at, updated_at）都已填充
	*project = createdProjects[0]
	logger.Info("Successfully created project in Supabase", "ProjectID", project.ID, "CreatedAt", project.CreatedAt)
	return nil
}

// GetByID 根据项目ID检索项目。
// Supabase PostgREST: GET /rest/v1/project?id=eq.{id}&select=*
func (r *SupabaseProjectRepository) GetByID(ctx context.Context, id string) (*entity.Project, error) {
	logger.Info("Getting project by ID from Supabase", "ProjectID", id)
	if id == "" {
		return nil, fmt.Errorf("project ID cannot be empty")
	}

	queryParams := url.Values{}
	queryParams.Set("id", "eq."+id)
	queryParams.Set("select", "*") //确保查询所有列

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectTableName, queryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get project by ID from Supabase", "Error", err, "ProjectID", id)
		// 检查是否是SupabaseAPIError并且状态码是404 (或等效的 PostgREST 行为)
		// PostgREST 对于未找到的记录通常返回一个空的JSON数组 `[]` 和 200 OK。
		// 如果 makeRequest 内部已将空数组视为错误或特定错误类型，则这里可能不需要额外处理。
		// 假设 makeRequest 返回 nil, nil, SupabaseAPIError for 404, or similar for no data.
		return nil, fmt.Errorf("supabase API error on get project by ID %s: %w", id, err)
	}

	var projects []entity.Project
	if err := json.Unmarshal(rawResponse, &projects); err != nil {
		logger.Error("Failed to unmarshal get project by ID response from Supabase", "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal Supabase response for get project by ID %s: %w", id, err)
	}

	if len(projects) == 0 {
		logger.Warn("Project not found in Supabase", "ProjectID", id)
		// 符合DDD的Get方法，未找到应返回错误，而不是nil, nil
		// 可以定义一个标准的 domain.ErrNotFound 错误
		return nil, fmt.Errorf("project with ID %s not found", id) // TODO: 使用标准化的 NotFoundError
	}

	logger.Info("Successfully fetched project by ID from Supabase", "ProjectID", projects[0].ID)
	return &projects[0], nil
}

// GetByUserID 根据用户ID检索该用户的所有项目。
// Supabase PostgREST: GET /rest/v1/project?user_id=eq.{user_id}&select=*
func (r *SupabaseProjectRepository) GetByUserID(ctx context.Context, userID string) ([]*entity.Project, error) {
	logger.Info("Getting projects by UserID from Supabase", "UserID", userID)
	if userID == "" {
		return nil, fmt.Errorf("user ID cannot be empty")
	}

	queryParams := url.Values{}
	queryParams.Set("user_id", "eq."+userID)
	queryParams.Set("select", "*")
	// queryParams.Set("order", "created_at.desc") // 可选: 按创建时间排序

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectTableName, queryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get projects by UserID from Supabase", "Error", err, "UserID", userID)
		return nil, fmt.Errorf("supabase API error on get projects by UserID %s: %w", userID, err)
	}

	var projects []*entity.Project // 注意这里是指针切片，以匹配返回类型
	if err := json.Unmarshal(rawResponse, &projects); err != nil {
		logger.Error("Failed to unmarshal get projects by UserID response from Supabase", "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal Supabase response for get projects by UserID %s: %w", userID, err)
	}

	// 如果没有找到项目，PostgREST会返回一个空数组 `[]`，此时 projects 切片长度为0，是正常情况。
	logger.Info("Successfully fetched projects by UserID from Supabase", "UserID", userID, "Count", len(projects))
	return projects, nil
}

// Update 更新现有项目的信息。
// Supabase PostgREST: PATCH /rest/v1/project?id=eq.{id}
func (r *SupabaseProjectRepository) Update(ctx context.Context, project *entity.Project) error {
	logger.Info("Updating project in Supabase", "ProjectID", project.ID)
	if project.ID == "" {
		return fmt.Errorf("project ID cannot be empty for update")
	}

	queryParams := url.Values{}
	queryParams.Set("id", "eq."+project.ID)

	// Prefer: return=representation 会在成功更新后返回更新的对象。
	// 对于PATCH，通常只发送需要更改的字段。如果 project 结构体中所有字段都发送，
	// 且数据库有默认值或触发器更新updated_at，那么返回的对象将包含这些最新值。
	preferHeaders := []string{"return=representation"}

	// 注意：Supabase/PostgREST 的 PATCH 通常只更新提供的字段。
	// 如果 project 实体包含零值字段，且这些零值是有效的，那么它们也会被设置为数据库中的相应值（例如，布尔值false，字符串空）。
	// 如果只想更新非零值字段，客户端需要构建一个只包含这些字段的map[string]any作为body，
	// 或者确保project实体在传递到这里之前，其零值确实是期望更新的值。
	// 为简单起见，这里假设整个project实体都被用来更新。

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodPatch, projectTableName, queryParams, preferHeaders, project)
	if err != nil {
		logger.Error("Failed to update project in Supabase", "Error", err, "ProjectID", project.ID)
		return fmt.Errorf("supabase API error on update project %s: %w", project.ID, err)
	}

	var updatedProjects []entity.Project
	if err := json.Unmarshal(rawResponse, &updatedProjects); err != nil {
		logger.Error("Failed to unmarshal update project response from Supabase", "Error", err, "RawResponse", string(rawResponse))
		return fmt.Errorf("failed to unmarshal Supabase response for update project %s: %w", project.ID, err)
	}

	if len(updatedProjects) == 0 {
		// 这通常不应该发生，如果ID存在并且Prefer:return=representation被设置
		// 但如果ID不存在，PostgREST的PATCH可能返回200 OK和空数组，或者404 (取决于Prefer头部和具体配置)
		logger.Warn("Project not found for update or no data returned", "ProjectID", project.ID)
		return fmt.Errorf("project with ID %s not found for update, or no data returned", project.ID) // TODO: 使用标准化的 NotFoundError
	}

	*project = updatedProjects[0] // 更新传入的project对象
	logger.Info("Successfully updated project in Supabase", "ProjectID", project.ID, "UpdatedAt", project.UpdatedAt)
	return nil
}

// Delete 根据项目ID删除项目。
// Supabase PostgREST: DELETE /rest/v1/project?id=eq.{id}
// 注意: DDD通常建议软删除。这里的实现是硬删除。如果需要软删除，应该使用PATCH更新一个例如 is_deleted 或 deleted_at 字段。
// 假设这里遵循接口定义，执行（潜在的）硬删除。
func (r *SupabaseProjectRepository) Delete(ctx context.Context, id string) error {
	logger.Info("Deleting project from Supabase", "ProjectID", id)
	if id == "" {
		return fmt.Errorf("project ID cannot be empty for delete")
	}

	queryParams := url.Values{}
	queryParams.Set("id", "eq."+id)

	// Prefer: return=representation 可以确认被删除的对象（如果API支持）。
	// 通常DELETE成功返回204 No Content。
	// 如果ID不存在，DELETE通常也返回204或404，取决于PostgREST版本和配置。
	preferHeaders := []string{"return=representation"} // 尝试获取被删除的记录

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodDelete, projectTableName, queryParams, preferHeaders, nil)
	if err != nil {
		// 检查是否是记录未找到的错误。如果是，对于删除操作，这可能不被视为一个错误。
		if apiErr, ok := err.(*SupabaseAPIError); ok && (apiErr.StatusCode == http.StatusNotFound || (apiErr.StatusCode == http.StatusOK && string(rawResponse) == "[]")) {
			logger.Warn("Project not found for deletion, or already deleted", "ProjectID", id)
			return nil // 如果记录不存在，则认为删除成功
		}
		logger.Error("Failed to delete project from Supabase", "Error", err, "ProjectID", id)
		return fmt.Errorf("supabase API error on delete project %s: %w", id, err)
	}

	// 即使有 return=representation, 对于DELETE, 成功的空响应体（或HTTP 204）是常见的。
	// 如果 rawResponse 不是空的并且可以反序列化，说明 API 返回了被删除的项。
	logger.Info("Successfully requested project deletion from Supabase", "ProjectID", id)
	return nil
}

// UpdateFields 根据项目ID更新项目的部分字段。
// Supabase PostgREST: PATCH /rest/v1/project?id=eq.{projectID}
func (r *SupabaseProjectRepository) UpdateFields(ctx context.Context, projectID string, updates map[string]any) (*entity.Project, error) {
	logger.Info("Updating project fields in Supabase", "ProjectID", projectID, "Updates", updates)
	if projectID == "" {
		return nil, fmt.Errorf("project ID cannot be empty for updating fields")
	}
	if len(updates) == 0 {
		return nil, fmt.Errorf("updates map cannot be empty for updating project fields")
	}

	queryParams := url.Values{}
	queryParams.Set("id", "eq."+projectID)

	preferHeaders := []string{"return=representation"}

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodPatch, projectTableName, queryParams, preferHeaders, updates)
	if err != nil {
		// 检查是否因为记录不存在而导致的错误
		if apiErr, ok := err.(*SupabaseAPIError); ok && (apiErr.StatusCode == http.StatusNotFound || (apiErr.StatusCode == http.StatusOK && string(rawResponse) == "[]")) {
			logger.Warn("Project not found for field update", "ProjectID", projectID)
			return nil, fmt.Errorf("project with ID %s not found for field update", projectID)
		}
		logger.Error("Failed to update project fields in Supabase", "Error", err, "ProjectID", projectID, "Updates", updates)
		return nil, fmt.Errorf("supabase API error on update project fields for ID %s: %w", projectID, err)
	}

	var updatedProjects []entity.Project
	if err := json.Unmarshal(rawResponse, &updatedProjects); err != nil {
		logger.Error("Failed to unmarshal update project fields response from Supabase", "Error", err, "RawResponse", string(rawResponse), "ProjectID", projectID)
		return nil, fmt.Errorf("failed to unmarshal Supabase response for update project fields %s: %w", projectID, err)
	}

	if len(updatedProjects) == 0 {
		logger.Warn("Project not found for field update or no data returned after update", "ProjectID", projectID)
		return nil, fmt.Errorf("project with ID %s not found for field update or no data returned", projectID)
	}

	logger.Info("Successfully updated project fields in Supabase", "ProjectID", updatedProjects[0].ID)
	return &updatedProjects[0], nil
}

// GetByNetlifySiteID 根据 Netlify 站点 ID 检索项目。
// Supabase PostgREST: GET /rest/v1/project?netlify_site_id=eq.{netlifySiteID}&select=*
func (r *SupabaseProjectRepository) GetByNetlifySiteID(ctx context.Context, netlifySiteID string) (*entity.Project, error) {
	logger.Info("Getting project by NetlifySiteID from Supabase", "NetlifySiteID", netlifySiteID)
	if netlifySiteID == "" {
		return nil, fmt.Errorf("netlify site ID cannot be empty")
	}

	queryParams := url.Values{}
	queryParams.Set("netlify_site_id", "eq."+netlifySiteID)
	queryParams.Set("select", "*")

	rawResponse, _, err := r.client.makeRequest(ctx, http.MethodGet, projectTableName, queryParams, nil, nil)
	if err != nil {
		logger.Error("Failed to get project by NetlifySiteID from Supabase", "Error", err, "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("supabase API error on get project by NetlifySiteID %s: %w", netlifySiteID, err)
	}

	var projects []entity.Project
	if err := json.Unmarshal(rawResponse, &projects); err != nil {
		logger.Error("Failed to unmarshal get project by NetlifySiteID response from Supabase", "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal Supabase response for get project by NetlifySiteID %s: %w", netlifySiteID, err)
	}

	if len(projects) == 0 {
		logger.Warn("Project not found in Supabase", "NetlifySiteID", netlifySiteID)
		return nil, fmt.Errorf("project with NetlifySiteID %s not found", netlifySiteID)
	}

	if len(projects) > 1 {
		// 这不应该发生，如果 netlify_site_id 在 projects 表中有唯一约束
		logger.Error("Multiple projects found for the same NetlifySiteID", "NetlifySiteID", netlifySiteID, "Count", len(projects))
		return nil, fmt.Errorf("multiple projects found for NetlifySiteID %s, data integrity issue?", netlifySiteID)
	}

	logger.Info("Successfully fetched project by NetlifySiteID from Supabase", "NetlifySiteID", netlifySiteID)
	return &projects[0], nil
}

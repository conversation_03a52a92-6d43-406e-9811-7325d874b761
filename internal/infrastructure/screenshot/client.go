package screenshot

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
)

// Client 截图API客户端
type Client struct {
	BaseURL string
}

// NewClient 创建一个截图API客户端
// baseURL: API基础地址
// 返回: Client实例
func NewClient(baseURL string) *Client {
	return &Client{BaseURL: baseURL}
}

// Screenshot 调用截图API生成网页截图
// url: 需要截图的网页地址
// 返回: 截图图片地址和错误信息
func (c *Client) Screenshot(url string) (string, error) {
	// 固定宽高为1920x1080
	body, err := json.Marshal(struct {
		URL    string `json:"url"`
		Width  int    `json:"width"`
		Height int    `json:"height"`
	}{
		URL:    url,
		Width:  1920,
		Height: 1080,
	})
	if err != nil {
		return "", err
	}

	endpoint := c.BaseURL + "/screenshot"
	resp, err := http.Post(endpoint, "application/json", bytes.NewBuffer(body))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", errors.New("screenshot api returned non-200 status")
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 只解析img_url字段
	var result struct {
		ImgURL string `json:"img_url"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", err
	}

	imgURL := result.ImgURL
	if strings.HasPrefix(imgURL, "http://be-screenshot:9143/static/") {
		imgURL = strings.Replace(imgURL, "http://be-screenshot:9143/static/", "https://screenshot-api.webbuilder.site/static/", 1)
	}
	return imgURL, nil
}

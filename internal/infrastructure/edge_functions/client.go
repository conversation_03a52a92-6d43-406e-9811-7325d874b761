package edge_functions

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	defaultTimeout  = 60 * time.Second
	functionsPrefix = "/functions/v1/"
)

// Service 是访问不同 Edge Function API 服务的主入口点。
type Service struct {
	client    *EdgeFunctionClient
	Migration *MigrationService
}

// NewService 创建并初始化所有 Edge Function API 服务。
// baseURL: Supabase Functions 域名
// apiKey: Supabase 项目 API Key
// httpClient: 可选自定义 http.Client
func NewService(baseURL, apiKey string, httpClient *http.Client) (*Service, error) {
	client, err := NewClient(baseURL, apiKey, httpClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create edge function base client: %w", err)
	}

	s := &Service{
		client:    client,
		Migration: NewMigrationService(client),
	}
	return s, nil
}

// EdgeFunctionClient 是与 Supabase Edge Functions 交互的底层客户端。
type EdgeFunctionClient struct {
	BaseURL    *url.URL
	httpClient *http.Client
	apiKey     string
}

// NewClient 创建一个新的 EdgeFunctionClient 实例。
// baseURL: Supabase Functions 域名
// apiKey: Supabase 项目 API Key
// httpClient: 可选自定义 http.Client
func NewClient(baseURL, apiKey string, httpClient *http.Client) (*EdgeFunctionClient, error) {
	if baseURL == "" {
		return nil, fmt.Errorf("supabase functions baseURL cannot be empty")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("supabase apiKey cannot be empty")
	}

	parsedURL, err := url.Parse(strings.TrimRight(baseURL, "/") + functionsPrefix)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Supabase Functions baseURL: %w", err)
	}

	if httpClient == nil {
		httpClient = &http.Client{Timeout: defaultTimeout}
	}

	c := &EdgeFunctionClient{
		BaseURL:    parsedURL,
		httpClient: httpClient,
		apiKey:     apiKey,
	}

	if c.BaseURL != nil && !strings.HasSuffix(c.BaseURL.Path, "/") {
		c.BaseURL.Path += "/"
	}

	return c, nil
}

// CallFunction 调用指定的 Edge Function。
func (c *EdgeFunctionClient) CallFunction(
	ctx context.Context,
	functionName string,
	method string,
	body any,
) ([]byte, *http.Response, error) {
	if functionName == "" {
		return nil, nil, fmt.Errorf("functionName cannot be empty")
	}
	if method == "" {
		method = http.MethodPost
	}

	rel, err := url.Parse(strings.TrimLeft(functionName, "/"))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse functionName '%s': %w", functionName, err)
	}
	fullURL := c.BaseURL.ResolveReference(rel)

	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
		logger.Debug("EdgeFunction request body", "Method", method, "URL", fullURL.String(), "Body", string(jsonBody))
	} else {
		logger.Debug("EdgeFunction request", "Method", method, "URL", fullURL.String())
	}

	req, err := http.NewRequestWithContext(ctx, method, fullURL.String(), reqBody)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to execute HTTP request: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Error("Failed to close response body", "Error", closeErr)
		}
	}()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, resp, fmt.Errorf("failed to read response body: %w", err)
	}

	logger.Debug("EdgeFunction response", "Status", resp.Status, "Headers", resp.Header, "BodyLength", len(respBodyBytes))
	if len(respBodyBytes) > 0 {
		logger.Debug("EdgeFunction response body (raw)", "Body", string(respBodyBytes))
	}

	if resp.StatusCode >= 400 {
		return respBodyBytes, resp, fmt.Errorf("EdgeFunction API error (status %d): %s", resp.StatusCode, string(respBodyBytes))
	}

	return respBodyBytes, resp, nil
}

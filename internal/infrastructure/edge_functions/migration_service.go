package edge_functions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// MigrationService 封装与迁移相关的 Edge Function 调用。
type MigrationService struct {
	client *EdgeFunctionClient
}

// NewMigrationService 创建一个新的 MigrationService 实例。
func NewMigrationService(client *EdgeFunctionClient) *MigrationService {
	return &MigrationService{client: client}
}

// CallMigrationWbFree 调用 migratie_wb_free Edge Function 执行 SQL 迁移。
// ctx: 上下文
// sql: 要执行的 SQL 语句
// 返回 true 表示迁移成功，false 表示失败，err 为详细错误信息
func (m *MigrationService) CallMigrationWbFree(ctx context.Context, sql string) (bool, error) {
	if sql == "" {
		return false, fmt.Errorf("sql cannot be empty")
	}

	requestBody := map[string]string{
		"sql": sql,
	}

	respBytes, resp, err := m.client.CallFunction(ctx, "hyper-endpoint", "POST", requestBody)
	if err != nil {
		logger.Error("Call migratie_wb_free failed", "Error", err, "Status", resp.StatusCode)
		return false, err
	}

	var result bool
	err = json.Unmarshal(respBytes, &result)
	if err != nil {
		logger.Error("Failed to unmarshal migratie_wb_free response", "Error", err, "RawBody", string(respBytes))
		return false, fmt.Errorf("failed to parse response: %w", err)
	}

	return result, nil
}

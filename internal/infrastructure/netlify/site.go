package netlify

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// SiteService 提供了操作 Netlify 站点的相关方法。
type SiteService struct {
	// client *Client // 通过 common service 间接引用 client
	common service // 嵌入 common service，它包含了 client 的引用
}

// newSiteService 创建一个新的 SiteService 实例。
func newSiteService(client *Client) *SiteService {
	return &SiteService{common: service{client: client}}
}

// Site represents a Netlify site.
type Site struct {
	ID              string           `json:"id"`
	SiteID          string           `json:"site_id"` // Netlify 有时用 site_id，确保能捕获
	Name            string           `json:"name"`
	CustomDomain    string           `json:"custom_domain,omitempty"`
	BuildSettings   map[string]any   `json:"build_settings,omitempty"`
	DeployURL       string           `json:"deploy_url,omitempty"` // 主站点的部署 URL
	URL             string           `json:"url,omitempty"`        // 主站点的 URL
	SslURL          string           `json:"ssl_url,omitempty"`    // 主站点的 SSL URL
	AdminURL        string           `json:"admin_url,omitempty"`
	ScreenshotURL   string           `json:"screenshot_url,omitempty"`
	CreatedAt       string           `json:"created_at,omitempty"`
	UpdatedAt       string           `json:"updated_at,omitempty"`
	AccountSlug     string           `json:"account_slug,omitempty"`
	AccountID       string           `json:"account_id,omitempty"`
	Repo            *NetlifyRepoInfo `json:"repo,omitempty"` // 关联的仓库信息
	PublishedDeploy *Deploy          `json:"published_deploy,omitempty"`
	// 新增字段
	Plan                            string              `json:"plan,omitempty"`
	SslPlan                         string              `json:"ssl_plan,omitempty"`
	Premium                         bool                `json:"premium,omitempty"`
	Claimed                         bool                `json:"claimed,omitempty"`
	BranchDeployCustomDomain        string              `json:"branch_deploy_custom_domain,omitempty"`
	DeployPreviewCustomDomain       string              `json:"deploy_preview_custom_domain,omitempty"`
	DomainAliases                   []string            `json:"domain_aliases,omitempty"`
	SsoLogin                        bool                `json:"sso_login,omitempty"`
	SsoLoginContext                 string              `json:"sso_login_context,omitempty"`
	NotificationEmail               string              `json:"notification_email,omitempty"`
	DeployID                        string              `json:"deploy_id,omitempty"`
	BuildID                         string              `json:"build_id,omitempty"`
	State                           string              `json:"state,omitempty"`
	UserID                          string              `json:"user_id,omitempty"`
	ErrorMessage                    string              `json:"error_message,omitempty"`
	Ssl                             bool                `json:"ssl,omitempty"`
	ForceSSL                        bool                `json:"force_ssl,omitempty"`
	SslStatus                       string              `json:"ssl_status,omitempty"`
	MaxDomainAliases                int                 `json:"max_domain_aliases,omitempty"`
	DevServerSettings               map[string]any      `json:"dev_server_settings,omitempty"`
	ProcessingSettings              map[string]any      `json:"processing_settings,omitempty"`
	Prerender                       string              `json:"prerender,omitempty"`
	PrerenderHeaders                string              `json:"prerender_headers,omitempty"`
	DeployHook                      string              `json:"deploy_hook,omitempty"`
	ManagedDNS                      bool                `json:"managed_dns,omitempty"`
	JwtSecret                       string              `json:"jwt_secret,omitempty"`
	JwtRolesPath                    string              `json:"jwt_roles_path,omitempty"`
	AccountName                     string              `json:"account_name,omitempty"`
	AccountType                     string              `json:"account_type,omitempty"`
	Capabilities                    map[string]any      `json:"capabilities,omitempty"`
	DnsZoneID                       string              `json:"dns_zone_id,omitempty"`
	IdentityInstanceID              string              `json:"identity_instance_id,omitempty"`
	PrimaryCreateProject            string              `json:"primary_create_project,omitempty"`
	UseFunctions                    bool                `json:"use_functions,omitempty"`
	UseEdgeHandlers                 bool                `json:"use_edge_handlers,omitempty"`
	UseForms                        bool                `json:"use_forms,omitempty"`
	ParentUserID                    string              `json:"parent_user_id,omitempty"`
	AutomaticTlsProvisioning        bool                `json:"automatic_tls_provisioning,omitempty"`
	Disabled                        bool                `json:"disabled,omitempty"`
	DisabledReason                  string              `json:"disabled_reason,omitempty"`
	LifecycleState                  string              `json:"lifecycle_state,omitempty"`
	IdDomain                        string              `json:"id_domain,omitempty"`
	UseLm                           bool                `json:"use_lm,omitempty"`
	BuildImage                      string              `json:"build_image,omitempty"`
	AutomaticTlsProvisioningExpired bool                `json:"automatic_tls_provisioning_expired,omitempty"`
	AnalyticsInstanceID             string              `json:"analytics_instance_id,omitempty"`
	FunctionsRegion                 string              `json:"functions_region,omitempty"`
	FunctionsTimeout                int                 `json:"functions_timeout,omitempty"`
	Plugins                         []map[string]string `json:"plugins,omitempty"`
	AccountSubdomain                string              `json:"account_subdomain,omitempty"`
	CdpEnabledContexts              []string            `json:"cdp_enabled_contexts,omitempty"`
	DeployRetentionInDays           int                 `json:"deploy_retention_in_days,omitempty"`
	BuildTimelimit                  int                 `json:"build_timelimit,omitempty"`
	PasswordContext                 string              `json:"password_context,omitempty"`
	HudEnabled                      bool                `json:"hud_enabled,omitempty"`
	Labels                          []string            `json:"labels,omitempty"`
	TrafficRulesConfigPerScope      map[string]any      `json:"traffic_rules_config_per_scope,omitempty"`
	VisualEditorActive              bool                `json:"visual_editor_active,omitempty"`
	Description                     string              `json:"description,omitempty"`
	DescriptionHidden               bool                `json:"description_hidden,omitempty"`
	DevServerResources              map[string]any      `json:"dev_server_resources,omitempty"`
	CreatedVia                      string              `json:"created_via,omitempty"`
	DeployOrigin                    map[string]any      `json:"deploy_origin,omitempty"`
	CoverURL                        string              `json:"cover_url,omitempty"`
	FeatureFlags                    map[string]any      `json:"feature_flags,omitempty"`
	Versions                        map[string]any      `json:"versions,omitempty"`
	HasPassword                     bool                `json:"has_password,omitempty"`
	DefaultDomain                   string              `json:"default_domain,omitempty"`
	UseEnvelope                     bool                `json:"use_envelope,omitempty"`
}

// SiteBuildSettingsInfo 包含用户提供的所有构建和仓库相关设置。
// 对应 Python 示例中的 site_data.build_settings。
type SiteBuildSettingsInfo struct {
	RepoURL         string            `json:"repo_url,omitempty"`
	RepoBranch      string            `json:"repo_branch,omitempty"`
	Provider        string            `json:"provider,omitempty"` // 例如 "github"
	DeployKeyID     string            `json:"deploy_key_id,omitempty"`
	AllowedBranches []string          `json:"allowed_branches,omitempty"`
	Env             map[string]string `json:"env,omitempty"`
	Cmd             string            `json:"cmd,omitempty"`
	Dir             string            `json:"dir,omitempty"`
	RepoPath        string            `json:"repo_path,omitempty"` // 例如 "owner/repo"
	Private         bool              `json:"private,omitempty"`
	Base            string            `json:"base,omitempty"`          // Monorepo base directory
	FunctionsDir    string            `json:"functions_dir,omitempty"` // Netlify functions directory
	InstallationID  int               `json:"installation_id,omitempty"`
}

// CreateSiteOptions 是 SiteService.CreateSite 方法的输入参数。
type CreateSiteOptions struct {
	Name          string
	CustomDomain  string
	Password      string // 可选
	ForceSSL      bool   // 可选
	BuildSettings *SiteBuildSettingsInfo
}

// NetlifyRepoInfo 用于 POST /sites 请求中的 'repo' 对象。
type NetlifyRepoInfo struct {
	Provider    string `json:"provider"`
	DeployKeyID string `json:"deploy_key_id"`
	Repo        string `json:"repo"` // 这是 repo_path
	Private     bool   `json:"private,omitempty"`
	Branch      string `json:"branch"`
	Cmd         string `json:"cmd,omitempty"`
	Dir         string `json:"dir,omitempty"`
	// 根据 Netlify API 文档， 'base' 和 'functions_dir' 也可以在这里
	Base         string `json:"base,omitempty"`
	FunctionsDir string `json:"functions_dir,omitempty"`
}

// NetlifyBuildSettingsForPatch 用于 PATCH /sites/{id} 请求中的 'build_settings' 对象。
type NetlifyBuildSettingsForPatch struct {
	RepoType        string            `json:"repo_type,omitempty"` // "git"
	RepoURL         string            `json:"repo_url,omitempty"`
	RepoBranch      string            `json:"repo_branch,omitempty"`
	Provider        string            `json:"provider,omitempty"`
	RepoPath        string            `json:"repo_path,omitempty"`
	Cmd             string            `json:"cmd,omitempty"`
	Dir             string            `json:"dir,omitempty"`
	AllowedBranches []string          `json:"allowed_branches,omitempty"`
	Base            string            `json:"base,omitempty"`
	FunctionsDir    string            `json:"functions_dir,omitempty"`
	Env             map[string]string `json:"env,omitempty"`
	InstallationID  int               `json:"installation_id,omitempty"`
	DeployKeyID     string            `json:"deploy_key_id,omitempty"` // 也可能在 build_settings 更新中
}

// netlifyCreateSitePOSTPayload 是 POST /sites 的实际请求体。
type netlifyCreateSitePOSTPayload struct {
	Name         string           `json:"name"`
	CustomDomain string           `json:"custom_domain,omitempty"`
	Password     string           `json:"password,omitempty"`
	ForceSSL     bool             `json:"force_ssl,omitempty"`
	Repo         *NetlifyRepoInfo `json:"repo,omitempty"`
	// Netlify API (POST /sites) 也可以接受一个顶级的 'build_settings' 对象。
	// 但为遵循Python示例的两步法，此处不在初始POST中包含完整的build_settings。
}

// netlifyUpdateSitePATCHPayload 是 PATCH /sites/{id} 的实际请求体。
type netlifyUpdateSitePATCHPayload struct {
	BuildSettings *NetlifyBuildSettingsForPatch `json:"build_settings"`
}

// CreateSite 在 Netlify 上创建一个新站点，并根据需要更新其构建设置。
// API 文档: POST /sites, PATCH /sites/{site_id}
func (s *SiteService) CreateSite(ctx context.Context, opts *CreateSiteOptions) (*Site, error) {
	logger.Info("Attempting to create Netlify site", "Name:", opts.Name, "CustomDomain:", opts.CustomDomain)

	// 准备初始 POST 请求体
	postPayload := netlifyCreateSitePOSTPayload{
		Name:         opts.Name,
		CustomDomain: opts.CustomDomain,
		Password:     opts.Password,
		ForceSSL:     opts.ForceSSL,
	}

	bs := opts.BuildSettings
	if bs != nil && bs.DeployKeyID != "" {
		logger.Info("Repo information provided, preparing repo object for initial site creation", "DeployKeyID:", bs.DeployKeyID)
		repoPath := bs.RepoPath
		if repoPath == "" && strings.Contains(bs.RepoURL, "github.com/") {
			parts := strings.SplitN(bs.RepoURL, "github.com/", 2)
			if len(parts) == 2 && parts[1] != "" {
				repoPath = parts[1]
				logger.Info("Derived repoPath from RepoURL", "OriginalURL:", bs.RepoURL, "DerivedPath:", repoPath)
			}
		}
		if repoPath == "" && bs.RepoURL != "" { // Fallback if not github.com or malformed
			repoPath = bs.RepoURL // Use full URL as last resort if path cannot be derived
			logger.Warn("Could not derive specific repo_path from RepoURL, using full RepoURL as repo path.", "RepoURL", bs.RepoURL)
		}

		provider := bs.Provider
		if provider == "" {
			provider = "github" // 默认提供商
		}
		branch := bs.RepoBranch
		if branch == "" {
			branch = "main" // 默认分支
		}

		postPayload.Repo = &NetlifyRepoInfo{
			Provider:     provider,
			DeployKeyID:  bs.DeployKeyID,
			Repo:         repoPath,
			Private:      bs.Private,
			Branch:       branch,
			Cmd:          bs.Cmd, // Cmd 和 Dir 也可以在 repo 对象中
			Dir:          bs.Dir,
			Base:         bs.Base,
			FunctionsDir: bs.FunctionsDir,
		}
		logger.Info("Site creation POST request will include repo object", "RepoInfo:", postPayload.Repo)
	}

	// 第一步: 创建站点
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, "/sites", postPayload)
	if err != nil {
		logger.Error("Failed to send create_site POST request", "Error:", err, "Payload:", postPayload)
		return nil, fmt.Errorf("failed to send create_site request: %w", err)
	}

	var siteResponse Site
	if err := json.Unmarshal(rawResponse, &siteResponse); err != nil {
		logger.Error("Failed to unmarshal create_site POST response", "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal site response from POST: %w", err)
	}
	logger.Info("Successfully created site via POST", "SiteID:", siteResponse.ID, "SiteName:", siteResponse.Name)

	// 第二步: 如果需要，更新站点的 build_settings (PATCH)
	// Python 示例中，如果 build_settings 包含 repo_url 或 provider，则执行更新。
	// 这里我们检查 BuildSettings 是否为nil以及是否包含一些关键的仓库或构建信息。
	if bs != nil && (bs.RepoURL != "" || bs.Provider != "" || bs.Cmd != "" || bs.Dir != "" || len(bs.Env) > 0 || len(bs.AllowedBranches) > 0) {
		logger.Info("Build settings provided, preparing to update site with PATCH", "SiteID:", siteResponse.ID)

		patchBuildSettings := &NetlifyBuildSettingsForPatch{
			RepoType:        "git", // 默认为 "git"
			RepoURL:         bs.RepoURL,
			RepoBranch:      bs.RepoBranch,
			Provider:        bs.Provider,
			RepoPath:        bs.RepoPath, // 可能已在上面派生
			Cmd:             bs.Cmd,
			Dir:             bs.Dir,
			AllowedBranches: bs.AllowedBranches,
			Base:            bs.Base,
			FunctionsDir:    bs.FunctionsDir,
			Env:             bs.Env,
			InstallationID:  bs.InstallationID,
			DeployKeyID:     bs.DeployKeyID, // DeployKeyID 也可能在 build_settings 中更新
		}
		// 如果RepoPath在bs中为空，但已从RepoURL派生并用于postPayload.Repo.Repo，则在这里也使用它
		if patchBuildSettings.RepoPath == "" && postPayload.Repo != nil && postPayload.Repo.Repo != "" {
			patchBuildSettings.RepoPath = postPayload.Repo.Repo
		}

		updatePayload := netlifyUpdateSitePATCHPayload{BuildSettings: patchBuildSettings}
		updateEndpoint := fmt.Sprintf("/sites/%s", siteResponse.ID)

		_, patchErr := s.common.client.makeRequest(ctx, http.MethodPatch, updateEndpoint, updatePayload)
		if patchErr != nil {
			// 遵循 Python 示例，记录错误但不使整个 CreateSite 失败
			logger.Error("Failed to update site build_settings via PATCH", "SiteID:", siteResponse.ID, "Error:", patchErr, "UpdatePayload:", updatePayload)
		} else {
			logger.Info("Successfully updated site build_settings via PATCH", "SiteID:", siteResponse.ID)
		}
	}

	return &siteResponse, nil
}

// DeleteSite deletes a site on Netlify by its ID.
// API 文档: https://open-api.netlify.com/#/default/deleteSite
func (s *SiteService) DeleteSite(ctx context.Context, siteID string) error {
	if siteID == "" {
		return fmt.Errorf("site ID cannot be empty for deletion")
	}
	logger.Info("Deleting Netlify site", "SiteID:", siteID)

	endpoint := fmt.Sprintf("/sites/%s", siteID)

	// makeRequest for a DELETE usually expects no body and returns no body on success (204)
	// json.RawMessage will be empty or {} for a 204, error will be nil.
	_, err := s.common.client.makeRequest(ctx, http.MethodDelete, endpoint, nil)
	if err != nil {
		// Check if it's an APIError for not found, which might not be a test failure in cleanup
		if apiErr, ok := err.(*APIError); ok && apiErr.StatusCode == http.StatusNotFound {
			logger.Warn("Site not found for deletion, might have been already deleted", "SiteID:", siteID)
			return nil // Or a specific error type indicating 'already deleted / not found'
		}
		logger.Error("Failed to delete site", "SiteID:", siteID, "Error:", err)
		return fmt.Errorf("failed to send delete_site request for site ID %s: %w", siteID, err)
	}

	logger.Info("Successfully requested site deletion", "SiteID:", siteID)
	return nil
}

// API 文档: https://open-api.netlify.com/#/default/getSite
func (s *SiteService) GetSite(ctx context.Context, siteID string) (*Site, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty")
	}
	logger.Info("获取 Netlify 站点详情", "SiteID", siteID)

	endpoint := fmt.Sprintf("/sites/%s", siteID)
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		logger.Error("获取 Netlify 站点详情失败: API 请求错误", "SiteID", siteID, "Error", err)
		return nil, fmt.Errorf("failed to send get_site request for site ID %s: %w", siteID, err)
	}

	var siteResponse Site
	if err := json.Unmarshal(rawResponse, &siteResponse); err != nil {
		logger.Error("获取 Netlify 站点详情失败: JSON 解析错误", "SiteID", siteID, "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal site response for site ID %s: %w", siteID, err)
	}

	// 确保 site_id 字段填充（如果原始响应中只有 id）
	if siteResponse.SiteID == "" && siteResponse.ID != "" {
		siteResponse.SiteID = siteResponse.ID
	}

	// 日志记录仓库和部署状态
	if siteResponse.Repo != nil && siteResponse.Repo.Repo != "" {
		logger.Info("站点已关联 GitHub 仓库", "SiteID", siteID, "RepoPath", siteResponse.Repo.Repo)
	} else {
		logger.Info("站点未关联 GitHub 仓库", "SiteID", siteID)
	}

	if siteResponse.PublishedDeploy != nil {
		logger.Info("站点最新发布部署状态", "SiteID", siteID, "DeployID", siteResponse.PublishedDeploy.ID, "DeployState", siteResponse.PublishedDeploy.State)
	} else {
		// 尝试从 build_settings 中获取部署信息，有时 Netlify API 行为不一致
		// 这是一个简化的处理，实际中可能需要更复杂的逻辑来确定最新部署
		logger.Info("站点没有明确的 'published_deploy' 记录", "SiteID", siteID)
	}

	logger.Info("成功获取 Netlify 站点详情", "SiteID", siteID, "SiteName", siteResponse.Name)
	return &siteResponse, nil
}

// ListSiteOptions 是 SiteService.ListSite 方法的输入参数。
type ListSiteOptions struct {
	Filter           string `json:"filter,omitempty"`            // 例如 "all"
	SortBy           string `json:"sort_by,omitempty"`           // 例如 "published_at"
	OrderBy          string `json:"order_by,omitempty"`          // 例如 "asc"
	Page             int    `json:"page,omitempty"`              // 页码
	PerPage          int    `json:"per_page,omitempty"`          // 每页数量
	IncludeFavorites bool   `json:"include_favorites,omitempty"` // 是否包含收藏
}

// ListSite 获取 Netlify 站点列表。
// API 文档: https://open-api.netlify.com/#/default/listSites
func (s *SiteService) ListSite(ctx context.Context, opts *ListSiteOptions) ([]*Site, error) {
	logger.Info("获取 Netlify 站点列表", "Options", opts)

	// 构建查询参数
	queryParams := make(map[string]string)
	if opts != nil {
		if opts.Filter != "" {
			queryParams["filter"] = opts.Filter
		}
		if opts.SortBy != "" {
			queryParams["sort_by"] = opts.SortBy
		}
		if opts.OrderBy != "" {
			queryParams["order_by"] = opts.OrderBy
		}
		if opts.Page > 0 {
			queryParams["page"] = fmt.Sprintf("%d", opts.Page)
		}
		if opts.PerPage > 0 {
			queryParams["per_page"] = fmt.Sprintf("%d", opts.PerPage)
		}
		if opts.IncludeFavorites {
			queryParams["include_favorites"] = "true"
		}
	}

	// 构建查询字符串
	var queryString string
	if len(queryParams) > 0 {
		params := make([]string, 0, len(queryParams))
		for k, v := range queryParams {
			params = append(params, fmt.Sprintf("%s=%s", k, v))
		}
		queryString = "?" + strings.Join(params, "&")
	}

	endpoint := "/sites" + queryString
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		logger.Error("获取 Netlify 站点列表失败: API 请求错误", "Error", err)
		return nil, fmt.Errorf("failed to send list_sites request: %w", err)
	}

	var sites []*Site
	if err := json.Unmarshal(rawResponse, &sites); err != nil {
		logger.Error("获取 Netlify 站点列表失败: JSON 解析错误", "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal sites response: %w", err)
	}

	// 确保每个站点的 site_id 字段填充（如果原始响应中只有 id）
	for _, site := range sites {
		if site.SiteID == "" && site.ID != "" {
			site.SiteID = site.ID
		}
	}

	logger.Info("成功获取 Netlify 站点列表", "Count", len(sites))
	return sites, nil
}

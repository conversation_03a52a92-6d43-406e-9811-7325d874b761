package netlify

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// HookCreateRequestData 定义了创建新 Hook 时可以传递的参数。
type HookCreateRequestData struct {
	SiteID string     `json:"site_id"`        // 关联的站点 ID
	Type   string     `json:"type"`           // Hook 类型，例如 "slack", "github_commit_status", "email", "outgoing_webhook"
	Event  string     `json:"event"`          // 触发事件
	Data   HookData   `json:"data,omitempty"` // Hook 的额外配置数据
}

// HookData 定义 data 字段的结构体，目前仅包含 URL
type HookData struct {
	URL string `json:"url,omitempty"` // 对 outgoing_webhook 是必需的
}

// Hook 表示 Netlify Hook 的信息。
type Hook struct {
	ID        string            `json:"id"`
	SiteID    string            `json:"site_id"`
	Type      string            `json:"type"`
	Event     string            `json:"event"`
	URL       string            `json:"url,omitempty"`  // 对于 outgoing_webhook
	Data      map[string]string `json:"data,omitempty"` // 例如 { "channel": "#deployments" }
	CreatedAt string            `json:"created_at"`
	UpdatedAt string            `json:"updated_at"`
	Disabled  bool              `json:"disabled"`
}

// HookService 提供了操作 Netlify Hook 的相关方法。
type HookService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newHookService 创建一个新的 HookService 实例。
func newHookService(client *Client) *HookService {
	return &HookService{common: service{client: client}}
}

// CreateHook 创建一个新的 Netlify Hook。
// hookData: 必需，包含创建 Hook 所需的信息，如 SiteID, Type, Event, 等。
// API 文档: https://open-api.netlify.com/#/default/createHook
func (s *HookService) CreateHook(ctx context.Context, hookData HookCreateRequestData) (*Hook, error) {
	logger.Info("Creating Netlify hook", "SiteID:", hookData.SiteID, "EventType:", hookData.Event, "HookType:", hookData.Type)

	// 根据 Netlify API, /hooks 端点用于创建账户级别的 Hook (account_id 在请求体中)
	// 而站点级别的 Hook (site_id 在请求体中) 也使用 /hooks 端点，但通常 site_id 是必须的。
	// Python 示例中 hook_data 包含 site_id, 所以我们假设是创建站点相关的 hook。
	// API 文档指示POST /hooks，请求体中包含 site_id。
	endpoint := "/hooks"

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, hookData)
	if err != nil {
		logger.Error("Failed to create hook", "SiteID:", hookData.SiteID, "Error:", err)
		return nil, fmt.Errorf("failed to send create_hook request: %w", err)
	}

	var hookResponse Hook
	if err := json.Unmarshal(rawResponse, &hookResponse); err != nil {
		logger.Error("Failed to unmarshal create_hook response", "SiteID:", hookData.SiteID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal create_hook response: %w", err)
	}

	logger.Info("Successfully created hook", "HookID:", hookResponse.ID, "SiteID:", hookResponse.SiteID)
	return &hookResponse, nil
}

package netlify

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// Snippet 表示 Netlify 站点的一个代码片段。
// 用于 GET /api/v1/sites/{site_id}/snippets 的响应。
type Snippet struct {
	ID              string `json:"id"`                // 根据实际API响应，ID是字符串类型
	SiteID          string `json:"site_id,omitempty"` // 通常在列表项中不直接出现，但在某些上下文中可能需要
	Title           string `json:"title"`
	General         string `json:"general"`                 // HTML/JS 代码片段内容
	GeneralPosition string `json:"general_position"`        // "head" 或 "footer"
	Goal            string `json:"goal,omitempty"`          // 目标相关代码段 (可能为null或空)
	GoalPosition    string `json:"goal_position,omitempty"` // 目标代码段位置
}

// CreateSnippetRequest 定义了创建新代码片段时的请求体。
// POST /api/v1/sites/{site_id}/snippets
type CreateSnippetRequest struct {
	Title           string `json:"title"`
	General         string `json:"general"`
	GeneralPosition string `json:"general_position"` // "head" 或 "footer"
	// 根据 Netlify 文档，创建时可能还有 goal 和 goal_position，但题目只要求了三个
}

// UpdateSnippetRequest 定义了更新代码片段时的请求体。
// PUT /api/v1/sites/{site_id}/snippets/{snippet_id}
type UpdateSnippetRequest struct {
	Title           string `json:"title,omitempty"`
	General         string `json:"general,omitempty"`
	GeneralPosition string `json:"general_position,omitempty"` // "head" 或 "footer"
}

// SnippetService 提供了操作 Netlify 站点代码片段的方法。
type SnippetService struct {
	common service
}

// newSnippetService 创建一个新的 SnippetService 实例。
func newSnippetService(client *Client) *SnippetService {
	return &SnippetService{common: service{client: client}}
}

// CreateSiteSnippet 在指定的 Netlify 站点上创建一个新的代码片段。
// siteID: 必需，Netlify 站点的 ID。
// snippetData: 包含代码片段信息的请求体。
// API 文档: POST /api/v1/sites/{site_id}/snippets
// Netlify API 成功时返回 201 Created，但实际响应体为 null。
func (s *SnippetService) CreateSiteSnippet(ctx context.Context, siteID string, snippetData *CreateSnippetRequest) error {
	if siteID == "" {
		return fmt.Errorf("site ID cannot be empty for creating a snippet")
	}
	if snippetData == nil {
		return fmt.Errorf("snippet data cannot be nil for creating a snippet")
	}
	logger.Info("Creating site snippet", "SiteID", siteID, "Title", snippetData.Title)

	endpoint := fmt.Sprintf("sites/%s/snippets", siteID) // Path without leading slash for makeRequest

	// makeRequest 对于成功的请求（如201）如果响应体是null，json.Unmarshal会处理（得到空对象）或者直接返回原始的null字节。
	// 但由于我们现在只关心操作是否成功（HTTP状态码），而不解析响应体获取Snippet对象，所以直接检查err即可。
	// client.go中的makeRequest在HTTP状态码>=400时返回error，在204时返回 (json.RawMessage("{}"), nil)。
	// 对于200/201等成功状态码且有响应体的情况，它返回 (respBodyBytes, nil)。
	// 如果Netlify返回201和null body, respBodyBytes会是[]byte("null")。
	_, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, snippetData)
	if err != nil {
		logger.Error("Failed to send create_site_snippet request", "SiteID", siteID, "Title", snippetData.Title, "Error", err)
		return fmt.Errorf("failed to send create_site_snippet request for site ID %s: %w", siteID, err)
	}

	// 如果 makeRequest 没有返回错误，我们认为 Netlify 接受了创建请求。
	// 即使响应体是 null，HTTP 状态码 201 也表示成功。
	logger.Info("Successfully requested site snippet creation (Netlify API returned success status)", "SiteID", siteID, "Title", snippetData.Title)
	return nil
}

// ListSiteSnippets 获取指定 Netlify 站点的所有代码片段。
// siteID: 必需，Netlify 站点的 ID。
// API 文档: GET /api/v1/sites/{site_id}/snippets
func (s *SnippetService) ListSiteSnippets(ctx context.Context, siteID string) ([]Snippet, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for listing snippets")
	}
	logger.Info("Listing site snippets", "SiteID", siteID)

	endpoint := fmt.Sprintf("/sites/%s/snippets", siteID)

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		logger.Error("Failed to list site snippets", "SiteID", siteID, "Error", err)
		return nil, fmt.Errorf("failed to send list_site_snippets request for site ID %s: %w", siteID, err)
	}

	if rawResponse == nil { // This condition might be unlikely given makeRequest's 204 handling returns "{}"
		logger.Info("ListSiteSnippets received no content (rawResponse is nil), returning empty list", "SiteID", siteID)
		return []Snippet{}, nil
	}

	var snippets []Snippet
	// If rawResponse is "{}", json.Unmarshal into []Snippet will error.
	// If rawResponse is "null", json.Unmarshal into []Snippet will result in a nil slice (which is fine).
	if err := json.Unmarshal(rawResponse, &snippets); err != nil {
		// If the API returned 204, makeRequest gives "{}". Unmarshaling "{}" into []Snippet fails.
		// This should ideally mean an empty list.
		// For now, we log the error and return it. A more robust solution might involve checking if string(rawResponse) == "{}"
		// and if so, returning []Snippet{}, nil. But that's a deeper change.
		logger.Error("Failed to unmarshal list_site_snippets response", "SiteID", siteID, "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal list_site_snippets response: %w", err)
	}

	logger.Info("Successfully listed site snippets", "SiteID", siteID, "Count", len(snippets))
	return snippets, nil
}

// GetSiteSnippet 获取指定 Netlify 站点上的特定代码片段。
// siteID: 必需，Netlify 站点的 ID。
// snippetID: 必需，代码片段的 ID。
// API 文档: GET /api/v1/sites/{site_id}/snippets/{snippet_id}
func (s *SnippetService) GetSiteSnippet(ctx context.Context, siteID string, snippetID string) (*Snippet, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for getting a snippet")
	}
	if snippetID == "" {
		return nil, fmt.Errorf("snippet ID cannot be empty for getting a snippet")
	}
	logger.Info("Getting site snippet", "SiteID", siteID, "SnippetID", snippetID)

	endpoint := fmt.Sprintf("/sites/%s/snippets/%s", siteID, snippetID)

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		// makeRequest should have already processed 404 Not Found etc. into APIError
		logger.Error("Failed to get site snippet", "SiteID", siteID, "SnippetID", snippetID, "Error", err)
		return nil, fmt.Errorf("failed to send get_site_snippet request for site ID %s, snippet ID %s: %w", siteID, snippetID, err)
	}

	var snippet Snippet
	if err := json.Unmarshal(rawResponse, &snippet); err != nil {
		logger.Error("Failed to unmarshal get_site_snippet response", "SiteID", siteID, "SnippetID", snippetID, "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal get_site_snippet response: %w", err)
	}

	if snippet.ID == "" {
		logger.Error("GetSiteSnippet returned a snippet with empty ID after unmarshal, indicating an empty or invalid response object.", "SiteID", siteID, "SnippetID", snippetID, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("get_site_snippet for site ID %s, snippet ID %s returned an empty or invalid snippet object (ID is empty)", siteID, snippetID)
	}

	logger.Info("Successfully retrieved site snippet", "SiteID", siteID, "SnippetID", snippet.ID)
	return &snippet, nil
}

// UpdateSiteSnippet 更新指定 Netlify 站点上的特定代码片段。
// siteID: 必需，Netlify 站点的 ID。
// snippetID: 必需，代码片段的 ID。
// snippetData: 包含要更新的代码片段信息的请求体。
// API 文档: PUT /api/v1/sites/{site_id}/snippets/{snippet_id}
// Netlify API 成功时返回更新后的 snippet 对象。
func (s *SnippetService) UpdateSiteSnippet(ctx context.Context, siteID string, snippetID string, snippetData *UpdateSnippetRequest) (*Snippet, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for updating a snippet")
	}
	if snippetID == "" {
		return nil, fmt.Errorf("snippet ID cannot be empty for updating a snippet")
	}
	if snippetData == nil {
		return nil, fmt.Errorf("snippet data cannot be nil for updating a snippet")
	}
	logger.Info("Updating site snippet", "SiteID", siteID, "SnippetID", snippetID)

	endpoint := fmt.Sprintf("/sites/%s/snippets/%s", siteID, snippetID)

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPut, endpoint, snippetData)
	if err != nil {
		logger.Error("Failed to send update_site_snippet request", "SiteID", siteID, "SnippetID", snippetID, "Error", err)
		return nil, fmt.Errorf("failed to send update_site_snippet request for site ID %s, snippet ID %s: %w", siteID, snippetID, err)
	}

	var snippet Snippet
	if err := json.Unmarshal(rawResponse, &snippet); err != nil {
		logger.Error("Failed to unmarshal update_site_snippet response", "SiteID", siteID, "SnippetID", snippetID, "Error", err, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal update_site_snippet response: %w", err)
	}

	if snippet.ID == "" {
		logger.Error("UpdateSiteSnippet returned a snippet with empty ID after unmarshal, indicating an empty or invalid response object.", "SiteID", siteID, "SnippetID", snippetID, "RawResponse", string(rawResponse))
		return nil, fmt.Errorf("update_site_snippet for site ID %s, snippet ID %s returned an empty or invalid snippet object (ID is empty)", siteID, snippetID)
	}

	logger.Info("Successfully updated site snippet", "SiteID", siteID, "SnippetID", snippet.ID)
	return &snippet, nil
}

// DeleteSiteSnippet 删除指定 Netlify 站点上的特定代码片段。
// siteID: 必需，Netlify 站点的 ID。
// snippetID: 必需，代码片段的 ID。
// API 文档: DELETE /api/v1/sites/{site_id}/snippets/{snippet_id}
// Netlify API 成功时通常返回 204 No Content。
func (s *SnippetService) DeleteSiteSnippet(ctx context.Context, siteID string, snippetID string) error {
	if siteID == "" {
		return fmt.Errorf("site ID cannot be empty for deleting a snippet")
	}
	if snippetID == "" {
		return fmt.Errorf("snippet ID cannot be empty for deleting a snippet")
	}
	logger.Info("Deleting site snippet", "SiteID", siteID, "SnippetID", snippetID)

	endpoint := fmt.Sprintf("/sites/%s/snippets/%s", siteID, snippetID)

	// makeRequest 会在 204 No Content 时返回 (json.RawMessage("{}"), nil)，这正是我们期望的成功删除情况。
	_, err := s.common.client.makeRequest(ctx, http.MethodDelete, endpoint, nil)
	if err != nil {
		logger.Error("Failed to send delete_site_snippet request", "SiteID", siteID, "SnippetID", snippetID, "Error", err)
		return fmt.Errorf("failed to send delete_site_snippet request for site ID %s, snippet ID %s: %w", siteID, snippetID, err)
	}

	logger.Info("Successfully deleted site snippet (or request accepted)", "SiteID", siteID, "SnippetID", snippetID)
	return nil
}

# Netlify Infrastructure Layer

本目录包含了与 Netlify API 交互的基础设施层代码，遵循 DDD 架构模式，提供了完整的 Netlify 服务集成功能。

## 目录结构

```
netlify/
├── client.go      # 核心客户端和通用请求处理
├── site.go        # 站点管理服务
├── deploy.go      # 部署管理服务
├── build.go       # 构建管理服务
├── hook.go        # Webhook 管理服务
├── snippet.go     # 代码片段管理服务
└── README.md      # 本文档
```

## 核心架构设计

### 1. 客户端设计模式

采用**组合模式**和**选项模式**设计客户端：

```go
// 选项模式配置客户端
client, err := NewClient(accessToken, 
    WithHTTPClient(customHTTPClient),
    WithBaseURL("https://custom-api.netlify.com/")
)

// 服务组合模式
service := &Service{
    client: client,
    Sites:    newSiteService(client),
    Deploys:  newDeployService(client),
    Hooks:    newHookService(client),
    Builds:   newBuildService(client),
    Snippets: newSnippetService(client),
}
```

### 2. 服务层设计

每个服务都嵌入 `service` 结构体，共享底层客户端：

```go
type SiteService struct {
    common service // 嵌入通用服务，包含 client 引用
}

type service struct {
    client *Client
}
```

## 文件功能说明

### client.go - 核心客户端
- **功能**: 提供统一的 HTTP 客户端和请求处理
- **核心组件**:
  - `Client`: 主客户端结构体
  - `APIError`: 统一的错误处理
  - `makeRequest`: 通用请求方法
  - `Service`: 服务聚合器

**设计特点**:
- 支持自定义 HTTP 客户端和 BaseURL
- 统一的错误处理和日志记录
- 速率限制监控和特殊处理
- 支持 204 No Content 响应

### site.go - 站点管理
- **功能**: 管理 Netlify 站点的创建、查询、更新、删除
- **核心方法**:
  - `CreateSite`: 创建站点（两步法：先创建站点，再更新构建设置）
  - `GetSite`: 获取站点详情
  - `ListSite`: 列出站点
  - `DeleteSite`: 删除站点

**设计特点**:
- 完整的站点信息模型（包含 80+ 字段）
- 支持自定义域名、密码保护、SSL 配置
- 灵活的构建设置配置

### deploy.go - 部署管理
- **功能**: 管理部署密钥和部署流程
- **核心方法**:
  - `CreateDeployKey`: 创建部署密钥
  - `CreateDeploy`: 创建部署
  - `GetDeploy`: 获取部署状态
  - `LockDeploy`/`UnlockDeploy`: 锁定/解锁部署
  - `ListSiteDeploys`: 列出站点部署

**设计特点**:
- 支持草稿部署和生产部署
- 完整的部署状态跟踪
- 部署锁定机制

### build.go - 构建管理
- **功能**: 触发构建和流式日志获取
- **核心方法**:
  - `CreateSiteBuild`: 触发站点构建
  - `StreamBuildLogs`: WebSocket 流式获取构建日志

**设计特点**:
- WebSocket 连接管理
- 实时日志流处理
- 上下文取消支持

### hook.go - Webhook 管理
- **功能**: 管理 Netlify Webhook
- **核心方法**:
  - `CreateHook`: 创建 Webhook

**设计特点**:
- 支持多种 Hook 类型（Slack、GitHub、Email、Webhook）
- 灵活的事件配置

### snippet.go - 代码片段管理
- **功能**: 管理站点的代码片段（HTML/JS 注入）
- **核心方法**:
  - `CreateSiteSnippet`: 创建代码片段
  - `ListSiteSnippets`: 列出代码片段
  - `GetSiteSnippet`: 获取代码片段
  - `UpdateSiteSnippet`: 更新代码片段
  - `DeleteSiteSnippet`: 删除代码片段

**设计特点**:
- 支持 head 和 footer 位置注入
- 完整的 CRUD 操作

## 代码风格特点

### 1. 错误处理
- 统一的 `APIError` 结构体
- 详细的错误日志记录
- 优雅的错误包装和传播

```go
if err != nil {
    logger.Error("Failed to create site", "SiteID", siteID, "Error", err)
    return nil, fmt.Errorf("failed to send create_site request: %w", err)
}
```

### 2. 日志记录
- 使用结构化日志
- 英文日志消息（符合项目规范）
- 详细的调试信息

```go
logger.Info("Creating Netlify site", "Name", opts.Name, "CustomDomain", opts.CustomDomain)
logger.Debug("Netlify request", "Method", method, "URL", fullURL.String())
```

### 3. 参数验证
- 严格的空值检查
- 清晰的错误消息
- 防御性编程

```go
if siteID == "" {
    return nil, fmt.Errorf("site ID cannot be empty for creating a deploy")
}
```

### 4. 类型安全
- 强类型的数据结构
- JSON 标签映射
- 可选字段使用 `omitempty`

### 5. 上下文支持
- 所有方法都支持 `context.Context`
- 支持超时和取消
- WebSocket 连接也支持上下文

## 使用示例

```go
// 创建服务实例
netlifyService, err := NewService(accessToken)
if err != nil {
    log.Fatal(err)
}

// 创建站点
site, err := netlifyService.Sites.CreateSite(ctx, &CreateSiteOptions{
    Name: "my-site",
    CustomDomain: "example.com",
    BuildSettings: &SiteBuildSettingsInfo{
        RepoURL: "https://github.com/user/repo",
        RepoBranch: "main",
        Provider: "github",
    },
})

// 创建部署
deploy, err := netlifyService.Deploys.CreateDeploy(ctx, site.ID, &CreateDeployRequest{
    Title: "Production deployment",
    Branch: "main",
})

// 流式获取构建日志
logs, err := netlifyService.Builds.StreamBuildLogs(ctx, deploy.ID, site.ID)
```

## 设计原则

1. **单一职责**: 每个服务只负责特定的功能领域
2. **依赖注入**: 通过构造函数注入依赖
3. **接口隔离**: 每个服务都有明确的职责边界
4. **错误处理**: 统一的错误处理策略
5. **可测试性**: 支持自定义 HTTP 客户端和 BaseURL
6. **可扩展性**: 易于添加新的 API 端点和服务

## 注意事项

1. **速率限制**: 代码中包含了速率限制监控，但需要在实际使用时注意 API 调用频率
2. **WebSocket 连接**: 构建日志流需要稳定的网络连接
3. **错误重试**: 当前实现没有自动重试机制，需要在上层应用处理
4. **并发安全**: 客户端实例不是并发安全的，需要为每个 goroutine 创建独立的实例 
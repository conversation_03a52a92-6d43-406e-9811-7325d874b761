package netlify

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gorilla/websocket"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// Build 表示 Netlify 构建的详细信息。
// 注意：这个结构体基于 Python 示例中的 BuildResponse，可能需要根据实际的 Netlify API 响应进行调整。
// 通常，触发构建的响应可能比较简单，只包含 ID 和状态，而获取构建详情的响应会更丰富。
type Build struct {
	ID                  string         `json:"id"`                              // 构建的唯一 ID
	DeployID            string         `json:"deploy_id"`                       // 此构建关联的部署 ID
	SiteID              string         `json:"site_id"`                         // 此构建关联的站点 ID
	State               string         `json:"state"`                           // 构建状态，例如 "enqueued", "building", "ready", "error"
	ErrorMessage        string         `json:"error_message,omitempty"`         // 如果构建失败，此字段包含错误信息
	CreatedAt           string         `json:"created_at"`                      // 构建创建时间
	UpdatedAt           string         `json:"updated_at"`                      // 构建最后更新时间
	Sha                 string         `json:"sha,omitempty"`                   // 触发构建的 commit SHA (如果适用)
	Done                bool           `json:"done"`                            // 构建是否已完成 (成功或失败)
	Error               string         `json:"error,omitempty"`                 // 详细的错误信息或名称
	LogAccessAttributes map[string]any `json:"log_access_attributes,omitempty"` // 用于访问构建日志的属性
	// 还有许多其他可能的字段，如 build_settings, branch, commit_url, deploy_time, etc.
	// 这里仅包含 Python 示例中暗示或直接使用的一些关键字段。
}

// netlifyLogAuthPayload 定义了发送到 Netlify WebSocket 的认证消息结构。
type netlifyLogAuthPayload struct {
	DeployID    string `json:"deploy_id"`
	SiteID      string `json:"site_id"`
	AccessToken string `json:"access_token"`
}

// BuildService 提供了操作 Netlify 构建的相关方法。
type BuildService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newBuildService 创建一个新的 BuildService 实例。
func newBuildService(client *Client) *BuildService {
	return &BuildService{common: service{client: client}}
}

// CreateSiteBuild 触发指定站点的 Netlify 构建。
// siteID: 必需，要触发构建的 Netlify 站点的 ID。
// API 文档: https://open-api.netlify.com/#/default/createSiteBuild
func (s *BuildService) CreateSiteBuild(ctx context.Context, siteID string) (*Build, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for triggering a build")
	}
	logger.Info("Triggering new build for site", "SiteID:", siteID)

	endpoint := fmt.Sprintf("/sites/%s/builds", siteID)

	// 触发构建通常不需要请求体，但根据 Netlify API 可能需要一个空的 JSON 对象 {}。
	// 如果不需要，可以传递 nil。为了安全起见，先传递一个空对象，如果 API 不接受，再改为 nil。
	// Python 示例中没有传递 body，所以我们这里也传递 nil。
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, nil)
	if err != nil {
		logger.Error("Failed to trigger site build", "SiteID:", siteID, "Error:", err)
		return nil, fmt.Errorf("failed to send create_site_build request for site ID %s: %w", siteID, err)
	}

	var buildResponse Build
	if err := json.Unmarshal(rawResponse, &buildResponse); err != nil {
		logger.Error("Failed to unmarshal create_site_build response", "SiteID:", siteID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal create_site_build response: %w", err)
	}

	logger.Info("Successfully triggered site build", "SiteID:", siteID, "BuildID:", buildResponse.ID, "State:", buildResponse.State)
	return &buildResponse, nil
}

// StreamBuildLogs 通过 WebSocket 流式传输指定部署的构建日志。
// deployID: 必需，Netlify 部署 ID。
// siteID: 必需，Netlify 站点 ID。
// 返回一个包含所有日志消息的列表，每条消息都是一个 map[string]any。
func (s *BuildService) StreamBuildLogs(ctx context.Context, deployID, siteID string) ([]map[string]any, error) {
	if s.common.client.accessToken == "" {
		logger.Error("Netlify access token is not configured.")
		return nil, fmt.Errorf("netlify access token is not configured")
	}
	if deployID == "" {
		return nil, fmt.Errorf("deploy ID cannot be empty for streaming logs")
	}
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for streaming logs")
	}

	wsURL := "wss://socketeer.services.netlify.com/build/logs"
	authPayload := netlifyLogAuthPayload{
		DeployID:    deployID,
		SiteID:      siteID,
		AccessToken: s.common.client.accessToken,
	}

	var logs []map[string]any

	logger.Info("Preparing to connect to WebSocket for build logs", "URL", wsURL, "DeployID", deployID, "SiteID", siteID)

	// 使用 DialContext 以便遵循传入的 context
	conn, _, err := websocket.DefaultDialer.DialContext(ctx, wsURL, nil)
	if err != nil {
		logger.Error("Failed to connect to WebSocket", "URL", wsURL, "Error", err)
		return nil, fmt.Errorf("failed to connect to WebSocket at %s: %w", wsURL, err)
	}
	defer conn.Close() // 确保连接在使用后关闭

	logger.Info("WebSocket connection established.")

	// 发送认证消息
	authPayloadBytes, err := json.Marshal(authPayload)
	if err != nil {
		logger.Error("Failed to marshal auth payload", "Error", err)
		return nil, fmt.Errorf("failed to marshal auth payload: %w", err)
	}

	if err := conn.WriteMessage(websocket.TextMessage, authPayloadBytes); err != nil {
		logger.Error("Failed to send auth message via WebSocket", "Error", err)
		return nil, fmt.Errorf("failed to send auth message: %w", err)
	}
	logger.Info("Auth message sent successfully via WebSocket", "DeployID", deployID, "SiteID", siteID)

	// 接收消息
	for {
		select {
		case <-ctx.Done(): // 检查上下文是否已取消
			logger.Info("Context cancelled, closing WebSocket connection.")
			// 发送一个关闭消息（可选，取决于服务器行为）
			// err := conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			// if err != nil {
			//	 logger.Error("Error sending close message", "Error", err)
			// }
			return logs, ctx.Err()
		default:
			messageType, message, err := conn.ReadMessage()
			if err != nil {
				// 检查是否是正常的关闭错误
				if websocket.IsUnexpectedCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
					logger.Error("Error reading message from WebSocket (unexpected close)", "Error", err)
					return logs, fmt.Errorf("error reading message from WebSocket: %w", err)
				}
				// 其他类型的错误，可能是正常的连接关闭
				logger.Info("WebSocket connection closed or read error", "Error", err)
				return logs, nil // 正常结束或已知错误
			}

			if messageType == websocket.TextMessage || messageType == websocket.BinaryMessage {
				var msgData map[string]any
				if err := json.Unmarshal(message, &msgData); err != nil {
					logger.Error("Failed to unmarshal log message from WebSocket", "Error", err, "RawMessage", string(message))
					// 可以选择跳过这条消息或中断
					continue // 继续接收下一条消息
				}
				logs = append(logs, msgData)
				logger.Debug("Received log message", "Data", msgData)

				// 检查是否是最后一条消息
				if eventType, ok := msgData["type"].(string); ok && eventType == "report" {
					logger.Info("Received 'report' type log message, finishing log streaming.", "DeployID", deployID)
					return logs, nil
				}
			}
		}
	}
}

package netlify

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/retry"
)

const (
	defaultBaseURL = "https://api.netlify.com/api/v1/"
	defaultTimeout = 60 * time.Second
)

// APIError 表示 Netlify API 返回的错误。
type APIError struct {
	StatusCode int    `json:"-"` // HTTP 状态码
	Message    string `json:"message,omitempty"`
	Code       any    `json:"code,omitempty"` // Netlify特有的错误码，有时是int，有时是string
	// Netlify的错误响应结构多样，可能包含其他字段如 "errors", "request_id" 等
	// 例如: {"code":404,"message":"Not found"} 或 {"code":"whatever_code", "message":"..."}
	// 使用 any 来适应不同类型的 code，或者根据具体观察到的类型来确定
}

func (e *APIError) Error() string {
	return fmt.Sprintf("Netlify API error (status %d): %s (code: %v)", e.StatusCode, e.Message, e.Code)
}

// ClientOption 定义了用于配置Netlify客户端的选项函数类型。
type ClientOption func(*Client)

// WithHTTPClient 允许用户提供自定义的http.Client。
func WithHTTPClient(httpClient *http.Client) ClientOption {
	return func(c *Client) {
		c.httpClient = httpClient
	}
}

// WithBaseURL 允许用户指定自定义的BaseURL，主要用于测试。
func WithBaseURL(baseURLString string) ClientOption {
	return func(c *Client) {
		p, err := url.Parse(baseURLString)
		if err == nil {
			if !strings.HasSuffix(p.Path, "/") {
				p.Path += "/"
			}
			c.baseURL = p
		} else {
			logger.Warn("Failed to parse custom baseURL, using default", "Error", err, "CustomURL", baseURLString)
		}
	}
}

// Client 是与Netlify API交互的客户端。
type Client struct {
	baseURL     *url.URL
	httpClient  *http.Client
	accessToken string
}

// NewClient 创建一个新的Netlify客户端实例。
func NewClient(accessToken string, opts ...ClientOption) (*Client, error) {
	if accessToken == "" {
		return nil, fmt.Errorf("netlify access token cannot be empty")
	}

	baseURL, _ := url.Parse(defaultBaseURL)

	c := &Client{
		baseURL:     baseURL,
		accessToken: accessToken,
		httpClient:  &http.Client{Timeout: defaultTimeout},
	}

	for _, opt := range opts {
		opt(c)
	}

	if c.baseURL != nil && !strings.HasSuffix(c.baseURL.Path, "/") {
		c.baseURL.Path += "/"
	}

	return c, nil
}

// makeRequest 是一个通用的辅助函数，用于向Netlify API发出HTTP请求。
// 它处理URL构建、认证、JSON序列化和错误处理。
// 'path' 参数现在应该是相对路径，不以 '/' 开头 (e.g., "deploy_keys", "sites/{site_id}/snippets")
func (c *Client) makeRequest(ctx context.Context, method, path string, body any) (json.RawMessage, error) {
	retryConfig := retry.NetworkRetryConfig()

	requestFunc := func() (json.RawMessage, error) {
		cleanedPath := strings.TrimLeft(path, "/")

		rel, err := url.Parse(cleanedPath)
		if err != nil {
			return nil, fmt.Errorf("failed to parse relative path '%s': %w", cleanedPath, err)
		}

		fullURL := c.baseURL.ResolveReference(rel)

		var reqBody io.Reader
		if body != nil {
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal request body: %w", err)
			}
			reqBody = bytes.NewBuffer(jsonBody)
			logger.Debug("Netlify request body", "Method", method, "URL", fullURL.String(), "Body", string(jsonBody))
		} else {
			logger.Debug("Netlify request", "Method", method, "URL", fullURL.String())
		}

		req, err := http.NewRequestWithContext(ctx, method, fullURL.String(), reqBody)
		if err != nil {
			return nil, fmt.Errorf("failed to create HTTP request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+c.accessToken)
		if body != nil {
			req.Header.Set("Content-Type", "application/json")
		}

		resp, err := c.httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("failed to execute HTTP request: %w", err)
		}
		defer resp.Body.Close()

		respBodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response body: %w", err)
		}

		// 打印Netlify API速率限制相关header和请求url
		rateLimitLimit := resp.Header.Get("X-RateLimit-Limit")
		rateLimitRemaining := resp.Header.Get("X-RateLimit-Remaining")
		rateLimitReset := resp.Header.Get("X-RateLimit-Reset")
		logger.Debug("Netlify RateLimit headers:", "URL", fullURL.String(), "X-RateLimit-Limit", rateLimitLimit, "X-RateLimit-Remaining", rateLimitRemaining, "X-RateLimit-Reset", rateLimitReset)

		logger.Debug("Netlify response", "Status", resp.Status, "BodyLength", len(respBodyBytes))
		if len(respBodyBytes) > 0 {
			// logger.Debug("Netlify response body (raw)", "Body", string(respBodyBytes)) // 仅在调试时开启，可能包含敏感信息
		}

		if resp.StatusCode == 429 { //  特定处理 429 Too Many Requests
			apiErr := &APIError{StatusCode: resp.StatusCode}
			if jsonErr := json.Unmarshal(respBodyBytes, apiErr); jsonErr != nil {
				apiErr.Message = string(respBodyBytes) // 使用原始响应体
				logger.Error("Failed to unmarshal Netlify API error response for 429, using raw body", "UnmarshalError", jsonErr, "RawBody", string(respBodyBytes))
			}
			// 即使 apiErr.Message 解析失败，也要记录速率限制信息
			logger.Error("Netlify API rate limit exceeded (429)",
				"URL", fullURL.String(),
				"Status", resp.Status,
				"ErrorCode", apiErr.Code, // apiErr.Code 可能为空，如果上面 unmarshal 失败
				"ErrorMessage", apiErr.Message,
				"X-RateLimit-Limit", rateLimitLimit,
				"X-RateLimit-Remaining", rateLimitRemaining,
				"X-RateLimit-Reset", rateLimitReset,
			)
			return nil, apiErr // 返回解析出的或构造的 apiErr
		} else if resp.StatusCode >= 400 { // 原有的 >= 400 错误处理
			apiErr := &APIError{StatusCode: resp.StatusCode}
			if jsonErr := json.Unmarshal(respBodyBytes, apiErr); jsonErr != nil {
				// 如果解析到APIError失败，使用原始响应体作为错误信息
				apiErr.Message = string(respBodyBytes)
				logger.Error("Failed to unmarshal Netlify API error response, using raw body", "UnmarshalError", jsonErr, "RawBody", string(respBodyBytes))
			}
			logger.Error("Netlify API error", "URL", fullURL.String(), "Status", resp.Status, "ErrorCode", apiErr.Code, "ErrorMessage", apiErr.Message)
			return nil, apiErr
		}

		// 对于 204 No Content，respBodyBytes可能是空的，这通常是正常的
		if resp.StatusCode == http.StatusNoContent && len(respBodyBytes) == 0 {
			return json.RawMessage("{}"), nil // 返回一个空的JSON对象，避免Unmarshal(nil)错误
		}

		return respBodyBytes, nil
	}

	return retry.DoWithResult(ctx, retryConfig, requestFunc)
}

// service 是一个嵌入式结构体，用于共享底层的 Netlify Client 实例。
type service struct {
	client *Client
}

// Service 是访问不同 Netlify API 服务的主入口点。
// 每个内嵌的服务（如 Sites, DeployKeys）都共享同一个底层HTTP客户端和认证信息。
type Service struct {
	client *Client // 底层客户端，主要用于内部或直接调用 makeRequest

	Sites    *SiteService
	Deploys  *DeployService
	Hooks    *HookService
	Builds   *BuildService
	Snippets *SnippetService // 新增 SnippetService
}

// NewService 创建并初始化所有 Netlify API 服务。
// accessToken: 您的 Netlify 个人访问令牌。
// opts: ClientOption，例如 WithHTTPClient 或 WithBaseURL。
func NewService(accessToken string, opts ...ClientOption) (*Service, error) {
	client, err := NewClient(accessToken, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create netlify base client: %w", err)
	}

	s := &Service{
		client: client,
	}
	s.Sites = newSiteService(client)
	s.Deploys = newDeployService(client)
	s.Hooks = newHookService(client)
	s.Builds = newBuildService(client)
	s.Snippets = newSnippetService(client) // 初始化 SnippetService

	return s, nil
}

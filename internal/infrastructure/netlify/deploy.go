package netlify

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// DeployKey 表示 Netlify 部署密钥的信息。
type DeployKey struct {
	ID        string `json:"id"`
	PublicKey string `json:"public_key"`
	CreatedAt string `json:"created_at"` // 例如 "2016-08-09T22:03:20.000Z"
}

// CreateDeployRequest 定义了创建新部署时可以传递的参数。
// Title, Branch, Draft 都是可选的。
type CreateDeployRequest struct {
	Title  string `json:"title,omitempty"`  // 部署的标题
	Branch string `json:"branch,omitempty"` // 要部署的分支名
	Draft  bool   `json:"draft,omitempty"`  // 是否为草稿部署 (true) 或生产部署 (false)
}

// Deploy 代表一个 Netlify 部署的详细信息。
// 这个结构体整合了创建部署、获取部署状态以及锁定/解锁部署时可能返回的字段。
type Deploy struct {
	ID                string   `json:"id"`
	SiteID            string   `json:"site_id"`
	BuildID           string   `json:"build_id,omitempty"` // 构建 ID，可能在某些早期状态下不存在
	State             string   `json:"state"`              // 例如 "uploading", "uploaded", "processing", "prepared", "building", "ready", "error"
	Name              string   `json:"name,omitempty"`     // 部署的名称/标题 (Netlify API 中有时用 'name' 有时用 'title')
	Title             string   `json:"title,omitempty"`    // 部署的标题 (兼容 Python 示例)
	URL               string   `json:"url"`
	SSLURL            string   `json:"ssl_url"`
	DeployURL         string   `json:"deploy_url"`
	DeploySSLURL      string   `json:"deploy_ssl_url"`
	CreatedAt         string   `json:"created_at"`
	UpdatedAt         string   `json:"updated_at,omitempty"`
	Branch            string   `json:"branch,omitempty"`
	Locked            bool     `json:"locked,omitempty"`             // 部署是否被锁定
	ErrorMessage      string   `json:"error_message,omitempty"`      // 如果部署失败，此字段包含错误信息
	CommitRef         string   `json:"commit_ref,omitempty"`         // 部署关联的 commit SHA
	CommitURL         string   `json:"commit_url,omitempty"`         // 部署关联的 commit 的 URL
	ReviewID          *int     `json:"review_id,omitempty"`          // 如果是 PR 部署，关联的 review ID
	ReviewURL         string   `json:"review_url,omitempty"`         // 如果是 PR 部署，关联的 review URL
	PublishedAt       string   `json:"published_at,omitempty"`       // 部署发布的时间
	Context           string   `json:"context,omitempty"`            // 例如 "production", "deploy-preview", "branch-deploy"
	DeployTime        *int     `json:"deploy_time,omitempty"`        // 部署耗时（秒）
	BuildTime         *int     `json:"build_time,omitempty"`         // 构建耗时（秒）
	Required          []string `json:"required,omitempty"`           // 部署所需的函数
	RequiredFunctions []string `json:"required_functions,omitempty"` // 部署所需的函数 (兼容不同API版本)
}

// DeployService 提供了操作 Netlify 部署的相关方法。
type DeployService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newDeployService 创建一个新的 DeployService 实例。
func newDeployService(client *Client) *DeployService {
	return &DeployService{common: service{client: client}}
}

// CreateDeployKey 创建一个新的 Netlify 部署密钥。
// API 文档: https://open-api.netlify.com/#/default/createDeployKey
func (s *DeployService) CreateDeployKey(ctx context.Context) (*DeployKey, error) {
	logger.Info("Creating Netlify deploy key using DeployService")

	endpoint := "/deploy_keys"
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, nil)
	if err != nil {
		logger.Error("Failed to create deploy key", "Error:", err)
		return nil, fmt.Errorf("failed to send create_deploy_key request: %w", err)
	}

	var deployKeyResponse DeployKey
	if err := json.Unmarshal(rawResponse, &deployKeyResponse); err != nil {
		logger.Error("Failed to unmarshal create_deploy_key response", "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	logger.Info("Successfully created deploy key", "ID:", deployKeyResponse.ID)
	return &deployKeyResponse, nil
}

// DeleteDeployKey deletes a deploy key on Netlify by its ID.
// API 文档: https://open-api.netlify.com/#/default/deleteDeployKey
func (s *DeployService) DeleteDeployKey(ctx context.Context, keyID string) error {
	if keyID == "" {
		return fmt.Errorf("deploy key ID cannot be empty for deletion")
	}
	logger.Info("Deleting Netlify deploy key", "KeyID:", keyID)

	endpoint := fmt.Sprintf("/deploy_keys/%s", keyID)

	_, err := s.common.client.makeRequest(ctx, http.MethodDelete, endpoint, nil)
	if err != nil {
		if apiErr, ok := err.(*APIError); ok && apiErr.StatusCode == http.StatusNotFound {
			logger.Warn("Deploy key not found for deletion, might have been already deleted", "KeyID:", keyID)
			return nil // Consider this not an error for cleanup purposes
		}
		logger.Error("Failed to delete deploy key", "KeyID:", keyID, "Error:", err)
		return fmt.Errorf("failed to send delete_deploy_key request for key ID %s: %w", keyID, err)
	}

	logger.Info("Successfully requested deploy key deletion", "KeyID:", keyID)
	return nil
}

// CreateDeploy 为指定的站点创建一个新的部署。
// siteID: 必需，要部署到的站点 ID。
// deployData: 可选参数，用于指定部署的标题、分支或是否为草稿。如果为 nil，则会创建一个默认的生产部署。
// API 文档: https://open-api.netlify.com/#/default/createSiteDeploy
func (s *DeployService) CreateDeploy(ctx context.Context, siteID string, deployData *CreateDeployRequest) (*Deploy, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for creating a deploy")
	}

	if deployData != nil {
		logger.Info("Creating deploy for site", "SiteID:", siteID, "Title:", deployData.Title, "Branch:", deployData.Branch, "Draft:", deployData.Draft)
	} else {
		logger.Info("Creating deploy for site with default parameters", "SiteID:", siteID)
	}

	endpoint := fmt.Sprintf("/sites/%s/deploys", siteID)

	var requestBody any
	if deployData != nil {
		requestBody = deployData
	}

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, requestBody)
	if err != nil {
		logger.Error("Failed to create deploy", "SiteID:", siteID, "Error:", err)
		return nil, fmt.Errorf("failed to send create_deploy request for site ID %s: %w", siteID, err)
	}

	var deployResponse Deploy
	if err := json.Unmarshal(rawResponse, &deployResponse); err != nil {
		logger.Error("Failed to unmarshal create_deploy response", "SiteID:", siteID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal create_deploy response: %w", err)
	}

	logger.Info("Successfully created deploy", "SiteID:", siteID, "DeployID:", deployResponse.ID, "State:", deployResponse.State)
	return &deployResponse, nil
}

// GetDeploy 获取指定部署的详细信息，包括其状态。
// deployID: 必需，要获取的部署的 ID。
// API 文档: https://open-api.netlify.com/#/default/getDeploy
func (s *DeployService) GetDeploy(ctx context.Context, deployID string) (*Deploy, error) {
	if deployID == "" {
		return nil, fmt.Errorf("deploy ID cannot be empty for fetching deploy status")
	}
	logger.Info("Getting deploy status", "DeployID:", deployID)

	endpoint := fmt.Sprintf("/deploys/%s", deployID)
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		logger.Error("Failed to get deploy status", "DeployID:", deployID, "Error:", err)
		return nil, fmt.Errorf("failed to send get_deploy_status request for deploy ID %s: %w", deployID, err)
	}

	var deployResponse Deploy
	if err := json.Unmarshal(rawResponse, &deployResponse); err != nil {
		logger.Error("Failed to unmarshal get_deploy_status response", "DeployID:", deployID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal get_deploy_status response: %w", err)
	}

	logger.Info("Successfully fetched deploy status", "DeployID:", deployResponse.ID, "State:", deployResponse.State, "ErrorMessage:", deployResponse.ErrorMessage)
	return &deployResponse, nil
}

// LockDeploy 锁定指定的 Netlify 部署。
// deployID: 必需，要锁定的部署的 ID。
// API 文档: https://open-api.netlify.com/#/default/lockDeploy
func (s *DeployService) LockDeploy(ctx context.Context, deployID string) (*Deploy, error) {
	if deployID == "" {
		return nil, fmt.Errorf("deploy ID cannot be empty for locking a deploy")
	}
	logger.Info("Locking deploy", "DeployID:", deployID)

	endpoint := fmt.Sprintf("/deploys/%s/lock", deployID)
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, nil)
	if err != nil {
		logger.Error("Failed to lock deploy", "DeployID:", deployID, "Error:", err)
		return nil, fmt.Errorf("failed to send lock_deploy request for deploy ID %s: %w", deployID, err)
	}

	var deployResponse Deploy
	if err := json.Unmarshal(rawResponse, &deployResponse); err != nil {
		logger.Error("Failed to unmarshal lock_deploy response", "DeployID:", deployID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal lock_deploy response: %w", err)
	}

	logger.Info("Successfully locked deploy", "DeployID:", deployResponse.ID, "Locked:", deployResponse.Locked)
	return &deployResponse, nil
}

// UnlockDeploy 解锁指定的 Netlify 部署。
// deployID: 必需，要解锁的部署的 ID。
// API 文档: https://open-api.netlify.com/#/default/unlockDeploy
func (s *DeployService) UnlockDeploy(ctx context.Context, deployID string) (*Deploy, error) {
	if deployID == "" {
		return nil, fmt.Errorf("deploy ID cannot be empty for unlocking a deploy")
	}
	logger.Info("Unlocking deploy", "DeployID:", deployID)

	endpoint := fmt.Sprintf("/deploys/%s/unlock", deployID)
	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodPost, endpoint, nil)
	if err != nil {
		logger.Error("Failed to unlock deploy", "DeployID:", deployID, "Error:", err)
		return nil, fmt.Errorf("failed to send unlock_deploy request for deploy ID %s: %w", deployID, err)
	}

	var deployResponse Deploy
	if err := json.Unmarshal(rawResponse, &deployResponse); err != nil {
		logger.Error("Failed to unmarshal unlock_deploy response", "DeployID:", deployID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal unlock_deploy response: %w", err)
	}

	logger.Info("Successfully unlocked deploy", "DeployID:", deployResponse.ID, "Locked:", deployResponse.Locked)
	return &deployResponse, nil
}

// ListSiteDeploysOptions 定义了列出站点部署时可以传递的可选参数。
type ListSiteDeploysOptions struct {
	Page    int // 页码 (从 1 开始)
	PerPage int // 每页数量
	// 可以根据 Netlify API 文档添加其他过滤参数，例如 branch, deploy-previews, production, state 等。
}

// ListSiteDeploys 获取指定站点的部署列表。
// siteID: 必需，Netlify 站点的 ID。
// opts: 可选参数，用于分页等。如果为 nil，则使用 API 默认值。
// API 文档: https://open-api.netlify.com/#/default/listSiteDeploys
func (s *DeployService) ListSiteDeploys(ctx context.Context, siteID string, opts *ListSiteDeploysOptions) ([]Deploy, error) {
	if siteID == "" {
		return nil, fmt.Errorf("site ID cannot be empty for listing deploys")
	}
	logger.Info("Listing deploys for site", "SiteID:", siteID)

	baseEndpoint := fmt.Sprintf("/sites/%s/deploys", siteID)
	queryParams := url.Values{}

	if opts != nil {
		if opts.Page > 0 {
			queryParams.Set("page", strconv.Itoa(opts.Page))
		}
		if opts.PerPage > 0 {
			queryParams.Set("per_page", strconv.Itoa(opts.PerPage))
		}
	}

	endpoint := baseEndpoint
	if len(queryParams) > 0 {
		endpoint = fmt.Sprintf("%s?%s", baseEndpoint, queryParams.Encode())
	}

	rawResponse, err := s.common.client.makeRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		logger.Error("Failed to list site deploys", "SiteID:", siteID, "Error:", err)
		return nil, fmt.Errorf("failed to send list_site_deploys request for site ID %s: %w", siteID, err)
	}

	var deploysResponse []Deploy
	if err := json.Unmarshal(rawResponse, &deploysResponse); err != nil {
		logger.Error("Failed to unmarshal list_site_deploys response", "SiteID:", siteID, "Error:", err, "RawResponse:", string(rawResponse))
		return nil, fmt.Errorf("failed to unmarshal list_site_deploys response: %w", err)
	}

	logger.Info("Successfully listed site deploys", "SiteID:", siteID, "Count:", len(deploysResponse))
	return deploysResponse, nil
}

package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/repository"
)

// agentState 存储 agent 的完整状态，包括实体和连接
type agentState struct {
	agent *entity.Agent
	conn  *AgentConnection
}

// agentRepositoryImpl 实现 AgentRepository 接口，使用内存存储 agent 连接
type agentRepositoryImpl struct {
	agents sync.Map // key: agentID, value: *agentState
}

// NewAgentRepositoryImpl 创建 agentRepositoryImpl 实例
func NewAgentRepositoryImpl() repository.AgentRepository {
	return &agentRepositoryImpl{}
}

// Register 注册 agent 连接
func (r *agentRepositoryImpl) Register(ctx context.Context, agent *entity.Agent) error {
	conn, ok := ctx.Value(AgentConnKey).(*AgentConnection)
	if !ok {
		return fmt.Errorf("agent connection not found in context for agent: %s", agent.ID)
	}
	r.agents.Store(agent.ID, &agentState{agent: agent, conn: conn})
	return nil
}

// Unregister 注销 agent 连接
func (r *agentRepositoryImpl) Unregister(ctx context.Context, agentID string) error {
	if v, ok := r.agents.Load(agentID); ok {
		// 先从 map 中删除，避免并发访问已关闭的连接
		r.agents.Delete(agentID)
		if state, ok := v.(*agentState); ok {
			if err := state.conn.Close(); err != nil {
				// 记录或返回关闭连接时的错误
				return fmt.Errorf("failed to close connection for agent %s: %w", agentID, err)
			}
		}
	}
	return nil
}

// GetByID 根据 ID 获取 agent 实体
func (r *agentRepositoryImpl) GetByID(ctx context.Context, id string) (*entity.Agent, error) {
	if v, ok := r.agents.Load(id); ok {
		if state, ok := v.(*agentState); ok {
			return state.agent, nil
		}
	}
	return nil, fmt.Errorf("agent with id %s not found", id)
}

// UpdateLastActive 更新 agent 的最后活跃时间
func (r *agentRepositoryImpl) UpdateLastActive(ctx context.Context, id string, lastActive int64) error {
	if v, ok := r.agents.Load(id); ok {
		if state, ok := v.(*agentState); ok {
			state.agent.LastActive = lastActive
			return nil
		}
	}
	return fmt.Errorf("agent with id %s not found for updating last active time", id)
}

// ListAgents 获取所有已注册 agent
func (r *agentRepositoryImpl) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	var agents []*entity.Agent
	r.agents.Range(func(key, value any) bool {
		if state, ok := value.(*agentState); ok {
			agents = append(agents, state.agent)
		}
		return true
	})
	return agents, nil
}

// SendCommand 向指定 agent 发送命令
func (r *agentRepositoryImpl) SendCommand(ctx context.Context, agentID string, cmd *entity.Command) error {
	if v, ok := r.agents.Load(agentID); ok {
		if state, ok := v.(*agentState); ok {
			msg, err := json.Marshal(cmd)
			if err != nil {
				return err
			}
			return state.conn.Send(ctx, msg)
		}
	}
	return fmt.Errorf("agent with id %s not found for sending command", agentID)
}

// SendCommandAndWait 向指定 agent 发送命令并等待响应
// 会自动忽略 ping/pong 心跳消息，只返回业务相关的响应
func (r *agentRepositoryImpl) SendCommandAndWait(ctx context.Context, agentID string, cmd *entity.Command) ([]byte, error) {
	if v, ok := r.agents.Load(agentID); ok {
		if state, ok := v.(*agentState); ok {
			if err := state.conn.SendJSON(ctx, cmd); err != nil {
				return nil, fmt.Errorf("failed to send command %s to agent %s: %w", cmd.Type, agentID, err)
			}

			// 循环等待响应，跳过 ping/pong 心跳消息
			for {
				msg, err := state.conn.ReadResponse(ctx)
				if err != nil {
					return nil, fmt.Errorf("failed to read response for command %s from agent %s: %w", cmd.Type, agentID, err)
				}

				// 检查是否为 pong 消息，如果是则跳过继续等待
				var response map[string]any
				if err := json.Unmarshal(msg, &response); err == nil {
					if message, ok := response["message"].(string); ok && (message == "pong" || message == "ping") {
						// 跳过 ping/pong 心跳消息，继续等待业务响应
						continue
					}
				}

				// 返回非心跳消息
				return msg, nil
			}
		}
	}
	return nil, fmt.Errorf("agent with id %s not found for sending command and waiting", agentID)
}

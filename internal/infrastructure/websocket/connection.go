package websocket

import (
	"context"
	"encoding/json"
	"sync"

	"github.com/coder/websocket"
)

// AgentConnection 封装 websocket 连接和并发安全操作

type contextKey string

const AgentConnKey contextKey = "agentConn"

type AgentConnection struct {
	Conn         *websocket.Conn // websocket 连接对象
	mu           *sync.Mutex     // 保护写操作
	responseChan chan []byte     // 用于从 reader goroutine 接收非心跳响应
}

// NewAgentConnection 创建一个新的 AgentConnection
func NewAgentConnection(conn *websocket.Conn) *AgentConnection {
	return &AgentConnection{
		Conn: conn,
		mu:   &sync.Mutex{},
		// 使用带缓冲的 channel，避免 reader 在没有接收者时阻塞
		responseChan: make(chan []byte, 1),
	}
}

// PushResponse 是由唯一的 reader goroutine 调用的，用于将消息推送给等待的业务逻辑
func (c *AgentConnection) PushResponse(ctx context.Context, data []byte) {
	select {
	case c.responseChan <- data:
	case <-ctx.Done():
	default:
		// 如果 channel 已满且没有接收者，则丢弃消息，避免阻塞 reader
	}
}

// ReadResponse 从 channel 中读取一个响应
func (c *AgentConnection) ReadResponse(ctx context.Context) ([]byte, error) {
	select {
	case data := <-c.responseChan:
		return data, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// SendJSON 安全地发送 JSON 消息
func (c *AgentConnection) SendJSON(ctx context.Context, v interface{}) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	data, err := json.Marshal(v)
	if err != nil {
		return err
	}

	return c.Conn.Write(ctx, websocket.MessageText, data)
}

// Send 发送原始消息（保持以满足 agentRepositoryImpl 的 SendCommand 需求）
func (ac *AgentConnection) Send(ctx context.Context, message []byte) error {
	ac.mu.Lock()
	defer ac.mu.Unlock()
	return ac.Conn.Write(ctx, websocket.MessageText, message)
}

// Close 关闭连接和 channel
func (ac *AgentConnection) Close() error {
	close(ac.responseChan)
	return ac.Conn.Close(websocket.StatusNormalClosure, "server closed")
}

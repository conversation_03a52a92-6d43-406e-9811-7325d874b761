package cloudflare

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/kv"
)

// WorkerKVService 定义了与 Cloudflare Worker KV 相关的操作接口。
type WorkerKVService struct {
	client    *cloudflare.Client
	accountID string
}

// newWorkerKVService 创建一个新的 WorkerKVService。
func newWorkerKVService(client *cloudflare.Client, accountID string) *WorkerKVService {
	return &WorkerKVService{
		client:    client,
		accountID: accountID,
	}
}

// KVResponse 表示 Cloudflare KV API 的响应结构
type KVResponse struct {
	Value    string `json:"value"`
	Metadata string `json:"metadata,omitempty"`
}

// ReadKeyValuePair 从指定的 KV namespace 中读取键值对。
// ctx: 上下文。
// namespaceID: KV namespace ID。
// keyName: 要读取的键名。
//
// 返回:
// - string: 键对应的值。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (kvs *WorkerKVService) ReadKeyValuePair(ctx context.Context, namespaceID, keyName string) (string, error) {
	resp, err := kvs.client.KV.Namespaces.Values.Get(ctx, namespaceID, keyName, kv.NamespaceValueGetParams{
		AccountID: cloudflare.F(kvs.accountID),
	})
	if err != nil {
		return "", fmt.Errorf("failed to read key-value pair: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	responseStr := string(body)

	// 尝试解析为 JSON 响应（当有元数据时）
	var kvResp KVResponse
	if err := json.Unmarshal(body, &kvResp); err == nil && kvResp.Value != "" {
		// 成功解析为 JSON，返回 value 字段
		return kvResp.Value, nil
	}

	// 如果不是 JSON 格式，直接返回原始响应（纯文本值）
	return responseStr, nil
}

// WriteKeyValuePairWithOptionalMetadata 向指定的 KV namespace 中写入键值对，支持可选的元数据。
// 使用 BulkUpdate API 进行更高效的操作。
// ctx: 上下文。
// namespaceID: KV namespace ID。
// keyName: 要写入的键名。
// value: 要写入的值。
// metadata: 可选的元数据（可以为 nil）。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (kvs *WorkerKVService) WriteKeyValuePairWithOptionalMetadata(
	ctx context.Context,
	namespaceID, keyName, value string,
	metadata map[string]interface{},
) error {
	// 准备批量更新的数据
	bulkData := kv.NamespaceKeyBulkUpdateParamsBody{
		Key:   cloudflare.F(keyName),
		Value: cloudflare.F(value),
	}

	// 如果提供了元数据，直接使用
	if metadata != nil {
		bulkData.Metadata = cloudflare.F(metadata)
	}

	// 构建批量更新参数
	params := kv.NamespaceKeyBulkUpdateParams{
		AccountID: cloudflare.F(kvs.accountID),
		Body:      []kv.NamespaceKeyBulkUpdateParamsBody{bulkData},
	}

	// 调用批量更新 API
	response, err := kvs.client.KV.Namespaces.Keys.BulkUpdate(ctx, namespaceID, params)
	if err != nil {
		return fmt.Errorf("failed to write key-value pair: %w", err)
	}

	// 检查是否成功更新
	if response.SuccessfulKeyCount == 0 {
		return fmt.Errorf("failed to update key: no keys were successfully updated")
	}

	return nil
}

// DeleteKeyValuePair 从指定的 KV namespace 中删除键值对。
// ctx: 上下文。
// namespaceID: KV namespace ID。
// keyName: 要删除的键名。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (kvs *WorkerKVService) DeleteKeyValuePair(
	ctx context.Context,
	namespaceID, keyName string,
) error {
	params := kv.NamespaceValueDeleteParams{
		AccountID: cloudflare.F(kvs.accountID),
	}

	_, err := kvs.client.KV.Namespaces.Values.Delete(ctx, namespaceID, keyName, params)
	if err != nil {
		return fmt.Errorf("failed to delete key-value pair: %w", err)
	}

	return nil
}

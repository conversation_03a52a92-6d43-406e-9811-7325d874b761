package cloudflare

import (
	"context"
	"fmt"

	"github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/pages"
)

// ProjectService 定义了与 Cloudflare Pages 项目相关的操作接口。
type ProjectService struct {
	client    *cloudflare.Client
	accountID string
}

// newProjectService 创建一个新的 ProjectService。
func newProjectService(client *cloudflare.Client, accountID string) *ProjectService {
	return &ProjectService{
		client:    client,
		accountID: accountID,
	}
}

// GetProjectByName 通过项目名称获取项目信息。
// ctx: 上下文。
// projectName: Pages 项目名称。
//
// 返回:
// - *pages.Project: 项目信息。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (ps *ProjectService) GetProjectByName(ctx context.Context, projectName string) (*pages.Project, error) {
	project, err := ps.client.Pages.Projects.Get(ctx, projectName, pages.ProjectGetParams{
		AccountID: cloudflare.F(ps.accountID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get project by name: %w", err)
	}
	return project, nil
}

// CreateProject 创建一个新的 Cloudflare Pages 项目。
// ctx: 上下文。
// name: 项目名称。
// buildCommand: 构建命令。
// destinationDir: 构建输出目录。
// sourceType: 源代码类型（如 "github"）。
// owner: 代码仓库所有者。
// repoName: 代码仓库名称。
// productionBranch: 生产环境分支。
//
// 返回:
// - *pages.Project: 创建的项目信息。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (ps *ProjectService) CreateProject(
	ctx context.Context,
	name string,
	buildCommand string,
	destinationDir string,
	sourceType string,
	owner string,
	repoName string,
	productionBranch string,
) (*pages.Project, error) {
	params := pages.ProjectNewParams{
		AccountID: cloudflare.F(ps.accountID),
		Project: pages.ProjectParam{
			Name: cloudflare.F(name),
			BuildConfig: cloudflare.F(pages.ProjectBuildConfigParam{
				BuildCommand:   cloudflare.F(buildCommand),
				DestinationDir: cloudflare.F(destinationDir),
				RootDir:        cloudflare.F(""),
			}),
			Source: cloudflare.F(pages.ProjectSourceParam{
				Type: cloudflare.F(sourceType),
				Config: cloudflare.F(pages.ProjectSourceConfigParam{
					Owner:            cloudflare.F(owner),
					RepoName:         cloudflare.F(repoName),
					ProductionBranch: cloudflare.F(productionBranch),
					PathIncludes:     cloudflare.F([]string{"*"}),
					PathExcludes:     cloudflare.F([]string{}),
				}),
			}),
			DeploymentConfigs: cloudflare.F(pages.ProjectDeploymentConfigsParam{
				Preview: cloudflare.F(pages.ProjectDeploymentConfigsPreviewParam{
					EnvVars: cloudflare.F(map[string]pages.ProjectDeploymentConfigsPreviewEnvVarsUnionParam{}),
				}),
				Production: cloudflare.F(pages.ProjectDeploymentConfigsProductionParam{
					EnvVars: cloudflare.F(map[string]pages.ProjectDeploymentConfigsProductionEnvVarsUnionParam{}),
				}),
			}),
		},
	}

	project, err := ps.client.Pages.Projects.New(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create project: %w", err)
	}
	return project, nil
}

// DeleteProject 删除指定的 Cloudflare Pages 项目。
// ctx: 上下文。
// projectName: Pages 项目名称。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (ps *ProjectService) DeleteProject(ctx context.Context, projectName string) error {
	_, err := ps.client.Pages.Projects.Delete(ctx, projectName, pages.ProjectDeleteParams{
		AccountID: cloudflare.F(ps.accountID),
	})
	if err != nil {
		return fmt.Errorf("failed to delete project: %w", err)
	}
	return nil
}

// CreateDeployment 创建一个新的 Cloudflare Pages 部署。
// ctx: 上下文。
// projectName: Pages 项目名称。
//
// 返回:
// - *pages.Deployment: 部署信息。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (ps *ProjectService) CreateDeployment(ctx context.Context, projectName string) (*pages.Deployment, error) {
	deployment, err := ps.client.Pages.Projects.Deployments.New(ctx, projectName, pages.ProjectDeploymentNewParams{
		AccountID: cloudflare.F(ps.accountID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}
	return deployment, nil
}
package cloudflare

import (
	"context"

	"github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/alerting"
)

// AlertingService 定义了与 Cloudflare 告警相关的操作接口。
type AlertingService struct {
	client    *cloudflare.Client
	accountID string
}

// newAlertingService 创建一个新的 AlertingService。
func newAlertingService(client *cloudflare.Client, accountID string) *AlertingService {
	return &AlertingService{
		client:    client,
		accountID: accountID,
	}
}

// CreateNotificationPolicy 创建一个新的通知策略。
// ctx: 上下文。
// name: 策略名称。
// description: 策略描述。
// alertType: 告警类型。
// mechanisms: 通知机制列表。
// enabled: 是否启用策略。
// filters: 策略过滤器。
//
// 返回:
// - string: 成功创建的策略 ID。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (as *AlertingService) CreateNotificationPolicy(
	ctx context.Context,
	name string,
	description string,
	alertType alerting.PolicyNewParamsAlertType,
	mechanisms alerting.MechanismParam,
	enabled bool,
	filters alerting.PolicyFilterParam,
) (string, error) {

	policy, err := as.client.Alerting.Policies.New(ctx, alerting.PolicyNewParams{
		AccountID:   cloudflare.F(as.accountID),
		AlertType:   cloudflare.F(alertType),
		Enabled:     cloudflare.F(enabled),
		Mechanisms:  cloudflare.F(mechanisms),
		Name:        cloudflare.F(name),
		Description: cloudflare.F(description),
		Filters:     cloudflare.F(filters),
	})
	if err != nil {
		return "", err
	}
	return policy.ID, nil
}

// DeleteNotificationPolicy 删除指定的通知策略。
// ctx: 上下文。
// policyID: 要删除的策略 ID。
//
// 返回:
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (as *AlertingService) DeleteNotificationPolicy(ctx context.Context, policyID string) error {
	_, err := as.client.Alerting.Policies.Delete(ctx, policyID, alerting.PolicyDeleteParams{
		AccountID: cloudflare.F(as.accountID),
	})
	return err
}

package cloudflare

import (
	"github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/option"
)

// Service 是一个统一的 Cloudflare 服务客户端，提供了对不同 Cloudflare API 功能区的访问。
// 它封装了底层的 cloudflare.Client，并组合了特定功能的服务，如 Alerting、Project 和 WorkerKV。
type Service struct {
	client    *cloudflare.Client
	accountID string
	Alerting  *AlertingService // 用于操作告警策略
	Project   *ProjectService  // 用于操作 Pages 项目
	WorkerKV  *WorkerKVService // 用于操作 Worker KV
}

// NewService 创建并返回一个新的 Service 实例。
// apiKey: Cloudflare API Token。
// apiEmail: Cloudflare API Email（已弃用，保留参数是为了兼容性）。
// accountID: Cloudflare Account ID。
func NewService(apiKey, apiEmail, accountID string) (*Service, error) {
	client := cloudflare.NewClient(
		option.WithAPIKey(apiKey),
		option.WithAPIEmail(apiEmail),
	)

	s := &Service{
		client:    client,
		accountID: accountID,
	}

	// 初始化并关联特定功能的服务
	s.Alerting = newAlertingService(client, accountID)
	s.Project = newProjectService(client, accountID)
	s.WorkerKV = newWorkerKVService(client, accountID)

	return s, nil
}

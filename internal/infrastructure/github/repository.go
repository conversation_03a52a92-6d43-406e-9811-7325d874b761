package github

import (
	"context"

	"github.com/google/go-github/v72/github"
)

// RepositoryService 定义了与 GitHub 仓库相关的操作接口。
// 它通过一个共享的 github.Client 实例来执行操作。
type RepositoryService struct {
	client *github.Client // 直接使用 github.Client
}

// newRepositoryService 创建一个新的 RepositoryService。
// 这个函数是包内可见的，主要由统一的 Service 来调用。
func newRepositoryService(client *github.Client) *RepositoryService {
	return &RepositoryService{client: client}
}

// CreateUserRepository 在当前认证用户下创建一个新的 GitHub 仓库。
// ctx 是一个上下文对象，用于控制 API 请求的生命周期。
// repo 是一个指向 github.Repository 对象的指针，其中包含新仓库的详细信息。
//
// 返回:
// - *github.Repository: 成功创建的仓库对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
//
// 注意: 此方法会立即返回响应，而不会等待 GitHub 完成仓库创建并在其服务器上传播更改。
// 您可能需要设置一个带有指数退避的循环来验证仓库的创建。
func (rs *RepositoryService) CreateUserRepository(ctx context.Context, repo *github.Repository) (*github.Repository, *github.Response, error) {
	createdRepo, resp, err := rs.client.Repositories.Create(ctx, "", repo)
	if err != nil {
		return nil, resp, err
	}
	return createdRepo, resp, nil
}

// AddDeployKey 添加一个部署密钥到指定的仓库。
// ctx: 上下文。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// key: 要添加的部署密钥。
//
// 返回:
// - *github.Key: 成功添加的部署密钥对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) AddDeployKey(ctx context.Context, owner string, repoName string, key *github.Key) (*github.Key, *github.Response, error) {
	createdKey, resp, err := rs.client.Repositories.CreateKey(ctx, owner, repoName, key)
	if err != nil {
		return nil, resp, err
	}
	return createdKey, resp, nil
}

// DeleteDeployKey 从指定的仓库中删除一个部署密钥。
// ctx: 上下文。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// keyID: 要删除的部署密钥的 ID。
//
// 返回:
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) DeleteDeployKey(ctx context.Context, owner string, repoName string, keyID int64) (*github.Response, error) {
	resp, err := rs.client.Repositories.DeleteKey(ctx, owner, repoName, keyID)
	return resp, err
}

// UpdateRepository 更新指定仓库的信息。
// ctx: 上下文。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// repoUpdate: 包含要更新的仓库信息的对象。
//
// 返回:
// - *github.Repository: 更新后的仓库对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) UpdateRepository(ctx context.Context, owner string, repoName string, repoUpdate *github.Repository) (*github.Repository, *github.Response, error) {
	updatedRepo, resp, err := rs.client.Repositories.Edit(ctx, owner, repoName, repoUpdate)
	if err != nil {
		return nil, resp, err
	}
	return updatedRepo, resp, nil
}

// CreateRepositoryFromTemplate 根据模板仓库创建一个新的 GitHub 仓库。
// ctx: 上下文。
// templateOwner: 模板仓库的所有者。
// templateRepo: 模板仓库的名称。
// newRepoRequest: 包含新仓库详细信息的请求对象。
//
// 返回:
// - *github.Repository: 成功创建的仓库对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) CreateRepositoryFromTemplate(ctx context.Context, templateOwner string, templateRepo string, newRepoRequest *github.TemplateRepoRequest) (*github.Repository, *github.Response, error) {
	createdRepo, resp, err := rs.client.Repositories.CreateFromTemplate(ctx, templateOwner, templateRepo, newRepoRequest)
	if err != nil {
		return nil, resp, err
	}
	return createdRepo, resp, nil
}

// ListRepositoryCommits 列出指定仓库的提交。
// ctx: 上下文。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// opts: 列出提交的选项 (例如，SHA 或路径，分支，作者，日期范围等)。可以为 nil。
//
// 返回:
// - []*github.RepositoryCommit: 提交对象的切片。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) ListRepositoryCommits(ctx context.Context, owner string, repoName string, opts *github.CommitsListOptions) ([]*github.RepositoryCommit, *github.Response, error) {
	commits, resp, err := rs.client.Repositories.ListCommits(ctx, owner, repoName, opts)
	if err != nil {
		return nil, resp, err
	}
	return commits, resp, nil
}

// GetRepository 获取指定所有者和仓库名称的仓库信息。
// ctx 是一个上下文对象，用于控制 API 请求的生命周期。
// owner 是仓库所有者的用户名或组织名。
// repoName 是仓库的名称。
//
// 返回:
// - *github.Repository: 获取到的仓库对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) GetRepository(ctx context.Context, owner string, repoName string) (*github.Repository, *github.Response, error) {
	repo, resp, err := rs.client.Repositories.Get(ctx, owner, repoName)
	if err != nil {
		return nil, resp, err
	}
	return repo, resp, nil
}

// DeleteRepository 删除指定的 GitHub 仓库。
// ctx 是一个上下文对象，用于控制 API 请求的生命周期。
// owner 是仓库所有者的用户名或组织名。
// repoName 是要删除的仓库的名称。
//
// 返回:
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) DeleteRepository(ctx context.Context, owner string, repoName string) (*github.Response, error) {
	resp, err := rs.client.Repositories.Delete(ctx, owner, repoName)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

// GetContents 获取仓库中指定路径的文件或目录内容。
// ctx: 上下文。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// path: 要获取内容的文件或目录在仓库中的路径。
// opts: 获取内容的选项，例如指定 ref (分支、标签或 commit SHA)。可以为 nil。
//
// 返回:
// - fileContent: 如果路径指向一个文件，则此为该文件的内容。
// - directoryContent: 如果路径指向一个目录，则此为该目录中各项内容的列表。
// - resp: GitHub API 的响应详情。
// - err: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *RepositoryService) GetContents(ctx context.Context, owner string, repoName string, path string, opts *github.RepositoryContentGetOptions) (fileContent *github.RepositoryContent, directoryContent []*github.RepositoryContent, resp *github.Response, err error) {
	return rs.client.Repositories.GetContents(ctx, owner, repoName, path, opts)
}

package github

import (
	"context"

	"github.com/google/go-github/v72/github"
)

// CommitService 定义了与 GitHub Git Commits 相关的操作接口。
// 它通过一个共享的 github.Client 实例来执行操作。
type CommitService struct {
	client *github.Client
}

// newCommitService 创建一个新的 CommitService。
// 这个函数是包内可见的，主要由统一的 Service 来调用。
func newCommitService(client *github.Client) *CommitService {
	return &CommitService{client: client}
}

// GetCommit fetches a single commit from a repository.
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// sha: 要获取的 commit 的 SHA。
// opts: 获取 commit 的选项 (例如，获取特定文件或路径的变更)。可以为 nil。
//
// 返回:
// - *github.RepositoryCommit: 获取到的提交对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (cs *CommitService) GetCommit(ctx context.Context, owner, repoName, sha string, opts *github.ListOptions) (*github.RepositoryCommit, *github.Response, error) {
	commit, resp, err := cs.client.Repositories.GetCommit(ctx, owner, repoName, sha, opts)
	if err != nil {
		return nil, resp, err
	}
	return commit, resp, nil
}

// CreateCommit 在仓库中创建一个新的 Git Commit 对象。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// commit: 要创建的 commit 的详细信息。需要指定 Message, Tree SHA, 和 Parent SHAs。
// opts: 创建 commit 的选项，例如签名信息。可以为 nil。
//
// 返回:
// - *github.Commit: 创建成功的 Git commit 对象 (注意: 这是 *github.Commit, 不是 *github.RepositoryCommit)。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (cs *CommitService) CreateCommit(ctx context.Context, owner string, repoName string, commit *github.Commit, opts *github.CreateCommitOptions) (*github.Commit, *github.Response, error) {
	createdCommit, resp, err := cs.client.Git.CreateCommit(ctx, owner, repoName, commit, opts)
	if err != nil {
		return nil, resp, err
	}
	return createdCommit, resp, nil
}

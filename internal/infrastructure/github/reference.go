package github

import (
	"context"

	"github.com/google/go-github/v72/github"
)

// ReferenceService 定义了与 GitHub Git References 相关的操作接口。
// 它通过一个共享的 github.Client 实例来执行操作。
type ReferenceService struct {
	client *github.Client
}

// newReferenceService 创建一个新的 ReferenceService。
// 这个函数是包内可见的，主要由统一的 Service 来调用。
func newReferenceService(client *github.Client) *ReferenceService {
	return &ReferenceService{client: client}
}

// UpdateReference 更新仓库中的一个 ref (例如，分支、标签)。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// refPath: 要更新的 ref 的完整路径 (例如，"refs/heads/main", "refs/tags/v1.0")。
// sha: 此 ref 应指向的新的 SHA-1 值。
// force: 布尔值，指示是否强制更新。如果为 true，即使不是快进合并也会更新 ref。
//
// 返回:
// - *github.Reference: 更新后的 ref 对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *ReferenceService) UpdateReference(ctx context.Context, owner string, repoName string, refPath string, sha string, force bool) (*github.Reference, *github.Response, error) {
	refToUpdate := &github.Reference{
		Ref:    github.Ptr(refPath),
		Object: &github.GitObject{SHA: github.Ptr(sha)},
	}

	updatedRef, resp, err := rs.client.Git.UpdateRef(ctx, owner, repoName, refToUpdate, force)
	if err != nil {
		return nil, resp, err
	}
	return updatedRef, resp, nil
}

// GetReference 获取仓库中的一个 ref (例如，分支、标签)。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// refPath: 要获取的 ref 的完整路径 (例如，"refs/heads/main", "refs/tags/v1.0")。
//
// 返回:
// - *github.Reference: 获取到的 ref 对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *ReferenceService) GetReference(ctx context.Context, owner string, repoName string, refPath string) (*github.Reference, *github.Response, error) {
	ref, resp, err := rs.client.Git.GetRef(ctx, owner, repoName, refPath)
	if err != nil {
		return nil, resp, err
	}
	return ref, resp, nil
}

// CreateReference 创建仓库中的一个新 ref (例如，分支、标签)。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// ref: 要创建的 ref 对象，需要包含 Ref (路径) 和 Object.SHA (指向的 commit SHA)。
//
// 返回:
// - *github.Reference: 创建的 ref 对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *ReferenceService) CreateReference(ctx context.Context, owner string, repoName string, ref *github.Reference) (*github.Reference, *github.Response, error) {
	createdRef, resp, err := rs.client.Git.CreateRef(ctx, owner, repoName, ref)
	if err != nil {
		return nil, resp, err
	}
	return createdRef, resp, nil
}

// DeleteReference 删除仓库中的一个 ref (例如，分支、标签)。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// refPath: 要删除的 ref 的完整路径 (例如，"refs/heads/my-branch")。
//
// 返回:
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (rs *ReferenceService) DeleteReference(ctx context.Context, owner string, repoName string, refPath string) (*github.Response, error) {
	resp, err := rs.client.Git.DeleteRef(ctx, owner, repoName, refPath)
	return resp, err
}

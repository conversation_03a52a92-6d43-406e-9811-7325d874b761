package github

import (
	"context"

	"github.com/google/go-github/v72/github"
)

// BranchService 定义了与 GitHub 仓库分支相关的操作接口。
// 它通过一个共享的 github.Client 实例来执行操作。
type BranchService struct {
	client *github.Client // 直接使用 github.Client
}

// newBranchService 创建一个新的 BranchService。
// 这个函数是包内可见的，主要由统一的 Service 来调用。
func newBranchService(client *github.Client) *BranchService {
	return &BranchService{client: client}
}

// GetBranch 获取指定仓库中特定分支的信息。
// ctx: 上下文对象，用于控制 API 请求的生命周期。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// branchName: 要获取信息的分支名称。
// maxRedirects: 客户端应遵循的最大重定向次数。通常可以传递 0 或 1。
//
// 返回:
// - *github.Branch: 获取到的分支对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (bs *BranchService) GetBranch(ctx context.Context, owner string, repoName string, branchName string, maxRedirects int) (*github.Branch, *github.Response, error) {
	branch, resp, err := bs.client.Repositories.GetBranch(ctx, owner, repoName, branchName, maxRedirects)
	if err != nil {
		return nil, resp, err
	}
	return branch, resp, nil
}

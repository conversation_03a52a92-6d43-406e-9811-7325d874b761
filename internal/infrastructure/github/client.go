package github

import (
	"net/http"

	"github.com/google/go-github/v72/github"
	"github.com/hasura/go-graphql-client"
)

// Service 是一个统一的 GitHub 服务客户端，提供了对不同 GitHub API 功能区的访问。
// 它封装了底层的 github.Client，并组合了特定功能的服务，如 Repositories 和 Branches。
type Service struct {
	client        *github.Client
	graphqlClient *graphql.Client
	Repositories  *RepositoryService // 用于操作仓库
	Branches      *BranchService     // 用于操作分支
	Trees         *TreeService       // 用于操作 Git Trees
	Commits       *CommitService     // 用于操作 Git Commits
	References    *ReferenceService  // 用于操作 Git References
	Files         *FileService       // 用于操作文件内容查询
	// 未来可以添加 Commits *CommitService, Trees *TreeService 等
}

// NewService 创建并返回一个新的 Service 实例。
// token: GitHub Personal Access Token。
func NewService(token string) *Service {
	gc := github.NewClient(nil).WithAuthToken(token)

	// 创建带有认证的 HTTP 客户端
	httpClient := &http.Client{
		Transport: &authTransport{
			token: token,
			base:  http.DefaultTransport,
		},
	}

	// 创建 GraphQL 客户端
	graphqlClient := graphql.NewClient("https://api.github.com/graphql", httpClient)

	s := &Service{
		client:        gc,
		graphqlClient: graphqlClient,
	}

	// 初始化并关联特定功能的服务
	// 这些服务将共享同一个底层的 github.Client
	s.Repositories = newRepositoryService(s.client)
	s.Branches = newBranchService(s.client)
	s.Trees = newTreeService(s.client)
	s.Commits = newCommitService(s.client)
	s.References = newReferenceService(s.client)
	s.Files = newFileService(s.graphqlClient)

	return s
}

// authTransport 是一个自定义的 HTTP 传输器，用于为 GraphQL 请求添加认证头
type authTransport struct {
	token string
	base  http.RoundTripper
}

// RoundTrip 实现 http.RoundTripper 接口，为请求添加认证头
func (t *authTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Set("Authorization", "Bearer "+t.token)
	return t.base.RoundTrip(req)
}

// GetGraphQLClient 返回 GraphQL 客户端实例
// 用于执行 GraphQL 查询，如批量获取文件内容等
func (s *Service) GetGraphQLClient() *graphql.Client {
	return s.graphqlClient
}

// clientForTests 是一个仅用于测试的辅助方法，用于访问底层的 client。
// 在实际应用代码中不应该使用它。
// func (s *Service) clientForTests() *github.Client {
// 	return s.client
// }

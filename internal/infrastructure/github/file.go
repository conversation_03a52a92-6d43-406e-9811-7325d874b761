package github

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"github.com/hasura/go-graphql-client"
)

// File 表示输出 JSON 中每个文件的结构
type File struct {
	Name     string `json:"name"`
	Contents string `json:"contents,omitempty"`
	Binary   bool   `json:"binary"`
}

// GraphQLBlob 表示 GraphQL 响应中的 blob 对象
type GraphQLBlob struct {
	Text     *string `graphql:"text"`
	IsBinary bool    `graphql:"isBinary"`
}

// FileInfo 表示输入文件的信息
type FileInfo struct {
	Path string
	Sha  string
}
// FileService 提供了文件内容查询相关的功能
type FileService struct {
	graphqlClient *graphql.Client
}

// newFileService 创建一个新的 FileService 实例
func newFileService(graphqlClient *graphql.Client) *FileService {
	return &FileService{
		graphqlClient: graphqlClient,
	}
}

// FetchFileContents 使用 GraphQL API 批量查询文件内容
// 参数:
//   - ctx: 上下文
//   - owner: 仓库拥有者
//   - repo: 仓库名称
//   - files: 文件信息列表（路径和 SHA）
//   - batchSize: 每批查询的文件数
//
// 返回:
//   - []File: 文件内容列表
//   - error: 查询过程中的错误
func (fs *FileService) FetchFileContents(ctx context.Context, owner, repo string, files []FileInfo, batchSize int) ([]File, error) {
	var resultFiles []File

	// 分页处理文件列表
	for i := 0; i < len(files); i += batchSize {
		end := i + batchSize
		if end > len(files) {
			end = len(files)
		}
		batch := files[i:end]

		// 构建动态 GraphQL 查询
		query := BuildGraphQLQuery(batch)
		variables := map[string]interface{}{
			"owner": owner,
			"repo":  repo,
		}
		for j, info := range batch {
			variables[fmt.Sprintf("sha%d", j)] = info.Sha
		}

		// 动态生成结果结构体
		resultStruct := generateResultStruct(batch)

		// 执行 GraphQL 查询
		if err := fs.graphqlClient.Exec(ctx, query, resultStruct, variables); err != nil {
			return nil, fmt.Errorf("GraphQL 查询失败 (批次 %d-%d): %w", i, end, err)
		}

		// 处理查询结果
		for j, info := range batch {
			blob := getBlobFromResult(resultStruct, j)
			if blob == nil {
				continue
			}

			file := File{
				Name:   info.Path,
				Binary: blob.IsBinary,
			}
			if !blob.IsBinary && blob.Text != nil {
				file.Contents = *blob.Text
			}
			resultFiles = append(resultFiles, file)
		}
	}

	return resultFiles, nil
}

// generateResultStruct 动态生成结果结构体
func generateResultStruct(batch []FileInfo) interface{} {
	// 创建 repository 结构体
	repositoryStruct := reflect.StructOf([]reflect.StructField{
		{
			Name: "Repository",
			Type: reflect.StructOf(generateFileFields(batch)),
			Tag:  reflect.StructTag(`graphql:"repository"`),
		},
	})

	return reflect.New(repositoryStruct).Interface()
}

// generateFileFields 生成文件字段
func generateFileFields(batch []FileInfo) []reflect.StructField {
	fields := make([]reflect.StructField, len(batch))

	for i := range batch {
		fields[i] = reflect.StructField{
			Name: fmt.Sprintf("File%d", i),
			Type: reflect.TypeOf((*GraphQLBlob)(nil)).Elem(),
			Tag:  reflect.StructTag(fmt.Sprintf(`graphql:"file%d: object(oid: $sha%d) { ... on Blob { text isBinary } }"`, i, i)),
		}
	}

	return fields
}

// getBlobFromResult 从结果结构体中获取 blob
func getBlobFromResult(result interface{}, index int) *GraphQLBlob {
	val := reflect.ValueOf(result)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 获取 Repository 字段
	repository := val.FieldByName("Repository")
	if !repository.IsValid() {
		return nil
	}

	// 获取对应的文件字段
	fileField := repository.FieldByName(fmt.Sprintf("File%d", index))
	if !fileField.IsValid() {
		return nil
	}

	// 转换为 GraphQLBlob
	if fileField.CanAddr() {
		return fileField.Addr().Interface().(*GraphQLBlob)
	}

	// 如果无法获取地址，创建一个副本
	blob := &GraphQLBlob{}
	if textField := fileField.FieldByName("Text"); textField.IsValid() {
		if textField.CanInterface() {
			if text := textField.Interface(); text != nil {
				if textStr, ok := text.(*string); ok && textStr != nil {
					blob.Text = textStr
				}
			}
		}
	}
	if binaryField := fileField.FieldByName("IsBinary"); binaryField.IsValid() {
		if binaryField.CanInterface() {
			if binary, ok := binaryField.Interface().(bool); ok {
				blob.IsBinary = binary
			}
		}
	}

	return blob
}

// BuildGraphQLQuery 构建批量文件的动态 GraphQL 查询
func BuildGraphQLQuery(batch []FileInfo) string {
	var fields []string
	for i := range batch {
		fields = append(fields, fmt.Sprintf("file%d: object(oid: $sha%d) { ... on Blob { text isBinary } }", i, i))
	}
	return fmt.Sprintf(`
		query GetFileContents($owner: String!, $repo: String!, %s) {
			repository(owner: $owner, name: $repo) {
				%s
			}
		}
	`, BuildVariables(batch), strings.Join(fields, "\n"))
}

// BuildVariables 构建 GraphQL 查询的变量声明
func BuildVariables(batch []FileInfo) string {
	var vars []string
	for i := range batch {
		vars = append(vars, fmt.Sprintf("$sha%d: GitObjectID!", i))
	}
	return strings.Join(vars, ", ")
}

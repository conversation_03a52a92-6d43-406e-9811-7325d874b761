package github

import (
	"context"

	"github.com/google/go-github/v72/github"
)

// TreeService 定义了与 GitHub Git Trees 相关的操作接口。
// 它通过一个共享的 github.Client 实例来执行操作。
type TreeService struct {
	client *github.Client
}

// newTreeService 创建一个新的 TreeService。
// 这个函数是包内可见的，主要由统一的 Service 来调用。
func newTreeService(client *github.Client) *TreeService {
	return &TreeService{client: client}
}

// CreateTree 在仓库中创建一个新的 Git Tree 对象。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// baseTreeSHA: 可选的。基础树的 SHA-1。如果提供，新树将基于此树构建。
//
//	如果为空字符串 ""，则新树不基于任何现有树。
//
// entries: 一系列描述树中每个条目的对象。条目可以是 blob、其他树或 commit。
//
// 返回:
// - *github.Tree: 创建成功的树对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
//
// 注意：TreeEntry 中的 Content 字段如果被设置，并且 Type 是 "blob"，
// GitHub API 会首先尝试根据 Content 创建一个新的 blob，然后将其 SHA 用于树条目。
// 如果你已经有一个 blob 的 SHA，应该直接在 TreeEntry 的 SHA 字段中指定它。
func (ts *TreeService) CreateTree(ctx context.Context, owner string, repoName string, baseTreeSHA string, entries []*github.TreeEntry) (*github.Tree, *github.Response, error) {
	tree, resp, err := ts.client.Git.CreateTree(ctx, owner, repoName, baseTreeSHA, entries)
	if err != nil {
		return nil, resp, err
	}
	return tree, resp, nil
}

// GetTree 获取仓库中指定 SHA 的 Git Tree 对象。
// ctx: 上下文对象。
// owner: 仓库所有者的用户名或组织名。
// repoName: 仓库的名称。
// treeSHA: 要获取的树的 SHA-1 值或引用（分支或标签）名称。
// recursive: 是否递归获取树的内容。如果为 true，将返回所有子目录的内容。
//
// 返回:
// - *github.Tree: 获取到的树对象。
// - *github.Response: GitHub API 的响应详情。
// - error: 如果在 API 调用过程中发生错误，则返回错误信息。
func (ts *TreeService) GetTree(ctx context.Context, owner string, repoName string, treeSHA string, recursive bool) (*github.Tree, *github.Response, error) {
	tree, resp, err := ts.client.Git.GetTree(ctx, owner, repoName, treeSHA, recursive)
	if err != nil {
		return nil, resp, err
	}
	return tree, resp, nil
}

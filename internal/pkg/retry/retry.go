package retry

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"strings"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// RetryConfig 定义了重试的配置参数
type RetryConfig struct {
	MaxAttempts     int           // 最大尝试次数
	InitialDelay    time.Duration // 初始延迟
	MaxDelay        time.Duration // 最大延迟
	BackoffFactor   float64       // 指数退避因子
	JitterFactor    float64       // 抖动因子 (0-1)
	RetryableErrors []string      // 可重试的错误字符串列表
}

// NetworkRetryConfig 返回一个针对网络相关错误的默认重试配置
func NetworkRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts:   5,
		InitialDelay:  200 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
		JitterFactor:  0.1,
		RetryableErrors: []string{
			"EOF", // End of File, 常见于连接意外关闭
			"connection reset by peer",
			"connection refused",
			"timeout",
			"network is unreachable",
			"no route to host",
			"temporary failure in name resolution",
			"server overloaded",
			"rate limit exceeded",
			"502 Bad Gateway",
			"503 Service Unavailable",
			"504 Gateway Timeout",
			"unexpected end of JSON input", // 有时EOF会表现为此形式
		},
	}
}

// RetryableFuncWithResult 是一个可重试并返回结果的函数类型
type RetryableFuncWithResult[T any] func() (T, error)

// isRetryableError 检查一个错误是否是可重试的
func (c *RetryConfig) isRetryableError(err error) bool {
	if err == nil {
		return false
	}
	errStr := strings.ToLower(err.Error())
	for _, retryableErr := range c.RetryableErrors {
		if strings.Contains(errStr, strings.ToLower(retryableErr)) {
			return true
		}
	}
	return false
}

// calculateDelay 计算下一次重试的延迟时间
func (c *RetryConfig) calculateDelay(attempt int) time.Duration {
	delay := float64(c.InitialDelay) * math.Pow(c.BackoffFactor, float64(attempt-1))
	if c.JitterFactor > 0 {
		jitter := delay * c.JitterFactor * (rand.Float64()*2 - 1)
		delay += jitter
	}
	if delay > float64(c.MaxDelay) {
		delay = float64(c.MaxDelay)
	}
	if delay < 0 {
		delay = 0
	}
	return time.Duration(delay)
}

// DoWithResult 执行带有返回值的重试逻辑
func DoWithResult[T any](ctx context.Context, config *RetryConfig, fn RetryableFuncWithResult[T]) (T, error) {
	var lastErr error
	var zero T

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		select {
		case <-ctx.Done():
			return zero, fmt.Errorf("retry cancelled before execution: %w", ctx.Err())
		default:
		}

		result, err := fn()
		if err == nil {
			if attempt > 1 {
				logger.Info("Request succeeded after retry", "attempt", attempt)
			}
			return result, nil
		}
		lastErr = err

		if !config.isRetryableError(err) {
			logger.Warn("Non-retryable error encountered, stopping retries.", "error", err, "attempt", attempt)
			return zero, err
		}

		if attempt == config.MaxAttempts {
			logger.Error("Max retry attempts reached, failing.", "error", err, "attempts", config.MaxAttempts)
			break
		}

		delay := config.calculateDelay(attempt)
		logger.Warn("Retryable error encountered, retrying after delay.",
			"error", err, "attempt", attempt, "delay", delay)

		select {
		case <-time.After(delay):
			// continue to next attempt
		case <-ctx.Done():
			return zero, fmt.Errorf("retry cancelled during delay: %w (last error: %v)", ctx.Err(), lastErr)
		}
	}

	return zero, fmt.Errorf("failed after %d attempts, last error: %w", config.MaxAttempts, lastErr)
}

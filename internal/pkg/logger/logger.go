package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var log *zap.SugaredLogger

// formatArgsWithSpaces 将参数列表转换为用空格分隔的单个字符串。
func formatArgsWithSpaces(args ...any) string {
	if len(args) == 0 {
		return ""
	}
	// 第一个参数通常是主要消息，后续参数是附加信息。
	// 为了保持主要消息的完整性，我们可以考虑只对后续参数进行 fmt.Sprint 并拼接。
	// 但根据用户要求 "会打印 Failed to list site snippets SiteID siteID Error err"
	// 似乎是希望所有参数都被视为空格分隔的字符串部分。
	var sb strings.Builder
	for i, arg := range args {
		if i > 0 {
			sb.WriteString(" ")
		}
		sb.WriteString(fmt.Sprint(arg))
	}
	return sb.String()
}

// getLogFilePath 生成日志文件路径
func getLogFilePath() string {
	// 确保 logs 目录存在
	logsDir := "logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create logs directory: %v\n", err)
		return ""
	}

	// 使用当前时间生成日志文件名
	timestamp := time.Now().Format("2006-01-02-15-04-05")
	return filepath.Join(logsDir, fmt.Sprintf("%s.log", timestamp))
}

// InitLogger 初始化全局日志记录器。
// logLevel: 日志级别字符串 (例如 "debug", "info", "warn", "error").
// env: 运行环境字符串 (例如 "dev", "prod", "test").
func InitLogger(logLevel string, env string) {
	var level zapcore.Level
	err := level.UnmarshalText([]byte(strings.ToLower(logLevel)))
	if err != nil {
		level = zapcore.InfoLevel // 默认级别
		fmt.Fprintf(os.Stderr, "Invalid log level '%s'. Defaulting to 'info'. Error: %v\n", logLevel, err)
	}

	// 创建控制台编码器配置（带颜色）
	consoleEncoderConfig := zap.NewDevelopmentEncoderConfig()
	if env == "dev" || env == "development" {
		consoleEncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder // 带颜色的大写级别
	} else {
		consoleEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder // 不带颜色的大写级别
	}
	consoleEncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)

	// 创建文件编码器配置（不带颜色）
	fileEncoderConfig := zap.NewProductionEncoderConfig()
	fileEncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder // 不带颜色的大写级别
	fileEncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
	fileEncoder := zapcore.NewConsoleEncoder(fileEncoderConfig)

	// 创建控制台输出
	consoleCore := zapcore.NewCore(
		consoleEncoder,
		zapcore.Lock(os.Stdout),
		level,
	)

	// 创建文件输出
	logFilePath := getLogFilePath()
	if logFilePath == "" {
		logFilePath = "logs/app.log" // 如果创建目录失败，使用默认路径
	}

	// 配置 lumberjack 进行日志轮转
	fileWriter := &lumberjack.Logger{
		Filename:   logFilePath,
		MaxSize:    100,  // 单个文件最大尺寸，单位是 MB
		MaxBackups: 10,   // 保留的旧文件最大数量
		MaxAge:     30,   // 保留天数
		Compress:   true, // 是否压缩
	}

	fileCore := zapcore.NewCore(
		fileEncoder,
		zapcore.AddSync(fileWriter),
		level,
	)

	// 使用 NewTee 将日志同时输出到控制台和文件
	core := zapcore.NewTee(consoleCore, fileCore)

	baseLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	log = baseLogger.Sugar()
	Info("Logger initialized successfully.", "level", level.String(), "environment", env, "logFile", logFilePath)
}

// Debug 记录 Debug 级别的日志
func Debug(args ...any) {
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formatArgsWithSpaces(args...))
		return
	}
	log.Debug(formatArgsWithSpaces(args...))
}

// Info 记录 Info 级别的日志
func Info(args ...any) {
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formatArgsWithSpaces(args...))
		return
	}
	log.Info(formatArgsWithSpaces(args...))
}

// Warn 记录 Warn 级别的日志
func Warn(args ...any) {
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formatArgsWithSpaces(args...))
		return
	}
	log.Warn(formatArgsWithSpaces(args...))
}

// Error 记录 Error 级别的日志
func Error(args ...any) {
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formatArgsWithSpaces(args...))
		return
	}
	log.Error(formatArgsWithSpaces(args...))
}

// DPanic 记录 DPanic 级别的日志 (开发模式下 Panic, 生产模式下 Error)
func DPanic(args ...any) {
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formatArgsWithSpaces(args...))
		return
	}
	log.DPanic(formatArgsWithSpaces(args...))
}

// Panic 记录 Panic 级别的日志 (记录日志后 Panic)
func Panic(args ...any) {
	formattedMessage := formatArgsWithSpaces(args...)
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formattedMessage)
		panic(formattedMessage)
	}
	log.Panic(formattedMessage)
}

// Fatal 记录 Fatal 级别的日志 (记录日志后 os.Exit(1))
func Fatal(args ...any) {
	formattedMessage := formatArgsWithSpaces(args...)
	if log == nil {
		fmt.Fprintf(os.Stderr, "Logger not initialized. Message: %s\n", formattedMessage)
		fmt.Fprintln(os.Stderr, formattedMessage)
		os.Exit(1)
	}
	log.Fatal(formattedMessage)
}

// SugaredLogger 返回全局的 SugaredLogger 实例，以备不时之需，但不推荐直接使用。
func SugaredLogger() *zap.SugaredLogger {
	return log
}

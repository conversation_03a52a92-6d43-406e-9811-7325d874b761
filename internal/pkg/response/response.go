package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// APIResponse 定义了标准的 API 响应结构体。
// Code: 业务状态码, 0 表示成功, 其他表示失败。
// Msg: 响应消息。
// Data: 响应数据 (必须字段，如果 Go 值为 nil，JSON 中会是 null)。
type APIResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"` // 使用 any (Go 1.18+), 移除 omitempty
}

// 定义一些常用的业务状态码 (可以根据需要扩展)
const (
	CodeSuccess       = 0
	CodeError         = -1  // 通用错误
	CodeInvalidParams = 1   // 参数无效
	CodeNotFound      = 2   // 资源未找到
	CodeUnauthorized  = 3   // 未授权
	CodeInternalError = 500 // 内部服务器错误 (与 HTTP 500 对应)
	// ... 其他自定义错误码
)

// 默认消息
const (
	DefaultSuccessMsg = "success"
	DefaultErrorMsg   = "error"
)

// Success 返回一个表示成功的 API 响应，使用默认消息。
func Success(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code: CodeSuccess,
		Msg:  DefaultSuccessMsg,
		Data: nil,
	})
}

// SuccessWithMsg 返回一个带自定义消息的成功 API 响应。
func SuccessWithMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, APIResponse{
		Code: CodeSuccess,
		Msg:  msg,
		Data: nil,
	})
}

// SuccessWithData 返回一个带数据的成功 API 响应，使用默认消息。
func SuccessWithData(c *gin.Context, data any) {
	c.JSON(http.StatusOK, APIResponse{
		Code: CodeSuccess,
		Msg:  DefaultSuccessMsg,
		Data: data,
	})
}

// SuccessWithMsgAndData 返回一个带自定义消息和数据的成功 API 响应。
func SuccessWithMsgAndData(c *gin.Context, msg string, data any) {
	c.JSON(http.StatusOK, APIResponse{
		Code: CodeSuccess,
		Msg:  msg,
		Data: data,
	})
}

// Failure 返回一个表示失败的 API 响应，使用默认错误码和消息。
func Failure(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: CodeError,
		Msg:  DefaultErrorMsg,
		Data: nil,
	})
}

// FailureWithMsg 返回一个带自定义消息的失败 API 响应，使用默认错误码。
func FailureWithMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: CodeError,
		Msg:  msg,
		Data: nil,
	})
}

// FailureWithCode 返回一个带自定义错误码的失败 API 响应，使用默认消息。
func FailureWithCode(c *gin.Context, code int) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: code,
		Msg:  DefaultErrorMsg,
		Data: nil,
	})
}

// FailureWithCodeAndMsg 返回一个带自定义错误码和消息的失败 API 响应。
func FailureWithCodeAndMsg(c *gin.Context, code int, msg string) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: code,
		Msg:  msg,
		Data: nil,
	})
}

// FailureWithData 返回一个带数据的失败 API 响应，使用默认错误码和消息。
func FailureWithData(c *gin.Context, data any) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: CodeError,
		Msg:  DefaultErrorMsg,
		Data: data,
	})
}

// FailureWithCodeAndData 返回一个带自定义错误码和数据的失败 API 响应，使用默认消息。
func FailureWithCodeAndData(c *gin.Context, code int, data any) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: code,
		Msg:  DefaultErrorMsg,
		Data: data,
	})
}

// FailureWithMsgAndData 返回一个带自定义消息和数据的失败 API 响应，使用默认错误码。
func FailureWithMsgAndData(c *gin.Context, msg string, data any) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: CodeError,
		Msg:  msg,
		Data: data,
	})
}

// FailureWithCodeAndMsgAndData 返回一个带自定义错误码、消息和数据的失败 API 响应。
func FailureWithCodeAndMsgAndData(c *gin.Context, code int, msg string, data any) {
	c.JSON(http.StatusInternalServerError, APIResponse{
		Code: code,
		Msg:  msg,
		Data: data,
	})
}

// BadRequest 返回一个 400 错误响应，使用默认错误码和消息。
func BadRequest(c *gin.Context) {
	c.JSON(http.StatusBadRequest, APIResponse{
		Code: CodeInvalidParams,
		Msg:  DefaultErrorMsg,
		Data: nil,
	})
}
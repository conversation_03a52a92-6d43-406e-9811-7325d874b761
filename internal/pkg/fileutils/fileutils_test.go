package fileutils

import (
	"testing"
)

func TestReplaceContent(t *testing.T) {
	tests := []struct {
		name    string
		content string
		oldStr  string
		newStr  string
		want    string
	}{
		{
			name:    "基础替换测试",
			content: "hello world hello",
			oldStr:  "hello",
			newStr:  "hi",
			want:    "hi world hi",
		},
		{
			name:    "无匹配内容",
			content: "hello world",
			oldStr:  "xyz",
			newStr:  "abc",
			want:    "hello world",
		},
		{
			name:    "空字符串替换",
			content: "hello world",
			oldStr:  "hello",
			newStr:  "",
			want:    " world",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ReplaceContent(tt.content, tt.oldStr, tt.newStr)
			if got != tt.want {
				t.Errorf("ReplaceContent() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReplaceContentBatch(t *testing.T) {
	tests := []struct {
		name    string
		content string
		rules   []ReplaceRule
		want    string
	}{
		{
			name:    "批量替换测试",
			content: "hello world foo bar",
			rules: []ReplaceRule{
				{Old: "hello", New: "hi"},
				{Old: "world", New: "universe"},
				{Old: "foo", New: "baz"},
			},
			want: "hi universe baz bar",
		},
		{
			name:    "空规则数组",
			content: "hello world",
			rules:   []ReplaceRule{},
			want:    "hello world",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ReplaceContentBatch(tt.content, tt.rules)
			if got != tt.want {
				t.Errorf("ReplaceContentBatch() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReplaceContentWithRegex(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		pattern     string
		replacement string
		want        string
		wantErr     bool
	}{
		{
			name:        "数字替换",
			content:     "I have 123 apples and 456 oranges",
			pattern:     `\d+`,
			replacement: "XXX",
			want:        "I have XXX apples and XXX oranges",
			wantErr:     false,
		},
		{
			name:        "捕获组替换",
			content:     "Hello John, Hello Mary",
			pattern:     `Hello (\w+)`,
			replacement: "Hi $1",
			want:        "Hi John, Hi Mary",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ReplaceContentWithRegex(tt.content, tt.pattern, tt.replacement)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReplaceContentWithRegex() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ReplaceContentWithRegex() = %v, want %v", got, tt.want)
			}
		})
	}
}

package fileutils

import (
	"regexp"
	"strings"
)

// ReplaceRule 替换规则结构体
type ReplaceRule struct {
	Old string // 待替换的文本
	New string // 替换后的文本
}

// ReplaceContent 基础文本替换函数 - 替换所有匹配的字符串
// content: 原始文本内容
// oldStr: 待替换的字符串
// newStr: 替换后的字符串
// 返回: 替换后的新文本（不修改原文本）
func ReplaceContent(content, oldStr, newStr string) string {
	return strings.ReplaceAll(content, oldStr, newStr)
}

// ReplaceContentOnce 只替换第一个匹配的字符串
// content: 原始文本内容
// oldStr: 待替换的字符串
// newStr: 替换后的字符串
// 返回: 替换后的新文本（不修改原文本）
func ReplaceContentOnce(content, oldStr, newStr string) string {
	return strings.Replace(content, oldStr, newStr, 1)
}

// ReplaceContentBatch 批量替换 - 按顺序应用多个替换规则
// content: 原始文本内容
// rules: 替换规则数组
// 返回: 替换后的新文本（不修改原文本）
func ReplaceContentBatch(content string, rules []ReplaceRule) string {
	result := content
	for _, rule := range rules {
		result = strings.ReplaceAll(result, rule.Old, rule.New)
	}
	return result
}

// ReplaceContentWithRegex 使用正则表达式替换文本
// content: 原始文本内容
// pattern: 正则表达式模式
// replacement: 替换文本（支持捕获组引用，如 $1, $2）
// 返回: 替换后的新文本和可能的错误（不修改原文本）
func ReplaceContentWithRegex(content, pattern, replacement string) (string, error) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return content, err
	}
	return regex.ReplaceAllString(content, replacement), nil
}

// ReplaceContentWithRegexFunc 使用正则表达式和自定义函数替换文本
// content: 原始文本内容
// pattern: 正则表达式模式
// replacer: 自定义替换函数，接收匹配的字符串，返回替换结果
// 返回: 替换后的新文本和可能的错误（不修改原文本）
func ReplaceContentWithRegexFunc(content, pattern string, replacer func(string) string) (string, error) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return content, err
	}
	return regex.ReplaceAllStringFunc(content, replacer), nil
}

// ReplaceLineContent 按行替换 - 对每一行应用替换规则
// content: 原始文本内容
// oldStr: 待替换的字符串
// newStr: 替换后的字符串
// 返回: 替换后的新文本（不修改原文本）
func ReplaceLineContent(content, oldStr, newStr string) string {
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lines[i] = strings.ReplaceAll(line, oldStr, newStr)
	}
	return strings.Join(lines, "\n")
}

// ReplaceContentMap 使用映射表进行批量替换 - 同时替换多个不同的字符串
// content: 原始文本内容
// replaceMap: 替换映射表，key为待替换文本，value为替换后文本
// 返回: 替换后的新文本（不修改原文本）
func ReplaceContentMap(content string, replaceMap map[string]string) string {
	result := content
	for old, new := range replaceMap {
		result = strings.ReplaceAll(result, old, new)
	}
	return result
}

// ReplaceContentCaseInsensitive 大小写不敏感的文本替换
// content: 原始文本内容
// oldStr: 待替换的字符串
// newStr: 替换后的字符串
// 返回: 替换后的新文本和可能的错误（不修改原文本）
func ReplaceContentCaseInsensitive(content, oldStr, newStr string) (string, error) {
	// 使用正则表达式实现大小写不敏感替换
	pattern := "(?i)" + regexp.QuoteMeta(oldStr)
	return ReplaceContentWithRegex(content, pattern, newStr)
}

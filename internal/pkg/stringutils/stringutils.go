package stringutils

import (
	"strings"
)

// ShortenUUID takes a UUID string and returns its first 8 characters.
// If the input string is shorter than 8 characters, it returns the original string.
func ShortenUUID(uuidStr string) string {
	if len(uuidStr) > 8 {
		return uuidStr[:8]
	}
	return uuidStr
}

// IsEmpty checks if a string is empty after trimming whitespace.
func IsEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}

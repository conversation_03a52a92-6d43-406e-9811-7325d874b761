package dto

// Response 表示 agent 向服务端发送的响应结构
// Success: 是否成功
// Message: 详细信息（如 "register"）
// Data: 通用数据字段（如 agent id、命令执行结果等）
type Response struct {
	Success bool        `json:"success"` // 是否成功
	Message string      `json:"message"` // 详细信息
	Data    interface{} `json:"data"`    // 通用数据字段
}

// CommandDTO 用于服务端下发命令的消息结构
// type: 命令类型
// data: 命令数据

type CommandDTO struct {
	Type string `json:"type"` // 命令类型
	Data string `json:"data"` // 命令数据
}

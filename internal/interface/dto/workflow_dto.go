package dto

// CopyProjectRequest DTO for copying a project.
type CopyProjectRequest struct {
	SourceProjectID string `json:"source_project_id" binding:"required"`
	TargetProjectID string `json:"target_project_id" binding:"required"`
}
type CopyProjectResult struct {
	NewRepoName string `json:"new_repo_id"` // 沿用Python示例中的字段名 new_repo_id
}

// PublishSiteRequest DTO for publishing a site.
type PublishSiteRequest struct {
	NetlifySiteID string `json:"netlify_site_id"`
	ProjectID     string `json:"project_id"`
}

// DeleteProjectRequest DTO for deleting a project.
type DeleteProjectRequest struct {
	ProjectID string `json:"project_id" binding:"required"`
}

// DeleteProjectResult DTO for delete project response.
type DeleteProjectResult struct {
	ProjectID       string                  `json:"project_id"`
	DeletionResults *ProjectDeletionResults `json:"deletion_results"`
	Errors          []string                `json:"errors"`
}

// ProjectDeletionResults DTO for project deletion results.
type ProjectDeletionResults struct {
	FlyAppDeleted        bool `json:"fly_app_deleted"`
	NetlifySiteDeleted   bool `json:"netlify_site_deleted"`
	ProjectRecordDeleted bool `json:"project_record_deleted"`
	ProjectStatusDeleted bool `json:"project_status_deleted"`
}

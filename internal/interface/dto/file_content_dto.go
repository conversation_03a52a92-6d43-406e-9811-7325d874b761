package dto

// GetRepositoryFileContentsRequest 获取仓库文件内容的请求 DTO
type GetRepositoryFileContentsRequest struct {
	Repository string `json:"repository" binding:"required"` // GitHub 仓库名称
}

// GetRepositoryFileContentsResponse 获取仓库文件内容的响应 DTO
type GetRepositoryFileContentsResponse struct {
	Files []FileInfo `json:"files"` // 文件列表
}

// FileInfo 文件信息 DTO
type FileInfo struct {
	Name     string `json:"name"`     // 文件名
	Contents string `json:"contents"` // 文件内容（仅文本文件）
	Binary   bool   `json:"binary"`   // 是否为二进制文件
}

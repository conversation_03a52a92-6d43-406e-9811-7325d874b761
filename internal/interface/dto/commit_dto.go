package dto

// FileContent 定义了要提交的文件内容。
type FileContent struct {
	Path    string `json:"path" binding:"required"`    // 文件在仓库中的完整路径
	Content string `json:"content" binding:"required"` // 文件内容 (UTF-8 编码)
}

// CommitRequest 定义了提交代码变更的请求结构。
// 这个结构体将用于应用层服务的输入。
type CommitRequest struct {
	Owner         string        `json:"owner" binding:"required"`          // 仓库所有者 (用户名或组织名)
	Repository    string        `json:"repository" binding:"required"`     // 仓库名称
	Branch        string        `json:"branch,omitempty"`                  // 目标分支名称，默认为 "main"
	CommitMessage string        `json:"commit_message" binding:"required"` // 提交信息
	Files         []FileContent `json:"files,omitempty"`                   // 要创建或更新的文件列表
	DeletedFiles  []string      `json:"deleted_files,omitempty"`           // 要从树中删除的文件路径列表
	AuthorName    string        `json:"author_name,omitempty"`             // 可选的提交作者名称
	AuthorEmail   string        `json:"author_email,omitempty"`            // 可选的提交作者邮箱
}

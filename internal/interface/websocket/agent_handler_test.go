package websocket

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
)

// mockAgentRepository 是一个用于测试的 mock repository
type mockAgentRepository struct {
	agents map[string]*entity.Agent
}

func newMockAgentRepository() *mockAgentRepository {
	return &mockAgentRepository{
		agents: make(map[string]*entity.Agent),
	}
}

func (m *mockAgentRepository) Register(ctx context.Context, agent *entity.Agent) error {
	m.agents[agent.ID] = agent
	return nil
}

func (m *mockAgentRepository) Unregister(ctx context.Context, agentID string) error {
	delete(m.agents, agentID)
	return nil
}

func (m *mockAgentRepository) GetByID(ctx context.Context, id string) (*entity.Agent, error) {
	if agent, ok := m.agents[id]; ok {
		return agent, nil
	}
	return nil, nil
}

func (m *mockAgentRepository) UpdateLastActive(ctx context.Context, id string, lastActive int64) error {
	if agent, ok := m.agents[id]; ok {
		agent.LastActive = lastActive
	}
	return nil
}

func (m *mockAgentRepository) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	var agents []*entity.Agent
	for _, agent := range m.agents {
		agents = append(agents, agent)
	}
	return agents, nil
}

func (m *mockAgentRepository) SendCommand(ctx context.Context, agentID string, cmd *entity.Command) error {
	return nil
}

func (m *mockAgentRepository) SendCommandAndWait(ctx context.Context, agentID string, cmd *entity.Command) ([]byte, error) {
	// 模拟返回一个业务响应
	response := dto.Response{
		Success: true,
		Message: "publish_complete",
		Data:    map[string]interface{}{"url": "https://example.com"},
	}
	return json.Marshal(response)
}

// TestPingPongFiltering 测试 ping/pong 消息过滤功能
func TestPingPongFiltering(t *testing.T) {
	// 测试 ping 消息处理
	t.Run("ping message should not be pushed to response channel", func(t *testing.T) {
		// 创建 ping 消息
		pingMsg := dto.Response{
			Success: true,
			Message: "ping",
			Data:    nil,
		}

		pingData, _ := json.Marshal(pingMsg)

		// 模拟处理 ping 消息的逻辑
		var resp dto.Response
		json.Unmarshal(pingData, &resp)

		// 验证消息类型
		if resp.Message != "ping" {
			t.Errorf("Expected ping message, got %s", resp.Message)
		}

		// 在实际的 messageLoop 中，ping 消息不应该被推送到响应通道
		// 这里我们验证消息类型是正确的
	})

	// 测试 pong 消息处理
	t.Run("pong message should not be pushed to response channel", func(t *testing.T) {
		// 创建 pong 消息
		pongMsg := dto.Response{
			Success: true,
			Message: "pong",
			Data:    nil,
		}

		pongData, _ := json.Marshal(pongMsg)

		// 模拟处理 pong 消息的逻辑
		var resp dto.Response
		json.Unmarshal(pongData, &resp)

		// 验证消息类型
		if resp.Message != "pong" {
			t.Errorf("Expected pong message, got %s", resp.Message)
		}
	})

	// 测试业务消息处理
	t.Run("business message should be pushed to response channel", func(t *testing.T) {
		// 创建业务消息
		businessMsg := dto.Response{
			Success: true,
			Message: "publish_complete",
			Data:    map[string]interface{}{"url": "https://example.com"},
		}

		businessData, _ := json.Marshal(businessMsg)

		// 模拟处理业务消息的逻辑
		var resp dto.Response
		json.Unmarshal(businessData, &resp)

		// 验证消息类型
		if resp.Message != "publish_complete" {
			t.Errorf("Expected publish_complete message, got %s", resp.Message)
		}

		// 验证数据
		if data, ok := resp.Data.(map[string]interface{}); ok {
			if url, ok := data["url"].(string); !ok || url != "https://example.com" {
				t.Errorf("Expected URL https://example.com, got %v", url)
			}
		} else {
			t.Error("Expected data to be a map")
		}
	})
}

// TestSendCommandAndWaitFiltering 测试 SendCommandAndWait 方法的过滤功能
func TestSendCommandAndWaitFiltering(t *testing.T) {
	mockRepo := newMockAgentRepository()
	agentManager := agent_service.NewAgentManagerService(mockRepo)

	// 注册一个测试 agent
	agent := &entity.Agent{
		ID:         "test-agent",
		Connected:  true,
		ConnTime:   time.Now().Unix(),
		LastActive: time.Now().Unix(),
	}

	ctx := context.Background()
	mockRepo.Register(ctx, agent)

	// 测试发送命令并等待响应
	cmd := entity.NewPublishCommand()
	response, err := agentManager.SendCommandToAgentAndWait(ctx, "test-agent", &cmd)

	if err != nil {
		t.Fatalf("SendCommandToAgentAndWait failed: %v", err)
	}

	if !response.Success {
		t.Error("Expected successful response")
	}

	if response.Message != "publish_complete" {
		t.Errorf("Expected publish_complete message, got %s", response.Message)
	}

	// 验证返回的数据
	if data, ok := response.Data.(map[string]interface{}); ok {
		if url, ok := data["url"].(string); !ok || url != "https://example.com" {
			t.Errorf("Expected URL https://example.com, got %v", url)
		}
	} else {
		t.Error("Expected data to be a map")
	}
}

package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/coder/websocket"
	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/repository"
	infraWS "github.com/web-builder-dev/be-web-builder/internal/infrastructure/websocket"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

const (
	registerReadTimeout = 30 * time.Second
	messageReadTimeout  = 5 * time.Minute
	heartbeatInterval   = 30 * time.Second
	heartbeatTimeout    = 60 // 单位：秒
	pongWriteTimeout    = 10 * time.Second
)

// RegisterData 表示注册消息的数据结构
type RegisterData struct {
	ID         string `json:"id"`
	FirstTime  bool   `json:"first_time"`
	RepoExists bool   `json:"repo_exists"`
}

// AgentWebSocketHandler 处理 agent websocket 连接
type AgentWebSocketHandler struct {
	agentManager *agent_service.AgentManagerService
	agentSetup   *agent_service.AgentSetupService
	repo         repository.AgentRepository
}

// NewAgentWebSocketHandler 创建 agent websocket 处理器
func NewAgentWebSocketHandler(
	agentManager *agent_service.AgentManagerService,
	agentSetup *agent_service.AgentSetupService,
	repo repository.AgentRepository,
) gin.HandlerFunc {
	h := &AgentWebSocketHandler{
		agentManager: agentManager,
		agentSetup:   agentSetup,
		repo:         repo,
	}
	return h.Handle
}

func (h *AgentWebSocketHandler) Handle(c *gin.Context) {
	conn, err := websocket.Accept(c.Writer, c.Request, &websocket.AcceptOptions{
		CompressionMode: websocket.CompressionDisabled,
	})
	if err != nil {
		logger.Error("failed to accept websocket:", err)
		return
	}
	defer conn.Close(websocket.StatusNormalClosure, "server closed")

	ctx, cancel := context.WithCancel(c.Request.Context())
	defer cancel()

	agentConn := infraWS.NewAgentConnection(conn)

	appName, repoExists, err := h.readRegisterMessage(ctx, conn)
	if err != nil {
		logger.Error("failed to read register message:", err)
		return
	}

	logger.Info("agent registered:", appName, "repo_exists:", repoExists)

	agent := &entity.Agent{
		ID:         appName,
		Connected:  true,
		ConnTime:   time.Now().Unix(),
		LastActive: time.Now().Unix(),
	}

	ctx = context.WithValue(ctx, infraWS.AgentConnKey, agentConn)

	if err := h.agentManager.RegisterAgent(ctx, agent); err != nil {
		logger.Error("failed to register agent:", err)
		return
	}

	h.handleConnection(ctx, agentConn, appName, repoExists)
}

func (h *AgentWebSocketHandler) readRegisterMessage(ctx context.Context, conn *websocket.Conn) (string, bool, error) {
	ctx, cancel := context.WithTimeout(ctx, registerReadTimeout)
	defer cancel()

	_, msg, err := conn.Read(ctx)
	if err != nil {
		return "", false, err
	}

	var regMsg dto.Response
	if err := json.Unmarshal(msg, &regMsg); err != nil {
		return "", false, err
	}

	if !regMsg.Success || regMsg.Message != "register" || regMsg.Data == nil {
		return "", false, fmt.Errorf("invalid register message content: %+v", regMsg)
	}

	// 将 Data 转换为 JSON 字符串，然后解析为 RegisterData
	dataBytes, err := json.Marshal(regMsg.Data)
	if err != nil {
		return "", false, fmt.Errorf("failed to marshal register data: %w", err)
	}

	var registerData RegisterData
	if err := json.Unmarshal(dataBytes, &registerData); err != nil {
		return "", false, fmt.Errorf("failed to unmarshal register data: %w", err)
	}

	return registerData.ID, registerData.RepoExists, nil
}

func (h *AgentWebSocketHandler) handleConnection(ctx context.Context, agentConn *infraWS.AgentConnection, appName string, repoExists bool) {
	defer func() {
		if err := h.agentManager.UnregisterAgent(ctx, appName); err != nil {
			logger.Error("failed to unregister agent:", err)
		}
		logger.Info("Agent unregistered and connection closed for:", appName)
	}()

	errChan := make(chan error, 3)

	// 只有在 repo 不存在时才执行 SetupAgentEnvironment
	if !repoExists {
		go func() {
			if err := h.agentSetup.SetupAgentEnvironment(ctx, agentConn, appName); err != nil {
				logger.Error("Failed to setup agent environment for", appName, ":", err)
				errChan <- err
			}
		}()
	} else {
		logger.Info("Repo already exists for", appName, ", skipping SetupAgentEnvironment")
	}

	go h.startHeartbeatChecker(ctx, appName, errChan)

	go h.messageLoop(ctx, agentConn, appName, errChan)

	select {
	case err := <-errChan:
		logger.Error("Connection error for", appName, ":", err)
	case <-ctx.Done():
		logger.Info("Context cancelled for", appName)
	}
}

func (h *AgentWebSocketHandler) messageLoop(ctx context.Context, agentConn *infraWS.AgentConnection, appName string, errChan chan<- error) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
		}

		_, data, err := agentConn.Conn.Read(ctx)
		if err != nil {
			if websocket.CloseStatus(err) == websocket.StatusNormalClosure || err == context.Canceled {
				logger.Info("Connection closed gracefully for", appName)
			} else {
				errChan <- fmt.Errorf("read error: %w", err)
			}
			return
		}

		var resp dto.Response
		if err := json.Unmarshal(data, &resp); err != nil {
			logger.Error("agent", appName, "invalid message:", err)
			continue
		}

		switch resp.Message {
		case "ping":
			logger.Debug("agent", appName, "heartbeat received")
			if err := h.updateLastActive(ctx, appName); err != nil {
				logger.Error("failed to update last active:", err)
			}
			if err := h.sendPong(ctx, agentConn); err != nil {
				logger.Error("failed to send pong:", err)
			}
			// 不将 ping 消息推送到响应通道，因为它是心跳消息
		case "pong received":
			logger.Debug("agent", appName, "pong received")
			// 不将 pong 消息推送到响应通道，因为它是心跳消息
		default:
			logger.Debug("agent", appName, "pushed response to channel:", resp.Message)
			agentConn.PushResponse(ctx, data)
		}
	}
}

func (h *AgentWebSocketHandler) startHeartbeatChecker(ctx context.Context, appName string, errChan chan<- error) {
	ticker := time.NewTicker(heartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			agent, err := h.repo.GetByID(ctx, appName)
			if err != nil {
				errChan <- fmt.Errorf("failed to get agent for heartbeat check: %w", err)
				return
			}

			if time.Now().Unix()-agent.LastActive > heartbeatTimeout {
				errChan <- fmt.Errorf("agent %s heartbeat timeout", appName)
				return
			}
		case <-ctx.Done():
			return
		}
	}
}

func (h *AgentWebSocketHandler) updateLastActive(ctx context.Context, appName string) error {
	return h.repo.UpdateLastActive(ctx, appName, time.Now().Unix())
}

func (h *AgentWebSocketHandler) sendPong(ctx context.Context, agentConn *infraWS.AgentConnection) error {
	pong := dto.CommandDTO{
		Type: "pong",
		Data: "",
	}
	ctx, cancel := context.WithTimeout(ctx, pongWriteTimeout)
	defer cancel()
	return agentConn.SendJSON(ctx, pong)
}

package rest

import (
	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application/fly_service"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/response"
)

// FlyHandler 处理 Fly.io 相关的 HTTP 请求
type FlyHandler struct {
	flyAppMachineService *fly_service.FlyAppMachineService
}

// NewFlyHandler 创建一个新的 FlyHandler
func NewFlyHandler(flyAppMachineService *fly_service.FlyAppMachineService) *FlyHandler {
	return &FlyHandler{
		flyAppMachineService: flyAppMachineService,
	}
}

// CheckAppExistsRequest 检查应用是否存在的请求结构
type CheckAppExistsRequest struct {
	ProjectID string `uri:"project_id" binding:"required,uuid4"`
}

// CheckAppExistsResponse 检查应用是否存在的响应结构
type CheckAppExistsResponse struct {
	ProjectID string `json:"project_id"`
	Exists    bool   `json:"exists"`
	Message   string `json:"message"`
}

// HandleCheckAppExists 处理检查 Fly.io 应用是否存在的请求
// @Summary 检查 Fly.io 应用是否存在
// @Description <p>检查指定项目的 Fly.io 应用是否存在。如果不存在，会自动创建应用和机器。</p>
// @Description <p><strong>功能说明:</strong></p>
// @Description <ol>
// @Description <li>检查 Fly.io 应用是否存在</li>
// @Description <li>如果不存在，更新项目状态为 in_progress</li>
// @Description <li>创建 Fly.io 应用和机器</li>
// @Description <li>返回检查结果</li>
// @Description </ol>
// @Description <p><strong>参数说明:</strong></p>
// @Description <ul>
// @Description <li><code>project_id</code>: (必填) 项目的 UUID，也是 Fly.io 应用名称</li>
// @Description </ul>
// @Description <p><strong>返回值:</strong></p>
// @Description <pre><code>{
// @Description   "code": 0,
// @Description   "msg": "success",
// @Description   "data": {
// @Description     "project_id": "550e8400-e29b-41d4-a716-************",
// @Description     "exists": true,
// @Description     "message": "App already exists"
// @Description   }
// @Description }</code></pre>
// @Tags Fly.io
// @Accept json
// @Produce json
// @Param project_id path string true "项目ID (UUID格式)"
// @Success 200 {object} response.APIResponse{data=CheckAppExistsResponse} "检查成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /api/fly/apps/{project_id}/check [get]
func (h *FlyHandler) HandleCheckAppExists(c *gin.Context) {
	var req CheckAppExistsRequest
	if err := c.ShouldBindUri(&req); err != nil {
		logger.Error("Invalid request parameters", "Error", err)
		response.FailureWithMsg(c, "Invalid project ID format")
		return
	}

	logger.Info("Checking Fly.io app existence", "ProjectID", req.ProjectID)

	// 检查并确保应用存在
	existed, err := h.flyAppMachineService.CheckAndEnsureAppExists(c.Request.Context(), req.ProjectID)
	if err != nil {
		logger.Error("Failed to check or create Fly.io app", "ProjectID", req.ProjectID, "Error", err)
		response.FailureWithMsg(c, "Failed to check or create Fly.io app: "+err.Error())
		return
	}

	// 构建响应
	var message string
	if existed {
		message = "App already exists"
	} else {
		message = "App was created successfully"
	}

	resp := CheckAppExistsResponse{
		ProjectID: req.ProjectID,
		Exists:    existed,
		Message:   message,
	}

	logger.Info("Successfully checked Fly.io app existence", "ProjectID", req.ProjectID, "Existed", existed)
	response.SuccessWithData(c, resp)
}

package rest

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application"
	appSvc "github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/response"
)

// WorkflowHandler handles HTTP requests related to project workflows.
type WorkflowHandler struct {
	copyProjectService     *appSvc.CopyProjectService
	publishService         *appSvc.PublishService
	commitAndDeployService *appSvc.CommitAndDeployService
	deleteProjectService   *appSvc.DeleteProjectService
	errorHandler           *application.ErrorHandler
}

// NewWorkflowHandler creates a new WorkflowHandler.
func NewWorkflowHandler(
	copySvc *appSvc.CopyProjectService,
	publishSvc *appSvc.PublishService,
	commitAndDeploySvc *appSvc.CommitAndDeployService,
	deleteProjectSvc *appSvc.DeleteProjectService,
	errorHandler *application.ErrorHandler,
) *WorkflowHandler {
	return &WorkflowHandler{
		copyProjectService:     copySvc,
		publishService:         publishSvc,
		commitAndDeployService: commitAndDeploySvc,
		deleteProjectService:   deleteProjectSvc,
		errorHandler:           errorHandler,
	}
}

// CopyProjectRequest 定义了复制项目请求的 JSON 结构。
type CopyProjectRequest struct {
	SourceProjectID string `json:"source_project_id" binding:"required,uuid4"`
	TargetProjectID string `json:"target_project_id" binding:"required,uuid4"`
}

// HandleCopyProject handles the request to copy a project.
// @Summary 复制项目
// @Description <p>触发项目复制流程。</p>
// @Description <p>接收源项目 ID 和目标项目 ID，调用后台服务执行复制操作。返回新创建的 repo_id 和 netlify_site_id。</p>
// @Description <p><strong>复制流程:</strong></p>
// @Description <ol>
// @Description <li>复制 GitHub 仓库</li>
// @Description <li>创建新的 Netlify 站点</li>
// @Description <li>配置部署设置</li>
// @Description <li>更新项目信息</li>
// @Description </ol>
// @Description <p><strong>参数说明:</strong></p>
// @Description <ul>
// @Description <li><code>source_project_id</code>: (必填) 源项目的 UUID</li>
// @Description <li><code>target_project_id</code>: (必填) 目标项目的 UUID</li>
// @Description </ul>
// @Description <p><strong>返回值:</strong></p>
// @Description <pre><code>{
// @Description   "code": 0,
// @Description   "msg": "复制项目成功",
// @Description   "data": {
// @Description     "new_repo_name": "string",
// @Description     "new_netlify_site_id": "string"
// @Description   }
// @Description }</code></pre>
// @Tags workflow
// @Accept json
// @Produce json
// @Param request body dto.CopyProjectRequest true "复制项目请求参数"
// @Success 200 {object} response.APIResponse{data=dto.CopyProjectResult} "复制项目成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "复制项目失败"
// @Router /api/workflow/copy-project [post]
func (h *WorkflowHandler) HandleCopyProject(c *gin.Context) {
	var req dto.CopyProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON for copy project request", "Error", err)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.TargetProjectID, "", "INVALID_REQUEST", "Invalid request payload: "+err.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Invalid request payload: "+err.Error())
		return
	}

	logger.Info("Handling copy project request", "SourceProjectID", req.SourceProjectID, "TargetProjectID", req.TargetProjectID)

	result, err := h.copyProjectService.CopyProject(c.Request.Context(), req.SourceProjectID, req.TargetProjectID)
	if err != nil {
		logger.Error("Failed to execute copy project workflow", "SourceProjectID", req.SourceProjectID, "TargetProjectID", req.TargetProjectID, "Error", err)
		// 更新错误状态
		if updateErr := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.TargetProjectID, "", "WORKFLOW_ERROR", err.Error()); updateErr != nil {
			logger.Error("Failed to update project status on error", "Error", updateErr)
		}
		response.FailureWithMsg(c, "Failed to copy project: "+err.Error())
		return
	}

	logger.Info("Copy project workflow initiated successfully", "TargetProjectID", req.TargetProjectID, "NewRepoName", result.NewRepoName)
	response.SuccessWithData(c, result)
}

// HandlePublishSite handles the request to publish a Netlify site.
// @Summary 发布站点
// @Description <p>执行发布流程：解锁 -> 触发构建 -> 锁定 -> 等待结果。</p>
// @Description <p><strong>发布流程:</strong></p>
// @Description <ol>
// @Description <li>查找当前锁定的生产部署并解锁</li>
// @Description <li>触发新的构建</li>
// @Description <li>锁定新触发的部署</li>
// @Description <li>轮询新部署的状态，直到成功或失败（或超时）</li>
// @Description </ol>
// @Description <p><strong>重要提示:</strong></p>
// @Description <ul>
// @Description <li>这是一个同步接口，会等待部署完成或失败后才返回结果</li>
// @Description <li>部署过程可能需要较长时间，请确保客户端有足够的超时设置</li>
// @Description <li>如果部署失败，将返回详细的错误信息</li>
// @Description </ul>
// @Description <p><strong>参数说明:</strong></p>
// @Description <ul>
// @Description <li><code>netlify_site_id</code>: (必填) Netlify 站点的 ID</li>
// @Description </ul>
// @Description <p><strong>返回值:</strong></p>
// @Description <pre><code>{
// @Description   "code": 0,
// @Description   "msg": "发布流程已启动",
// @Description   "data": null
// @Description }</code></pre>
// @Tags workflow
// @Accept json
// @Produce json
// @Param request body dto.PublishSiteRequest true "发布站点请求参数"
// @Success 200 {object} response.APIResponse "发布流程已启动"
// @Failure 400 {object} response.APIResponse "请求参数错误或缺少 netlify_site_id"
// @Failure 500 {object} response.APIResponse "发布站点失败"
// @Router /api/workflow/publish [post]
func (h *WorkflowHandler) HandlePublishSite(c *gin.Context) {
	var req dto.PublishSiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON for publish site request", "Error", err)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.ProjectID, "", "INVALID_REQUEST", "Invalid request payload: "+err.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Invalid request payload: "+err.Error())
		return
	}

	logger.Info("Handling publish site request", "NetlifySiteID", req.NetlifySiteID, "ProjectID", req.ProjectID)
	if req.ProjectID == "" {
		logger.Error("project_id is required for publishing site")
		response.FailureWithMsg(c, "project_id is required")
		return
	}

	// err := h.publishService.PublishWorkflow(c.Request.Context(), req.NetlifySiteID)
	staticWebsiteURL, err := h.publishService.PublishWorkflowWithFly(c.Request.Context(), req.ProjectID)
	// _, err := h.publishService.PublishWorkflowWithNetlify(c.Request.Context(), req.ProjectID)
	if err != nil {
		logger.Error("Failed to execute publish workflow", "NetlifySiteID", req.NetlifySiteID, "ProjectID", req.ProjectID, "Error", err)
		// 更新错误状态
		if updateErr := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.ProjectID, "", "WORKFLOW_PUBLISH_ERROR", err.Error()); updateErr != nil {
			logger.Error("Failed to update project status on error", "Error", updateErr)
		}
		response.FailureWithMsg(c, "Failed to publish site: "+err.Error())
		return
	}

	logger.Info("Publish workflow initiated successfully", "NetlifySiteID", req.NetlifySiteID)
	response.SuccessWithMsgAndData(c, "Publish workflow initiated successfully for site "+req.NetlifySiteID, staticWebsiteURL)
}

// HandleCommitAndDeploy handles the request to commit files and deploy.
// @Summary 提交并部署
// @Description <p>🚀 <strong>异步后台执行：一步完成代码提交和Netlify部署</strong></p>
// @Description <p>此接口接收请求后会立即返回响应，表示任务已接受并在后台开始处理。实际的 GitHub 提交和 Netlify 部署将在后台异步进行。</p>
// @Description <p><strong>后台流程:</strong></p>
// @Description <ol>
// @Description <li><strong>提交代码:</strong> 将文件提交到 GitHub 仓库</li>
// @Description <li><strong>部署代码:</strong> 触发 Netlify 部署流程</li>
// @Description <li><strong>更新数据库:</strong> 更新项目状态和部署信息</li>
// @Description </ol>
// @Description <p><strong>重要提示:</strong></p>
// @Description <ul>
// @Description <li>由于是后台执行，此接口的直接响应<strong>不会</strong>包含最终的部署结果 (如 deploy_id, status, site_url)</li>
// @Description <li>客户端需要通过其他方式（例如轮询状态接口，或等待 webhook/通知）来获取最终部署状态</li>
// @Description <li>后台任务执行过程中的错误将记录在服务器日志中，不会直接返回给调用此接口的客户端</li>
// @Description </ul>
// @Description <p><strong>参数说明:</strong></p>
// @Description <ul>
// @Description <li><code>project_id</code>: (必填) Supabase 中项目记录的 UUID</li>
// @Description <li><code>files</code>: (可选) 包含要创建或更新的文件路径和内容的列表</li>
// @Description <li><code>deleted_files</code>: (可选) 包含要从仓库中删除的文件路径的列表</li>
// @Description <li><code>commit_message</code>: (可选) 本次提交的 Git commit 信息</li>
// @Description <li><code>repo_id</code>: (可选) 目标 GitHub 仓库的名称</li>
// @Description <li><code>netlify_site_id</code>: (可选) 目标 Netlify 站点的 ID</li>
// @Description <li><code>build_cmd</code>: (可选) Netlify 使用的构建命令</li>
// @Description </ul>
// @Description <p><strong>返回值 (立即响应):</strong></p>
// @Description <pre><code>{
// @Description   "code": 0,
// @Description   "msg": "提交并部署流程已启动",
// @Description   "data": null
// @Description }</code></pre>
// @Tags workflow
// @Accept json
// @Produce json
// @Param request body appSvc.CommitAndDeployRequest true "提交并部署请求参数"
// @Success 200 {object} response.APIResponse "提交并部署流程已启动"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "启动提交并部署流程失败"
// @Router /api/workflow/commit-and-deploy [post]
func (h *WorkflowHandler) HandleCommitAndDeploy(c *gin.Context) {
	var req appSvc.CommitAndDeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON for commit and deploy request", "Error", err, "RequestBody", c.Request.Body)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.ProjectID, req.NetlifySiteID, "INVALID_REQUEST", "Invalid request payload: "+err.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Invalid request payload: "+err.Error())
		return
	}

	logger.Info("Handling commit and deploy request", "ProjectID", req.ProjectID)

	if h.commitAndDeployService == nil {
		logger.Error("CommitAndDeployService is not initialized in WorkflowHandler")
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), req.ProjectID, req.NetlifySiteID, "SERVER_ERROR", "Server configuration error: CommitAndDeployService not available"); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Server configuration error: CommitAndDeployService not available")
		return
	}

	// 在后台 goroutine 中执行提交和部署操作
	go func() {
		// 创建新的 context，不依赖于请求的 context
		ctx := context.Background()
		// err := h.commitAndDeployService.CommitAndDeployWorkflowWithNetlify(ctx, &req)
		err := h.commitAndDeployService.CommitAndDeployWorkflowWithFly(ctx, &req)
		if err != nil {
			logger.Error("Failed to execute commit and deploy workflow", "ProjectID", req.ProjectID, "Error", err)
			// 更新错误状态
			if updateErr := h.errorHandler.UpdateProjectStatusOnError(ctx, req.ProjectID, req.NetlifySiteID, "WORKFLOW_ERROR", err.Error()); updateErr != nil {
				logger.Error("Failed to update project status on error", "Error", updateErr)
			}
			return
		}
		logger.Info("Commit and deploy workflow completed successfully", "ProjectID", req.ProjectID)
	}()

	logger.Info("Commit and deploy workflow initiated in background", "ProjectID", req.ProjectID)
	response.SuccessWithMsg(c, "Commit and deploy workflow initiated successfully for project "+req.ProjectID)
}

// HandleDeleteProject handles the request to delete a project.
// @Summary 删除项目
// @Description <p>🗑️ <strong>删除项目及其所有相关资源</strong></p>
// @Description <p>此接口会删除指定项目的所有相关资源，包括：</p>
// @Description <ol>
// @Description <li><strong>Fly.io 资源:</strong> 删除应用、机器和卷</li>
// @Description <li><strong>Netlify 站点:</strong> 删除关联的 Netlify 站点</li>
// @Description <li><strong>数据库记录:</strong> 删除项目状态和项目记录</li>
// @Description </ol>
// @Description <p><strong>删除顺序:</strong></p>
// @Description <ol>
// @Description <li>获取项目信息</li>
// @Description <li>删除 Fly.io 机器和卷</li>
// @Description <li>删除 Fly.io 应用</li>
// @Description <li>删除 Netlify 站点</li>
// @Description <li>删除项目状态记录</li>
// @Description <li>删除项目记录</li>
// @Description </ol>
// @Description <p><strong>重要提示:</strong></p>
// @Description <ul>
// @Description <li>此操作不可逆，请谨慎使用</li>
// @Description <li>删除过程中如果某个步骤失败，会继续执行其他步骤</li>
// @Description <li>返回结果会详细说明每个组件的删除状态</li>
// @Description <li>建议在删除前备份重要数据</li>
// @Description </ul>
// @Description <p><strong>参数说明:</strong></p>
// @Description <ul>
// @Description <li><code>project_id</code>: (必填) 要删除的项目 UUID</li>
// @Description </ul>
// @Description <p><strong>返回值:</strong></p>
// @Description <pre><code>{
// @Description   "code": 0,
// @Description   "msg": "Project deleted successfully",
// @Description   "data": {
// @Description     "project_id": "uuid",
// @Description     "deletion_results": {
// @Description       "fly_app_deleted": true,
// @Description       "netlify_site_deleted": true,
// @Description       "project_record_deleted": true,
// @Description       "project_status_deleted": true
// @Description     },
// @Description     "errors": []
// @Description   }
// @Description }</code></pre>
// @Tags workflow
// @Accept json
// @Produce json
// @Param project_id path string true "项目ID" format(uuid)
// @Success 200 {object} response.APIResponse{data=dto.DeleteProjectResult} "项目删除成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "项目不存在"
// @Failure 500 {object} response.APIResponse "删除项目失败"
// @Router /api/workflow/projects/{project_id} [delete]
func (h *WorkflowHandler) HandleDeleteProject(c *gin.Context) {
	projectID := c.Param("project_id")
	if projectID == "" {
		logger.Error("Project ID is required for deletion")
		response.FailureWithMsg(c, "Project ID is required")
		return
	}

	logger.Info("Handling delete project request", "ProjectID", projectID)

	if h.deleteProjectService == nil {
		logger.Error("DeleteProjectService is not initialized in WorkflowHandler")
		response.FailureWithMsg(c, "Server configuration error: DeleteProjectService not available")
		return
	}

	result, err := h.deleteProjectService.DeleteProject(c.Request.Context(), projectID)
	if err != nil {
		logger.Error("Failed to execute delete project workflow", "ProjectID", projectID, "Error", err)
		response.FailureWithMsg(c, "Failed to delete project: "+err.Error())
		return
	}

	// 检查删除结果
	allDeleted := result.DeletionResults.FlyAppDeleted &&
		result.DeletionResults.NetlifySiteDeleted &&
		result.DeletionResults.ProjectRecordDeleted &&
		result.DeletionResults.ProjectStatusDeleted

	if allDeleted {
		logger.Info("Project deleted successfully", "ProjectID", projectID)
		response.SuccessWithMsgAndData(c, "Project deleted successfully", result)
	} else if len(result.Errors) > 0 {
		logger.Warn("Project deletion completed with partial success", "ProjectID", projectID, "Errors", result.Errors)
		response.SuccessWithMsgAndData(c, "Project deletion completed with partial success", result)
	} else {
		logger.Info("Project deletion completed", "ProjectID", projectID, "Results", result.DeletionResults)
		response.SuccessWithMsgAndData(c, "Project deletion completed", result)
	}
}

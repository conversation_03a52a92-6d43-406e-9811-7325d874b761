package rest

import (
	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/response"
)

// FileContentHandler 处理文件内容相关的 HTTP 请求
type FileContentHandler struct {
	fileContentService *github_service.FileContentService
}

// NewFileContentHandler 创建一个新的 FileContentHandler 实例
func NewFileContentHandler(fileContentService *github_service.FileContentService) *FileContentHandler {
	return &FileContentHandler{
		fileContentService: fileContentService,
	}
}

// GetRepositoryFileContents 获取 GitHub 仓库文件内容的 HTTP 处理器
// @Summary 获取 GitHub 仓库文件内容
// @Description 获取指定 GitHub 仓库中所有文件的内容
// @Tags GitHub
// @Accept json
// @Produce json
// @Param repository path string true "GitHub 仓库名称"
// @Success 200 {object} dto.GetRepositoryFileContentsResponse "成功获取文件内容"
// @Failure 400 {object} response.ErrorResponse "请求参数错误"
// @Failure 404 {object} response.ErrorResponse "仓库不存在"
// @Failure 500 {object} response.ErrorResponse "服务器内部错误"
// @Router /api/github/{repository}/files [get]
func (h *FileContentHandler) GetRepositoryFileContents(c *gin.Context) {
	repo := c.Param("repository")
	if repo == "" {
		response.FailureWithMsgAndData(c, "Missing repository path parameter", nil)
		return
	}

	// 调用应用服务
	files, err := h.fileContentService.GetRepositoryFileContents(c.Request.Context(), repo)
	if err != nil {
		// 根据错误类型返回不同的状态码
		if err.Error() == "GitHub owner not configured" {
			response.FailureWithMsgAndData(c, "GitHub configuration error", err.Error())
			return
		}
		// 检查是否是 404 错误（仓库不存在）
		if err.Error() == "failed to get tree for main branch: GET https://api.github.com/repos/NextSpace-coder/"+repo+"/git/trees/main?recursive=true: 404 Not Found []" {
			response.FailureWithCodeAndMsgAndData(c, response.CodeNotFound, "Repository not found", "The specified repository does not exist or is not accessible")
			return
		}
		response.FailureWithMsgAndData(c, "Failed to get repository file contents", err.Error())
		return
	}

	// 转换为响应 DTO
	responseFiles := make([]dto.FileInfo, len(files))
	for i, file := range files {
		responseFiles[i] = dto.FileInfo{
			Name:     file.Name,
			Contents: file.Contents,
			Binary:   file.Binary,
		}
	}

	response.SuccessWithData(c, dto.GetRepositoryFileContentsResponse{
		Files: responseFiles,
	})
}

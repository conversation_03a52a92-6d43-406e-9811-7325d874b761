package rest

import (
	"bytes"
	"io"

	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application"
	"github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/response"
)

// NetlifyHookHandler 封装了与 Netlify Webhook 相关的 HTTP 请求处理逻辑。
type NetlifyHookHandler struct {
	processorSvc *workflow_service.NetlifyHookProcessorService
	errorHandler *application.ErrorHandler
}

// NewNetlifyHookHandler 创建一个新的 NetlifyHookHandler 实例。
func NewNetlifyHookHandler(processorSvc *workflow_service.NetlifyHookProcessorService, errorHandler *application.ErrorHandler) *NetlifyHookHandler {
	return &NetlifyHookHandler{
		processorSvc: processorSvc,
		errorHandler: errorHandler,
	}
}

// HandleDeployHook 处理 Netlify 部署相关的 webhook。
// @Summary 处理 Netlify 部署 Webhook
// @Description 接收并处理 Netlify 发送的部署状态更新通知
// @Tags netlify
// @Accept json
// @Produce json
// @Param request body workflow_service.NetlifyDetailedDeploy true "Netlify Webhook 请求体"
// @Success 200 {object} response.APIResponse "Webhook 处理成功"
// @Failure 400 {object} response.APIResponse "请求参数错误或无效的 payload"
// @Failure 500 {object} response.APIResponse "服务器内部处理错误"
// @Router /api/netlify/hooks [post]
func (h *NetlifyHookHandler) HandleDeployHook(c *gin.Context) {
	// 读取原始 JSON 请求体内容
	bodyBytes, err1 := io.ReadAll(c.Request.Body)
	if err1 != nil {
		logger.Error("Failed to read request body", "error", err1)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), "", "", "INVALID_REQUEST", "Failed to read request body: "+err1.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Failed to read request body: "+err1.Error())
		return
	}

	// 打印原始请求体内容
	logger.Info("Raw Netlify webhook payload", "rawBody", string(bodyBytes))

	// 重置请求体以供后续 JSON 解析使用
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	var payload workflow_service.NetlifyDetailedDeploy

	if err := c.ShouldBindJSON(&payload); err != nil {
		logger.Error("Failed to bind Netlify webhook JSON payload", "Error", err)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), "", "", "INVALID_REQUEST", "Invalid request payload: "+err.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Invalid request payload: "+err.Error())
		return
	}

	// Netlify 的 webhook 签名验证 (推荐)
	// X-Netlify-Signature: 验证请求是否真的来自 Netlify
	// 在生产环境中，您应该在这里添加签名验证逻辑。
	// signature := c.GetHeader("X-Netlify-Signature")
	// if !validateSignature(signature, c.Request.Body, "YOUR_NETLIFY_WEBHOOK_SECRET") { // 替换 YOUR_NETLIFY_WEBHOOK_SECRET
	// 	 logger.Warn("Invalid Netlify webhook signature", "Signature", signature)
	// 	 response.FailureWithMsg(c, "Invalid signature")
	// 	 return
	// }
	logger.Info("Received Netlify deploy hook", "SiteID", payload.SiteID, "DeployID", payload.ID, "State", payload.State)

	err := h.processorSvc.ProcessDeployHook(c.Request.Context(), &payload)
	if err != nil {
		logger.Error("Error processing Netlify deploy hook", "SiteID", payload.SiteID, "DeployID", payload.ID, "Error", err)
		// 更新错误状态
		if updateErr := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), "", payload.SiteID, "WORKFLOW_ERROR", err.Error()); updateErr != nil {
			logger.Error("Failed to update project status on error", "Error", updateErr)
		}
		response.FailureWithMsg(c, "Failed to process webhook: "+err.Error())
		return
	}

	logger.Info("Netlify deploy hook processed successfully", "SiteID", payload.SiteID, "DeployID", payload.ID)
	response.SuccessWithMsg(c, "Webhook processed successfully")
}

// validateSignature (示例) 是一个用于验证 Netlify webhook 签名的函数存根。
// 您需要使用实际的逻辑和您的 Netlify webhook 密钥来实现它。
// func validateSignature(signature string, body io.Reader, secret string) bool {
// 	 // 读取 body 内容
// 	 // 使用 HMAC-SHA256 和 secret 计算 body 的哈希值
// 	 // 将计算出的哈希值与 signature 进行比较
// 	 return true // 占位符
// }

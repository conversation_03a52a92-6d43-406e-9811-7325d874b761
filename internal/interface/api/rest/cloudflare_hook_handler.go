package rest

import (
	"bytes"
	"io"

	"github.com/gin-gonic/gin"
	"github.com/web-builder-dev/be-web-builder/internal/application"
	"github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/response"
)

// CloudflareHookHandler 封装了与 Cloudflare Pages Webhook 相关的 HTTP 请求处理逻辑。
type CloudflareHookHandler struct {
	processorSvc *workflow_service.CloudflareHookProcessorService
	errorHandler *application.ErrorHandler
	webhookKey   string
}

// NewCloudflareHookHandler 创建一个新的 CloudflareHookHandler 实例。
func NewCloudflareHookHandler(processorSvc *workflow_service.CloudflareHookProcessorService, errorHandler *application.ErrorHandler, webhookKey string) *CloudflareHookHandler {
	return &CloudflareHookHandler{
		processorSvc: processorSvc,
		errorHandler: errorHandler,
		webhookKey:   webhookKey,
	}
}

// HandleDeployHook 处理 Cloudflare Pages 部署相关的 webhook。
// @Summary 处理 Cloudflare Pages 部署 Webhook
// @Description 接收并处理 Cloudflare Pages 发送的部署状态更新通知
// @Tags cloudflare
// @Accept json
// @Produce json
// @Param request body workflow_service.CloudflareDetailedDeploy true "Cloudflare Pages Webhook 请求体"
// @Success 200 {object} response.APIResponse "Webhook 处理成功"
// @Failure 400 {object} response.APIResponse "请求参数错误或无效的 payload"
// @Failure 401 {object} response.APIResponse "未授权的请求"
// @Failure 500 {object} response.APIResponse "服务器内部处理错误"
// @Router /api/cloudflare/hooks [post]
func (h *CloudflareHookHandler) HandleDeployHook(c *gin.Context) {
	// 验证 webhook 认证
	authKey := c.GetHeader("cf-webhook-auth")
	if authKey == "" || authKey != h.webhookKey {
		logger.Warn("Invalid or missing Cloudflare webhook authentication", "provided_key", authKey)
		response.FailureWithMsg(c, "Unauthorized")
		return
	}

	// 读取原始 JSON 请求体内容
	bodyBytes, err1 := io.ReadAll(c.Request.Body)
	if err1 != nil {
		logger.Error("Failed to read request body", "error", err1)
		// 更新错误状态
		if err := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), "", "", "INVALID_REQUEST", "Failed to read request body: "+err1.Error()); err != nil {
			logger.Error("Failed to update project status on error", "Error", err)
		}
		response.FailureWithMsg(c, "Failed to read request body: "+err1.Error())
		return
	}

	// 打印原始请求体内容
	logger.Info("Raw Cloudflare webhook payload", "rawBody", string(bodyBytes))

	// 重置请求体以供后续 JSON 解析使用
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	var payload workflow_service.CloudflareDetailedDeploy

	if err := c.ShouldBindJSON(&payload); err != nil {
		logger.Error("Failed to bind Cloudflare webhook JSON payload", "Error", err)
		response.FailureWithMsg(c, "Invalid request payload: "+err.Error())
		return
	}

	logger.Info("Received Cloudflare deploy hook", "ProjectID", payload.Data.ProjectID, "DeploymentID", payload.Data.DeploymentID, "Event", payload.Data.Event)
	if payload.Data.ProjectID == "" {
		logger.Info("Received empty data in Cloudflare webhook payload, skipping processing")
		response.SuccessWithMsg(c, "Empty data received, skipping processing")
		return
	}
	err := h.processorSvc.ProcessDeployHook(c.Request.Context(), &payload)
	if err != nil {
		logger.Error("Error processing Cloudflare deploy hook", "ProjectID", payload.Data.ProjectID, "DeploymentID", payload.Data.DeploymentID, "Error", err)
		// 更新错误状态
		if updateErr := h.errorHandler.UpdateProjectStatusOnError(c.Request.Context(), payload.Data.ProjectName, "", "WORKFLOW_ERROR", err.Error()); updateErr != nil {
			logger.Error("Failed to update project status on error", "Error", updateErr)
		}
		response.FailureWithMsg(c, "Failed to process webhook: "+err.Error())
		return
	}

	logger.Info("Cloudflare deploy hook processed successfully", "ProjectID", payload.Data.ProjectID, "DeploymentID", payload.Data.DeploymentID)
	response.SuccessWithMsg(c, "Webhook processed successfully")
}

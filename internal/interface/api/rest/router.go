package rest

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"     // swagger embed files
	ginSwagger "github.com/swaggo/gin-swagger" // gin-swagger middleware
	"go.uber.org/zap"

	// 确保 workflow_service 被导入，如果 NewRouter 参数类型需要
	// "github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"

	// docs is generated by Swag CLI, you have to import it.
	// adjust the path if your docs folder is located elsewhere
	_ "github.com/web-builder-dev/be-web-builder/docs"
	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/repository"
	ws "github.com/web-builder-dev/be-web-builder/internal/interface/websocket"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger" // 确保 logger 包导入了
)

// NewRouter 创建并配置一个新的 Gin 引擎。
// logger: Zap SugaredLogger 实例，用于请求日志记录。
// appEnv: 当前的应用程序环境 (例如 "dev", "prod")，用于设置 Gin 模式。
// workflowHandler: 工作流相关的 HTTP 请求处理器。
// netlifyHookHandler: Netlify Webhook 相关的 HTTP 请求处理器。
// cloudflareHookHandler: Cloudflare Webhook 相关的 HTTP 请求处理器。
// fileContentHandler: 文件内容相关的 HTTP 请求处理器。
// flyHandler: Fly.io 相关的 HTTP 请求处理器。
// agentManager: 代理管理服务。
// agentSetupSvc: 代理设置服务。
// agentRepo: 代理仓库。
func NewRouter(
	logger *zap.SugaredLogger,
	appEnv string,
	workflowHandler *WorkflowHandler,
	netlifyHookHandler *NetlifyHookHandler,
	cloudflareHookHandler *CloudflareHookHandler,
	fileContentHandler *FileContentHandler,
	flyHandler *FlyHandler,
	agentManager *agent_service.AgentManagerService,
	agentSetupSvc *agent_service.AgentSetupService,
	agentRepo repository.AgentRepository,
) *gin.Engine {
	gin.SetMode(selectGinMode(appEnv))
	router := gin.New()

	// 使用 Zap 作为 Gin 的中间件
	router.Use(GinZapLogger(logger), gin.Recovery())

	// 注册健康检查路由
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "UP",
		})
	})

	// Swagger UI 路由
	// 访问 /swagger/index.html 查看文档
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API 路由组
	apiGroup := router.Group("/api")

	// 1. GitHub 路由组
	githubGroup := apiGroup.Group("/github")
	{
		// 文件内容相关路由
		githubGroup.GET(":repository/files", fileContentHandler.GetRepositoryFileContents)
	}

	// 2. Netlify 路由组
	netlifyGroup := apiGroup.Group("/netlify")
	{
		// Webhook 路由
		netlifyGroup.POST("/hooks", netlifyHookHandler.HandleDeployHook)
		// 未来在此处添加其他 Netlify 相关路由
		// 例如: netlifyGroup.POST("/sites", someOtherNetlifyHandler.CreateSite)
	}

	// 3. Cloudflare 路由组
	cloudflareGroup := apiGroup.Group("/cloudflare")
	{
		// Webhook 路由
		cloudflareGroup.POST("/hooks", cloudflareHookHandler.HandleDeployHook)
		// 未来在此处添加其他 Cloudflare 相关路由
	}

	// 4. Workflow 路由组
	workflowGroup := apiGroup.Group("/workflow")
	{
		workflowGroup.POST("/copy-project", workflowHandler.HandleCopyProject)
		workflowGroup.POST("/publish", workflowHandler.HandlePublishSite)
		workflowGroup.POST("/commit-and-deploy", workflowHandler.HandleCommitAndDeploy)
		workflowGroup.DELETE("/projects/:project_id", workflowHandler.HandleDeleteProject)
	}

	// 5. Fly.io 路由组
	flyGroup := apiGroup.Group("/fly")
	{
		flyGroup.GET("/apps/:project_id/check", flyHandler.HandleCheckAppExists)
		// 未来在此处添加其他 Fly.io 相关路由
	}

	// Websocket 路由
	ws.RegisterWebSocketRoutes(router, ws.NewAgentWebSocketHandler(agentManager, agentSetupSvc, agentRepo))

	return router
}

// GinZapLogger 是一个 Gin 中间件，用于将 Gin 的请求日志通过 Zap 输出。
func GinZapLogger(zapLogger *zap.SugaredLogger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		c.Next()

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		errorMessage := c.Errors.ByType(gin.ErrorTypePrivate).String()
		bodySize := c.Writer.Size()

		// 使用 strings.Builder 构建空格分隔的日志消息
		var logMessageBuilder strings.Builder

		// 主消息部分由后续的 if/else if/else 决定

		// 构建键值对部分
		logMessageBuilder.WriteString(fmt.Sprintf("statusCode %d", statusCode))
		logMessageBuilder.WriteString(fmt.Sprintf(" latency %s", latency.String()))
		logMessageBuilder.WriteString(fmt.Sprintf(" clientIP %s", clientIP))
		logMessageBuilder.WriteString(fmt.Sprintf(" method %s", method))
		logMessageBuilder.WriteString(fmt.Sprintf(" path %s", path))
		logMessageBuilder.WriteString(fmt.Sprintf(" bodySize %d", bodySize))

		if query != "" {
			logMessageBuilder.WriteString(fmt.Sprintf(" query %s", query))
		}
		if errorMessage != "" {
			logMessageBuilder.WriteString(fmt.Sprintf(" errorMessage %s", errorMessage))
		}

		finalLogMessage := logMessageBuilder.String()

		if statusCode >= http.StatusInternalServerError {
			logger.Error("Request completed with error", finalLogMessage) // 主消息 + 拼接好的字段
		} else if statusCode >= http.StatusBadRequest {
			logger.Warn("Client error", finalLogMessage) // 主消息 + 拼接好的字段
		} else {
			logger.Info("Request completed", finalLogMessage) // 主消息 + 拼接好的字段
		}
	}
}

// selectGinMode 根据 APP_ENV 选择 Gin 的运行模式
func selectGinMode(appEnv string) string {
	switch strings.ToLower(appEnv) {
	case "prod", "production":
		return gin.ReleaseMode
	case "test":
		return gin.ReleaseMode
	default:
		return gin.ReleaseMode
	}
}

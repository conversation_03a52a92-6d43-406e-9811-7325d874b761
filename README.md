<a name="readme-top"></a>
<h1 align="center">
    <img src="https://github.com/user-attachments/assets/8a0d7272-ac80-4605-a8c6-3785854390e5" alt="be-web-builder" width="500">
  <br>
  be-web-builder
</h1>


本项目是一个 Web Builder 应用的后端服务，旨在通过与 GitHub 和 Netlify 等第三方 API 交互来编排复杂的操作。

## 核心功能

*   **GitHub 集成:** 通过 GitHub API 管理 GitHub 仓库（创建、更新等）、分支、提交、部署密钥以及其他 Git 相关操作。相关客户端代码位于 `internal/infrastructure/github`。
*   **Netlify 集成:** 通过 Netlify API 管理 Netlify 站点（创建、删除等）和部署密钥。相关客户端代码位于 `internal/infrastructure/netlify`。
*   **可扩展的 API 客户端设计:** GitHub 和 Netlify 客户端均采用服务层结构，易于扩展和维护，实现了清晰的关注点分离。

## 技术栈

*   **编程语言:** Go (版本遵循 `go.mod` 文件中的定义)
*   **Web 框架:** Gin (用于处理 REST API)
*   **架构:** 领域驱动设计 (DDD)
*   **日志库:** Zap (通过 `internal/pkg/logger` 中的自定义日志包)
*   **依赖管理:** Go Modules
*   **数据库:** Supabase (PostgreSQL) - (根据项目规划)
*   **配置管理:** YAML 文件 (`conf/config_<env>.yaml`)，可被环境变量覆盖。
*   **容器化:** Docker, Docker Compose (用于部署和保障开发环境一致性)

## 项目结构

本项目遵循领域驱动设计 (DDD) 原则，致力于实现清晰的关注点分离：

*   `cmd/`: 应用程序入口 (例如: `main.go`)。
*   `internal/`: 项目内部代码，不作为库对外暴露。
    *   `application/`: 包含特定用例的操作、应用服务、命令和查询。
    *   `domain/`: 软件的核心，代表业务逻辑、实体、值对象和仓库接口。
    *   `infrastructure/`: 实现技术层面的功能并为上层提供支持，例如数据库访问 (`db/`) 和第三方 API 客户端 (`github/`, `netlify/`)。
    *   `interface/`: 与外部世界交互的适配器层，包括 REST API 处理器 (`api/rest/`) 和数据传输对象 (`dto/`)。
    *   `pkg/`: 可共享的工具包 (例如: `logger/`)。
*   `conf/`: 配置文件 (针对不同环境的 YAML 文件，如 `_dev.yaml`, `_test.yaml`, `_pro.yaml`)。
*   `test/`: 包含单元测试和集成测试，其目录结构与 `internal` 目录保持一致。
*   `Dockerfile`: 用于构建应用程序的 Docker 镜像。
*   `docker-compose.yml`: 用于编排多容器 Docker 应用 (例如: 应用本身、数据库)。
*   `.env.example`: 环境变量模板文件。
*   `go.mod`, `go.sum`: Go 模块定义文件。

## 安装与设置

### 环境要求

*   Go (版本遵循 `go.mod`，例如 1.21 或更高版本)。
*   Docker 和 Docker Compose (可选，用于容器化部署)。
*   如果你计划运行集成测试或使用与 GitHub/Netlify 交互的功能，需要准备相应的访问令牌 (Access Token)。

### 操作步骤

1.  **克隆代码仓库:**
    ```bash
    git clone https://github.com/your-username/be-web-builder.git
    cd be-web-builder
    ```
    (请将 `your-username/be-web-builder.git` 替换为你的实际仓库地址)

2.  **配置:**
    *   应用程序从 `conf/config_<env>.yaml` (例如，开发环境为 `conf/config_dev.yaml`) 加载配置。
    *   项目提供了 `.env.example` 作为环境变量的模板。请将其复制为 `.env` 并进行自定义：
        ```bash
        cp .env.example .env
        ```
    *   重要的、会覆盖 YAML 配置的环境变量包括:
        *   `LOG_LEVEL`: 例如 `debug`, `info`, `warn`, `error` (默认为 `info`)。
        *   `GIN_MODE`: 例如 `debug`, `test`, `release`。
        *   `GITHUB_TOKEN`: 用于 GitHub API 的个人访问令牌。
        *   `GITHUB_OWNER`: 用于相关操作的 GitHub 用户名或组织名。
        *   `NETLIFY_TOKEN`: 用于 Netlify API 的个人访问令牌。
        *   (根据需要添加其他相关的数据库连接字符串或服务端口等配置)

3.  **安装依赖:**
    ```bash
    go mod tidy
    ```

## 运行应用

*   **本地运行 (用于开发):**
    ```bash
    go run cmd/main.go
    ```
    应用启动后，Gin 默认通常以 debug 模式运行，并监听特定端口 (例如 8080，具体请查阅配置)。

*   **使用 Docker 运行 (用于类生产环境或部署):**
    确保你的 `.env` 文件已针对生产环境正确配置。
    ```bash
    docker-compose up -d
    ```
    此命令会构建镜像 (如果尚未构建) 并启动 `docker-compose.yml` 中定义的服务。

## 运行测试

测试代码位于 `test/` 目录下，其结构与 `internal/` 目录保持一致。

*   **运行所有测试:**
    ```bash
    go test ./...
    ```

*   **运行特定包的测试:**
    例如，运行所有 GitHub 集成测试：
    ```bash
    go test ./test/infrastructure/github/...
    ```
    运行所有 Netlify 集成测试：
    ```bash
    go test ./test/infrastructure/netlify/...
    ```

*   **集成测试注意事项:**
    *   GitHub 和 Netlify 的集成测试需要有效的 API 令牌。
    *   这些令牌应配置在 `conf/config_dev.yaml` (方便本地测试) 或通过环境变量 (`GITHUB_TOKEN`, `NETLIFY_TOKEN`) 提供。
    *   如果未找到所需令牌，测试用例会跳过执行，以避免在不具备令牌的环境中 (例如某些 CI 阶段) 发生失败。

## API 端点

*(当 API 端点开发完成后，请更新此部分。具体定义的路由请参考 `internal/interface/api/rest/router.go`。)*

示例 (概念性):
*   `POST /api/v1/github/repositories` - 创建一个新的 GitHub 仓库。
*   `GET /api/v1/netlify/sites` - 列出 Netlify 站点。

## 开发规范

*   **架构:** 严格遵循领域驱动设计 (DDD) 原则，具体参考项目内部文档及借鉴如 `sklinkert/go-ddd` 等实践。
    *   **领域层纯洁性:** 领域层必须保持纯净，不依赖于其他层。它定义由基础设施层实现的接口。
    *   **清晰的边界:** 保持领域层、应用层、基础设施层和接口层之间的清晰分离。
    *   **校验:** 实体校验在领域层进行。
    *   **默认值:** 领域实体负责设置其默认值 (例如 ID、时间戳)。
*   **日志:**
    *   使用位于 `internal/pkg/logger` 的基于 `zap` 的自定义日志器。
    *   所有由应用程序代码产生的日志信息必须使用 **英文**。
*   **注释:**
    *   所有的 Go 代码 (函数、结构体、接口、方法) 都应有详细的注释，解释其用途、参数和返回值。
    *   代码注释必须使用 **中文**。
*   **Commit Message:**
    *   遵循 [Conventional Commits](https://www.conventionalcommits.org/zh-hans/v1.0.0/) 规范。
    *   示例: `feat: 添加用户认证功能`, `fix: 修正 API 响应中的拼写错误`, `refactor: 简化 GitHub 服务逻辑`, `docs: 更新 README 中的安装说明`, `test: 为 Netlify 客户端添加集成测试`。
*   **通用原则:**
    *   遵循成熟的软件工程最佳实践。虽然《[阿里巴巴 Java 开发手册](https://github.com/alibaba/p3c)》是针对 Java 的，但其关于代码质量、命名规范、异常处理等通用原则具有很好的参考价值。
    *   践行最小改动原则：在处理特定需求或缺陷时，避免进行无关的修改，以降低引入新问题的风险。如果发现无关问题，请记录或另行讨论。

## 如何贡献

我们欢迎各种形式的贡献！请遵循以下步骤：
1.  Fork 本仓库。
2.  创建一个新的功能分支 (`git checkout -b feature/your-awesome-feature`)。
3.  进行代码修改，并遵守项目的编码风格和规范。
4.  为你的改动编写或更新测试用例。
5.  确保所有测试通过 (`go test ./...`)。
6.  使用 Conventional Commits 规范提交你的改动。
7.  将你的分支推送到你的 Fork (`git push origin feature/your-awesome-feature`)。
8.  向上游主仓库发起 Pull Request。

## 开源许可

(待定 - 请选择并添加一个 `LICENSE` 文件，例如 MIT, Apache 2.0)
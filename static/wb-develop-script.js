<script>
/**
 * wb-develop-script.js
 * 
 * 这个脚本的唯一作用是动态加载 iframe-bridge.js
 * 这样可以在不修改注入脚本的情况下，通过修改 iframe-bridge.js 来实时控制网站
 */

(function() {
  // 创建script元素
  const script = document.createElement('script');
  
  // 设置script属性
  script.src = "https://code.webbuilder.site/iframe-bridge.js";
  script.async = true;
  
  // 添加加载错误处理
  script.onerror = function() {
    console.error('无法加载');
  };
  
  // 将script元素添加到页面
  document.head.appendChild(script);
})();
</script>

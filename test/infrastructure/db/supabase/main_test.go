package supabase_test

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	domainRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	supabaseInfra "github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain is executed before any other tests in this package.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test") // Initialize logger for tests
	os.Exit(m.Run())
}

// setupSupabaseTestClientAndRepos loads config and initializes the Supabase client and repositories.
// Skips test if essential Supabase config is missing.
func setupSupabaseTestClientAndRepos(t *testing.T) (context.Context, domainRepo.ProjectRepository, domainRepo.ProjectStatusRepository) {
	t.Helper()

	cfg, err := config.LoadConfig("dev") // Assuming 'dev' config for tests
	if err != nil {
		t.Skipf("Skipping Supabase integration test: Could not load config for env 'dev': %v", err)
	}

	if cfg.Supabase.URL == "" || cfg.Supabase.ServiceKey == "" {
		t.Skip("Skipping Supabase integration test: SUPABASE_URL and SUPABASE_SERVICE_KEY must be configured.")
	}

	sbClient, err := supabaseInfra.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	require.NoError(t, err, "Failed to create Supabase client")
	require.NotNil(t, sbClient, "Supabase client should not be nil")

	projectRepo := supabaseInfra.NewSupabaseProjectRepository(sbClient)
	projectStatusRepo := supabaseInfra.NewSupabaseProjectStatusRepository(sbClient)

	ctx := context.Background()
	return ctx, projectRepo, projectStatusRepo
}

// TestSupabaseIntegrationSuite is the main entry point for running ordered Supabase integration tests.
// It sets up the necessary services and then calls test execution functions from other files.
func TestSupabaseIntegrationSuite(t *testing.T) {
	ctx, projectRepo, projectStatusRepo := setupSupabaseTestClientAndRepos(t)

	t.Run("ProjectRepositoryCRUD", func(t *testing.T) {
		executeProjectRepositoryCRUDTests(t, ctx, projectRepo)
	})

	t.Run("ProjectStatusRepositoryCRUD", func(t *testing.T) {
		// ProjectStatus tests need a ProjectRepository to create a parent project first.
		executeProjectStatusRepositoryCRUDTests(t, ctx, projectRepo, projectStatusRepo)
	})

	// Add more test groups here if needed
}

package supabase_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	domainRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
)

// executeProjectRepositoryCRUDTests 包含对 ProjectRepository 实现的CRUD操作测试。
func executeProjectRepositoryCRUDTests(t *testing.T, ctx context.Context, projectRepo domainRepo.ProjectRepository) {
	t.Helper()

	// 用于测试的唯一用户ID，避免与其他测试或数据冲突
	// 注意：Supabase RLS 通常基于请求中的 JWT user_id，这里的 user_id 仅用于数据模型
	testUserID := "99863bea-fcb2-4eeb-b492-6573414c118f" //测试用户ID
	var createdProjectID string

	t.Run("CreateProject", func(t *testing.T) {
		project := &entity.Project{
			UserID:            testUserID,
			Title:             "Test Project Title",
			IsPublished:       false,
			SupabaseProjectID: "supabase-proj-id-test",
			RepoID:            "github.com/test/repo",
			NetlifySiteID:     "netlify-site-id-test",
			// ID, CreatedAt, UpdatedAt 会在Create方法中或数据库中设置
		}
		err := projectRepo.Create(ctx, project)
		require.NoError(t, err, "CreateProject should not return an error")
		require.NotEmpty(t, project.ID, "Project ID should be set after creation")
		require.NotZero(t, project.CreatedAt, "Project CreatedAt should be set after creation")
		require.NotZero(t, project.UpdatedAt, "Project UpdatedAt should be set after creation")
		createdProjectID = project.ID // 保存ID用于后续测试和清理
		t.Logf("Created project with ID: %s, UserID: %s", project.ID, project.UserID)

		// 清理：在父测试(ProjectRepositoryCRUD)结束后删除创建的项目
		// 这里不能直接 t.Cleanup，因为它会在这个子测试结束时运行
	})

	// 确保在CreateProject之后才运行依赖createdProjectID的测试
	require.NotEmpty(t, createdProjectID, "createdProjectID must be set by CreateProject test")

	t.Cleanup(func() {
		if createdProjectID != "" {
			t.Logf("Cleaning up project ID: %s", createdProjectID)
			delErr := projectRepo.Delete(ctx, createdProjectID)
			// 对于清理操作，通常不严格要求NoError，但记录错误是有益的
			if delErr != nil {
				t.Logf("Warning: failed to delete project %s during cleanup: %v", createdProjectID, delErr)
			}
		}
	})

	t.Run("GetProjectByID_Found", func(t *testing.T) {
		project, err := projectRepo.GetByID(ctx, createdProjectID)
		require.NoError(t, err, "GetProjectByID_Found should not return an error")
		require.NotNil(t, project, "GetProjectByID_Found should return a project")
		assert.Equal(t, createdProjectID, project.ID)
		assert.Equal(t, testUserID, project.UserID)
		assert.Equal(t, "Test Project Title", project.Title)
		assert.False(t, project.IsPublished)
		t.Logf("Successfully fetched project by ID: %s", project.ID)
	})

	t.Run("GetProjectByID_NotFound", func(t *testing.T) {
		nonExistentID := uuid.NewString()
		project, err := projectRepo.GetByID(ctx, nonExistentID)
		require.Error(t, err, "GetProjectByID_NotFound should return an error for non-existent ID")
		assert.Nil(t, project, "GetProjectByID_NotFound should return a nil project")
		// TODO: 检查错误是否为特定的 domain.ErrNotFound 类型，如果已定义
		t.Logf("Correctly failed to fetch non-existent project ID: %s, Error: %v", nonExistentID, err)
	})

	t.Run("UpdateProject", func(t *testing.T) {
		projectToUpdate, err := projectRepo.GetByID(ctx, createdProjectID)
		require.NoError(t, err)
		require.NotNil(t, projectToUpdate)

		originalUpdatedAt := projectToUpdate.UpdatedAt
		newTitle := "Updated Test Project Title"
		projectToUpdate.Title = newTitle
		projectToUpdate.IsPublished = true

		// 等待一秒，确保UpdatedAt有足够的时间差来改变
		time.Sleep(1 * time.Second)

		err = projectRepo.Update(ctx, projectToUpdate)
		require.NoError(t, err, "UpdateProject should not return an error")
		assert.Equal(t, newTitle, projectToUpdate.Title, "Project title should be updated")
		assert.True(t, projectToUpdate.IsPublished, "Project IsPublished should be updated")
		assert.NotEqual(t, originalUpdatedAt, projectToUpdate.UpdatedAt, "Project UpdatedAt should be changed after update")
		t.Logf("Successfully updated project ID: %s, NewTitle: %s, UpdatedAt: %v", projectToUpdate.ID, projectToUpdate.Title, projectToUpdate.UpdatedAt)
	})

	t.Run("UpdateProjectFields_Success", func(t *testing.T) {
		require.NotEmpty(t, createdProjectID, "createdProjectID must be set for UpdateProjectFields test")

		// 获取当前项目的 UpdatedAt 时间，以便后续比较
		projectBeforeFieldUpdate, err := projectRepo.GetByID(ctx, createdProjectID)
		require.NoError(t, err)
		originalUpdatedAt := projectBeforeFieldUpdate.UpdatedAt

		newNetlifySiteID := "new-netlify-id-via-fields-" + uuid.NewString()
		updates := map[string]any{
			"title":           "Title Updated via Fields",
			"is_published":    false, // 之前在 UpdateProject 中设置为 true
			"netlify_site_id": newNetlifySiteID,
			"repo_id":         "", // 修改：使用空字符串代替nil，以满足NOT NULL约束
		}

		time.Sleep(1 * time.Second) // 确保 UpdatedAt 会变化

		updatedProject, err := projectRepo.UpdateFields(ctx, createdProjectID, updates)
		require.NoError(t, err, "UpdateProjectFields should not return an error for successful update")
		require.NotNil(t, updatedProject, "UpdateProjectFields should return the updated project")

		assert.Equal(t, createdProjectID, updatedProject.ID)
		assert.Equal(t, "Title Updated via Fields", updatedProject.Title)
		assert.False(t, updatedProject.IsPublished)
		assert.Equal(t, newNetlifySiteID, updatedProject.NetlifySiteID)
		assert.Equal(t, "", updatedProject.RepoID) //  nil 在数据库中通常变为空字符串或被忽略, 假设变为空字符串
		assert.NotEqual(t, originalUpdatedAt, updatedProject.UpdatedAt, "UpdatedAt should change after field update")
		// 验证未包含在updates中的字段 (如UserID, SupabaseProjectID) 保持不变
		assert.Equal(t, testUserID, updatedProject.UserID) // 假设 testUserID 是初始创建时的 UserID
		assert.Equal(t, projectBeforeFieldUpdate.SupabaseProjectID, updatedProject.SupabaseProjectID)

		t.Logf("Successfully updated project fields for ID: %s. New Title: '%s', IsPublished: %v, New NetlifySiteID: '%s'",
			updatedProject.ID, updatedProject.Title, updatedProject.IsPublished, updatedProject.NetlifySiteID)

		// 再次获取以确认数据库中的值
		verifiedProject, errGet := projectRepo.GetByID(ctx, createdProjectID)
		require.NoError(t, errGet)
		assert.Equal(t, "Title Updated via Fields", verifiedProject.Title)
		assert.False(t, verifiedProject.IsPublished)
		assert.Equal(t, newNetlifySiteID, verifiedProject.NetlifySiteID)
		assert.Equal(t, "", verifiedProject.RepoID)
	})

	t.Run("UpdateProjectFields_NonExistentID", func(t *testing.T) {
		nonExistentID := uuid.NewString()
		updates := map[string]any{"title": "Trying to update non-existent"}
		project, err := projectRepo.UpdateFields(ctx, nonExistentID, updates)
		assert.Error(t, err, "UpdateProjectFields should return an error for non-existent ID")
		assert.Nil(t, project, "Returned project should be nil on error")
		t.Logf("Correctly failed to update fields for non-existent project ID: %s, Error: %v", nonExistentID, err)
	})

	t.Run("UpdateProjectFields_EmptyID", func(t *testing.T) {
		updates := map[string]any{"title": "Trying to update with empty ID"}
		project, err := projectRepo.UpdateFields(ctx, "", updates)
		assert.Error(t, err, "UpdateProjectFields should return an error for empty project ID")
		assert.Nil(t, project, "Returned project should be nil on error")
		t.Logf("Correctly failed to update fields for empty project ID, Error: %v", err)
	})

	t.Run("UpdateProjectFields_EmptyUpdates", func(t *testing.T) {
		updates := map[string]any{}
		project, err := projectRepo.UpdateFields(ctx, createdProjectID, updates)
		assert.Error(t, err, "UpdateProjectFields should return an error for empty updates map")
		assert.Nil(t, project, "Returned project should be nil on error")
		t.Logf("Correctly failed to update fields for project ID %s with empty updates, Error: %v", createdProjectID, err)
	})

	// GetProjectsByUserID 测试应该在 UpdateProjectFields 之后，以反映最新的更改
	t.Run("GetProjectsByUserID", func(t *testing.T) {
		// 先删除之前创建的，确保测试环境干净，或者使用不同的UserID
		// 为简单起见，我们继续使用 testUserID，并期望此测试能找到上面创建/更新过的项目

		// 如果需要创建更多项目进行测试：
		// project2 := &entity.Project{UserID: testUserID, Title: "Another Project"}
		// err := projectRepo.Create(ctx, project2)
		// require.NoError(t, err)
		// t.Cleanup(func() { projectRepo.Delete(ctx, project2.ID) })

		projects, err := projectRepo.GetByUserID(ctx, testUserID)
		require.NoError(t, err, "GetProjectsByUserID should not return an error")
		require.NotEmpty(t, projects, "Projects list should not be empty for test user")
		assert.Len(t, projects, 1, "Should find 1 project for the test user ID") // 基于当前测试只创建了一个

		found := false
		for _, p := range projects {
			if p.ID == createdProjectID {
				// 注意: 这里的断言应该基于 UpdateProjectFields_Success 中的最新更改
				assert.Equal(t, "Title Updated via Fields", p.Title)
				found = true
				break
			}
		}
		assert.True(t, found, fmt.Sprintf("Project with ID %s not found in list for UserID %s", createdProjectID, testUserID))
		t.Logf("Successfully fetched %d projects for UserID: %s", len(projects), testUserID)
	})

	t.Run("DeleteProject", func(t *testing.T) {
		err := projectRepo.Delete(ctx, createdProjectID)
		require.NoError(t, err, "DeleteProject should not return an error")
		t.Logf("Successfully deleted project ID: %s", createdProjectID)

		// 验证项目是否真的被删除了
		deletedProject, err := projectRepo.GetByID(ctx, createdProjectID)
		assert.Error(t, err, "GetByID after delete should return an error")
		assert.Nil(t, deletedProject, "GetByID after delete should return nil project")

		createdProjectID = "" // 清除ID，防止Cleanup再次尝试删除
	})
}

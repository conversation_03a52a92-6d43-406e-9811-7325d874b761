package supabase_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	domainRepo "github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
)

// executeProjectStatusRepositoryCRUDTests 包含对 ProjectStatusRepository 实现的CRUD操作测试。
func executeProjectStatusRepositoryCRUDTests(t *testing.T, ctx context.Context, projectRepo domainRepo.ProjectRepository, projectStatusRepo domainRepo.ProjectStatusRepository) {
	t.Helper()
	testUserID := "aeb3470b-1f3b-4e50-a4c1-f180aeffd1e1"               //测试用户ID
	uniqueNetlifySiteID := "test-netlify-site-" + uuid.NewString()     // 用于测试的 Netlify Site ID
	anotherNetlifySiteID := "another-netlify-site-" + uuid.NewString() // 用于测试 NetlifySiteID 存在但 ProjectStatus 不存在的场景

	// 1. 首先创建一个父项目，因为 project_status依赖于 project
	parentProject := &entity.Project{
		UserID: testUserID,
		Title:  "Parent Project for Status Test",
		RepoID: "github.com/test/status-repo",
		// NetlifySiteID 将在后续测试中设置
	}
	err := projectRepo.Create(ctx, parentProject)
	require.NoError(t, err, "Failed to create parent project for status tests")
	require.NotEmpty(t, parentProject.ID, "Parent project ID should be set")
	t.Logf("Created parent project with ID: %s for status tests", parentProject.ID)

	// 为 GetByNetlifySiteID_NoProjectStatus 测试创建另一个项目
	projectForNoStatusTest := &entity.Project{
		UserID:        testUserID,
		Title:         "Project for No Status Test",
		RepoID:        "github.com/test/no-status-repo",
		NetlifySiteID: anotherNetlifySiteID, // 设置 NetlifySiteID
	}
	err = projectRepo.Create(ctx, projectForNoStatusTest)
	require.NoError(t, err, "Failed to create project for no status test")
	require.NotEmpty(t, projectForNoStatusTest.ID, "Project for no status test ID should be set")
	t.Logf("Created project with ID: %s and NetlifySiteID: %s for no status test", projectForNoStatusTest.ID, anotherNetlifySiteID)

	// 清理父项目和另一个项目
	t.Cleanup(func() {
		t.Logf("Cleaning up parent project ID: %s (used for status tests)", parentProject.ID)
		delErr := projectRepo.Delete(ctx, parentProject.ID)
		if delErr != nil {
			t.Logf("Warning: failed to delete parent project %s during status test cleanup: %v", parentProject.ID, delErr)
		}
		t.Logf("Cleaning up project ID: %s (used for no status test)", projectForNoStatusTest.ID)
		delErr = projectRepo.Delete(ctx, projectForNoStatusTest.ID)
		if delErr != nil {
			t.Logf("Warning: failed to delete project %s during no status test cleanup: %v", projectForNoStatusTest.ID, delErr)
		}
	})

	var currentProjectStatus *entity.ProjectStatus

	t.Run("CreateProjectStatus", func(t *testing.T) {
		status := &entity.ProjectStatus{
			ProjectID:     parentProject.ID,
			Status:        entity.ProjectStatusStarted,
			PreviewLink:   "http://preview.example.com/start",
			PublishLink:   "",
			SiteThumbnail: "http://thumb.example.com/start.jpg",
		}
		err := projectStatusRepo.CreateOrUpdate(ctx, status)
		require.NoError(t, err, "CreateOrUpdate (create) for project status should not return an error")
		require.NotZero(t, status.CreatedAt, "Status CreatedAt should be set")
		require.NotZero(t, status.UpdatedAt, "Status UpdatedAt should be set")
		assert.Equal(t, entity.ProjectStatusStarted, status.Status)
		currentProjectStatus = status
		t.Logf("Created project status for ProjectID: %s, Status: %s", status.ProjectID, status.Status)

		// 创建完状态后，更新父项目的 NetlifySiteID 以便后续测试 GetByNetlifySiteID
		parentProject.NetlifySiteID = uniqueNetlifySiteID
		parentProject.UpdatedAt = time.Now() // 确保更新时间戳
		err = projectRepo.Update(ctx, parentProject)
		require.NoError(t, err, "Failed to update parent project with NetlifySiteID")
		t.Logf("Updated parent project %s with NetlifySiteID: %s", parentProject.ID, uniqueNetlifySiteID)
	})

	require.NotNil(t, currentProjectStatus, "currentProjectStatus must be set by CreateProjectStatus test")

	t.Run("GetProjectStatusByProjectID_Found", func(t *testing.T) {
		status, err := projectStatusRepo.GetByProjectID(ctx, parentProject.ID)
		require.NoError(t, err, "GetByProjectID_Found should not return an error")
		require.NotNil(t, status, "GetByProjectID_Found should return a status object")
		assert.Equal(t, parentProject.ID, status.ProjectID)
		assert.Equal(t, entity.ProjectStatusStarted, status.Status)
		assert.Equal(t, "http://preview.example.com/start", status.PreviewLink)
		t.Logf("Successfully fetched project status for ProjectID: %s", status.ProjectID)
	})

	t.Run("UpdateProjectStatus", func(t *testing.T) {
		statusToUpdate, err := projectStatusRepo.GetByProjectID(ctx, parentProject.ID)
		require.NoError(t, err)
		require.NotNil(t, statusToUpdate)

		originalUpdatedAt := statusToUpdate.UpdatedAt
		statusToUpdate.Status = entity.ProjectStatusInProgress
		statusToUpdate.PreviewLink = "http://preview.example.com/progress"
		statusToUpdate.ErrorCode = "NETLIFY_BUILD_FAILED"
		statusToUpdate.ErrorMsg = "Netlify build timed out."

		// 等待确保 UpdatedAt 会变化
		time.Sleep(1 * time.Second)

		err = projectStatusRepo.CreateOrUpdate(ctx, statusToUpdate) // This is an upsert
		require.NoError(t, err, "CreateOrUpdate (update) for project status should not return an error")
		assert.Equal(t, entity.ProjectStatusInProgress, statusToUpdate.Status)
		assert.Equal(t, "http://preview.example.com/progress", statusToUpdate.PreviewLink)
		assert.Equal(t, "NETLIFY_BUILD_FAILED", statusToUpdate.ErrorCode)
		assert.NotEqual(t, originalUpdatedAt, statusToUpdate.UpdatedAt, "Status UpdatedAt should change after update")
		currentProjectStatus = statusToUpdate // Keep track of the latest version
		t.Logf("Successfully updated project status for ProjectID: %s to Status: %s", statusToUpdate.ProjectID, statusToUpdate.Status)
	})

	t.Run("GetProjectStatusByProjectID_NotFound", func(t *testing.T) {
		nonExistentProjectID := uuid.NewString()
		status, err := projectStatusRepo.GetByProjectID(ctx, nonExistentProjectID)
		require.Error(t, err, "GetByProjectID should return an error for non-existent project ID")
		assert.Nil(t, status, "GetByProjectID should return a nil status for non-existent project ID")
		t.Logf("Correctly failed to fetch status for non-existent ProjectID: %s, Error: %v", nonExistentProjectID, err)
	})

	// 新增 GetByNetlifySiteID 的测试用例
	t.Run("GetByNetlifySiteID_Found", func(t *testing.T) {
		require.NotEmpty(t, uniqueNetlifySiteID, "uniqueNetlifySiteID should be set for this test")
		status, err := projectStatusRepo.GetByNetlifySiteID(ctx, uniqueNetlifySiteID)
		require.NoError(t, err, "GetByNetlifySiteID_Found should not return an error")
		require.NotNil(t, status, "GetByNetlifySiteID_Found should return a status object")
		assert.Equal(t, parentProject.ID, status.ProjectID)         // 验证是否是正确的项目状态
		assert.Equal(t, currentProjectStatus.Status, status.Status) // 验证状态是否与最近更新的一致
		assert.Equal(t, currentProjectStatus.PreviewLink, status.PreviewLink)
		t.Logf("Successfully fetched project status via NetlifySiteID: %s, for ProjectID: %s", uniqueNetlifySiteID, status.ProjectID)
	})

	t.Run("GetByNetlifySiteID_NotFound_NonExistentNetlifyID", func(t *testing.T) {
		nonExistentNetlifyID := "netlify-id-does-not-exist-" + uuid.NewString()
		status, err := projectStatusRepo.GetByNetlifySiteID(ctx, nonExistentNetlifyID)
		require.Error(t, err, "GetByNetlifySiteID should return an error for non-existent NetlifySiteID")
		assert.Nil(t, status, "GetByNetlifySiteID should return a nil status for non-existent NetlifySiteID")
		t.Logf("Correctly failed to fetch status for non-existent NetlifySiteID: %s, Error: %v", nonExistentNetlifyID, err)
	})

	t.Run("GetByNetlifySiteID_NotFound_NoProjectStatusRecord", func(t *testing.T) {
		require.NotEmpty(t, anotherNetlifySiteID, "anotherNetlifySiteID should be set for this test")
		// projectForNoStatusTest 存在且有 anotherNetlifySiteID，但没有对应的 project_status 记录
		status, err := projectStatusRepo.GetByNetlifySiteID(ctx, anotherNetlifySiteID)
		require.Error(t, err, "GetByNetlifySiteID should return an error if project exists but status record does not")
		assert.Nil(t, status, "GetByNetlifySiteID should return a nil status if project exists but status record does not")
		t.Logf("Correctly failed to fetch status for NetlifySiteID %s (project_status record missing), Error: %v", anotherNetlifySiteID, err)
	})

	t.Run("GetByNetlifySiteID_EmptyInput", func(t *testing.T) {
		status, err := projectStatusRepo.GetByNetlifySiteID(ctx, "")
		require.Error(t, err, "GetByNetlifySiteID should return an error for empty NetlifySiteID")
		assert.Nil(t, status, "GetByNetlifySiteID should return a nil status for empty NetlifySiteID")
		t.Logf("Correctly failed to fetch status for empty NetlifySiteID, Error: %v", err)
	})

	// 新增 UpdateByNetlifySiteID 的测试用例
	t.Run("UpdateByNetlifySiteID_Success", func(t *testing.T) {
		require.NotEmpty(t, uniqueNetlifySiteID, "uniqueNetlifySiteID must be set for this test")
		require.NotNil(t, currentProjectStatus, "currentProjectStatus must be set by prior tests")

		updates := map[string]any{
			"status":       entity.ProjectStatusDone,
			"preview_link": "http://preview.example.com/done-via-netlify-id",
			"error_code":   "",  // 清除之前的错误代码
			"error_msg":    nil, // 测试将字段设置为null
		}

		originalUpdatedAt := currentProjectStatus.UpdatedAt
		time.Sleep(1 * time.Second) // 确保 updated_at 会改变

		updatedStatus, err := projectStatusRepo.UpdateByNetlifySiteID(ctx, uniqueNetlifySiteID, updates)
		require.NoError(t, err, "UpdateByNetlifySiteID should not return an error for successful update")
		require.NotNil(t, updatedStatus, "UpdateByNetlifySiteID should return the updated status")

		assert.Equal(t, parentProject.ID, updatedStatus.ProjectID)
		assert.Equal(t, entity.ProjectStatusDone, updatedStatus.Status)
		assert.Equal(t, "http://preview.example.com/done-via-netlify-id", updatedStatus.PreviewLink)
		assert.Equal(t, "", updatedStatus.ErrorCode)
		assert.Equal(t, "", updatedStatus.ErrorMsg) // nil 在数据库中通常变为空字符串或被忽略，取决于数据库和ORM行为，这里假设它会是空字符串
		assert.NotEqual(t, originalUpdatedAt, updatedStatus.UpdatedAt, "UpdatedAt should change after update")

		t.Logf("Successfully updated project status via NetlifySiteID: %s, New Status: %s, New Preview: %s", uniqueNetlifySiteID, updatedStatus.Status, updatedStatus.PreviewLink)

		// 验证数据库中的数据是否真的被更新了
		verifiedStatus, errGet := projectStatusRepo.GetByProjectID(ctx, parentProject.ID)
		require.NoError(t, errGet)
		assert.Equal(t, entity.ProjectStatusDone, verifiedStatus.Status)
		assert.Equal(t, "http://preview.example.com/done-via-netlify-id", verifiedStatus.PreviewLink)
		currentProjectStatus = verifiedStatus // 更新 currentProjectStatus 以便后续测试
	})

	t.Run("UpdateByNetlifySiteID_NonExistentNetlifyID", func(t *testing.T) {
		nonExistentNetlifyID := "netlify-id-does-not-exist-for-update-" + uuid.NewString()
		updates := map[string]any{"status": "some_status"}
		status, err := projectStatusRepo.UpdateByNetlifySiteID(ctx, nonExistentNetlifyID, updates)
		assert.Error(t, err, "UpdateByNetlifySiteID should return an error for non-existent NetlifySiteID")
		assert.Nil(t, status, "Returned status should be nil on error")
		t.Logf("Correctly failed to update status for non-existent NetlifySiteID: %s, Error: %v", nonExistentNetlifyID, err)
	})

	t.Run("UpdateByNetlifySiteID_NoProjectStatusRecord", func(t *testing.T) {
		require.NotEmpty(t, anotherNetlifySiteID, "anotherNetlifySiteID must be set for this test")
		// projectForNoStatusTest 存在且有 anotherNetlifySiteID, 但没有对应的 project_status 记录
		updates := map[string]any{"status": "trying_to_update_non_existent"}
		status, err := projectStatusRepo.UpdateByNetlifySiteID(ctx, anotherNetlifySiteID, updates)
		assert.Error(t, err, "UpdateByNetlifySiteID should return an error if project_status record does not exist")
		assert.Nil(t, status, "Returned status should be nil on error")
		t.Logf("Correctly failed to update status for NetlifySiteID %s (project_status record missing), Error: %v", anotherNetlifySiteID, err)
	})

	t.Run("UpdateByNetlifySiteID_EmptyNetlifyID", func(t *testing.T) {
		updates := map[string]any{"status": "some_status"}
		status, err := projectStatusRepo.UpdateByNetlifySiteID(ctx, "", updates)
		assert.Error(t, err, "UpdateByNetlifySiteID should return an error for empty NetlifySiteID")
		assert.Nil(t, status, "Returned status should be nil on error")
		t.Logf("Correctly failed to update status for empty NetlifySiteID, Error: %v", err)
	})

	t.Run("UpdateByNetlifySiteID_EmptyUpdates", func(t *testing.T) {
		updates := map[string]any{}
		status, err := projectStatusRepo.UpdateByNetlifySiteID(ctx, uniqueNetlifySiteID, updates)
		assert.Error(t, err, "UpdateByNetlifySiteID should return an error for empty updates map")
		assert.Nil(t, status, "Returned status should be nil on error")
		t.Logf("Correctly failed to update status for NetlifySiteID %s with empty updates, Error: %v", uniqueNetlifySiteID, err)
	})

	t.Run("DeleteProjectStatus", func(t *testing.T) {
		// 删除是基于 project_id 的
		err := projectStatusRepo.DeleteByProjectID(ctx, parentProject.ID)
		require.NoError(t, err, "DeleteByProjectID should not return an error")
		t.Logf("Successfully deleted project status for ProjectID: %s", parentProject.ID)

		// 验证是否真的被删除了
		deletedStatus, errGet := projectStatusRepo.GetByProjectID(ctx, parentProject.ID)
		assert.Error(t, errGet, "GetByProjectID after delete should return an error")
		assert.Nil(t, deletedStatus, "GetByProjectID after delete should return nil status")

		// 同时验证通过 NetlifySiteID 也获取不到了
		deletedStatusByNetlifyID, errGetNetlify := projectStatusRepo.GetByNetlifySiteID(ctx, uniqueNetlifySiteID)
		assert.Error(t, errGetNetlify, "GetByNetlifySiteID after delete should also return an error")
		assert.Nil(t, deletedStatusByNetlifyID, "GetByNetlifySiteID after delete should also return nil status")
	})
}

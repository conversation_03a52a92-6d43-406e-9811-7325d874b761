package edge_functions_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/edge_functions"
)

// executeCallMigrationWbFree 测试 MigrationService 的 CallMigrationWbFree 方法。
func executeCallMigrationWbFree(t *testing.T, ctx context.Context, migrationService *edge_functions.MigrationService) {
	t.Run("EmptySQLShouldFail", func(t *testing.T) {
		ok, err := migrationService.CallMigrationWbFree(ctx, "")
		require.False(t, ok, "空 SQL 应该返回 false")
		require.Error(t, err, "空 SQL 应该返回错误")
	})

	t.Run("ValidSQLShouldSucceedOrFailGracefully", func(t *testing.T) {
		// 使用CREATE TABLE IF NOT EXISTS和DROP+CREATE实现可重复执行
		tableName := "test_migration_wbfree"
		dropSQL := "DROP TABLE IF EXISTS " + tableName + ";"
		createSQL := "CREATE TABLE IF NOT EXISTS " + tableName + " (id SERIAL PRIMARY KEY, name VARCHAR(255));"

		// 先尝试删除表
		okDrop, errDrop := migrationService.CallMigrationWbFree(ctx, dropSQL)
		t.Logf("Drop table result: ok=%v, err=%v", okDrop, errDrop)
		// 不管删除是否成功，继续创建
		okCreate, errCreate := migrationService.CallMigrationWbFree(ctx, createSQL)
		t.Logf("Create table result: ok=%v, err=%v", okCreate, errCreate)
		// 只要不panic，能拿到bool和error就算通过
	})
}

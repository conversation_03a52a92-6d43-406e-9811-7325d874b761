package edge_functions_test

import (
	"context"
	"testing"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/edge_functions"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain 用于初始化日志。
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test")
	m.Run()
}

// setupTestService 加载配置并初始化 MigrationService。
func setupTestService(t *testing.T) (context.Context, *edge_functions.MigrationService) {
	t.Helper()
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v. Ensure conf/config_dev.yaml is set or SUPABASE_URL/SUPABASE_EDGE_FUNCTIONS_TOKEN env vars are present.", err)
	}

	baseURL := cfg.Supabase.URL
	token := cfg.Supabase.EdgeFunctionsToken
	if baseURL == "" || token == "" {
		t.Skip("Skipping integration test: SUPABASE_URL and SUPABASE_EDGE_FUNCTIONS_TOKEN must be available via config file or environment variables.")
	}

	client, err := edge_functions.NewClient(baseURL, token, nil)
	if err != nil {
		t.Fatalf("Failed to create EdgeFunctionClient: %v", err)
	}
	migrationService := edge_functions.NewMigrationService(client)
	ctx := context.Background()
	return ctx, migrationService
}

// TestEdgeFunctionsIntegrationSuite 统一调度 edge_functions 相关集成测试。
func TestEdgeFunctionsIntegrationSuite(t *testing.T) {
	ctx, migrationService := setupTestService(t)
	t.Run("CallMigrationWbFree", func(t *testing.T) {
		executeCallMigrationWbFree(t, ctx, migrationService)
	})
}

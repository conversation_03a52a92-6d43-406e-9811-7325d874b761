package github_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	// gh 作为 go-github 的别名
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	// 引入配置包，供 setupTestService 使用
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// executeGetBranch tests GetBranch method.
// It uses a temporary repository created by createTempRepo.
func executeGetBranch(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	// 1. Create a temporary repository (auto-initialized with a main branch)
	tempRepo := createTempRepo(t, ctx, ghService, owner) // createTempRepo is in repository_test.go
	repoName := tempRepo.GetName()
	defaultBranchName := "main" // For AutoInit repos, GitHub usually creates "main"

	// 2. Attempt to get this default branch information
	time.Sleep(2 * time.Second) // Brief delay for GitHub API consistency

	branch, resp, err := ghService.Branches.GetBranch(ctx, owner, repoName, defaultBranchName, 1 /* maxRedirects */)

	// 3. Validate the results
	require.NoError(t, err, fmt.Sprintf("Error getting branch '%s' for repo %s/%s", defaultBranchName, owner, repoName))
	require.NotNil(t, resp, "Response from GetBranch should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK for GetBranch")

	require.NotNil(t, branch, "Branch object should not be nil")
	assert.Equal(t, defaultBranchName, branch.GetName(), "Branch name does not match")
	// 可以根据需要添加更多断言，例如 branch.GetProtected()

	t.Logf("Successfully fetched branch '%s' for repo %s/%s", branch.GetName(), owner, repoName)

	// Attempt to get a non-existent branch
	nonExistentBranchName := "non-existent-branch-" + fmt.Sprintf("%d", time.Now().UnixNano())
	_, nonExistentResp, nonExistentErr := ghService.Branches.GetBranch(ctx, owner, repoName, nonExistentBranchName, 1)

	require.Error(t, nonExistentErr, "Expected an error when trying to get a non-existent branch")
	require.NotNil(t, nonExistentResp, "Response from getting non-existent branch should not be nil")
	// 对于不存在的资源，GitHub 通常返回 404 Not Found
	assert.Equal(t, http.StatusNotFound, nonExistentResp.StatusCode, "Expected status code 404 Not Found for non-existent branch")
	t.Logf("Correctly failed to fetch non-existent branch '%s' with status %d", nonExistentBranchName, nonExistentResp.StatusCode)

	// tempRepo 的清理由 createTempRepo 中的 t.Cleanup 处理
}

// 注意：setupTestService 和 createTempRepo 辅助函数定义在 repository_test.go 文件中。
// 如果此文件需要独立运行或为了更清晰的组织，可以将这些通用辅助函数提取到共享的 test helper 文件中。
// 但在同一个包 (github_test) 下，它们可以直接被使用。

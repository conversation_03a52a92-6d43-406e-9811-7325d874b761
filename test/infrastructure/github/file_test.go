package github_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// executeFetchFileContents 测试 FetchFileContents 方法
// 创建真实的测试仓库和文件，然后使用 GraphQL API 查询文件内容
func executeFetchFileContents(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	// 创建临时仓库
	tempRepo := createTempRepo(t, ctx, ghService, owner)
	repoName := tempRepo.GetName()

	// 等待 GitHub API 状态一致
	time.Sleep(2 * time.Second)

	// 获取主分支
	defaultBranchName := "main"
	branch, _, err := ghService.Branches.GetBranch(ctx, owner, repoName, defaultBranchName, 1)
	require.NoError(t, err, "Failed to get main branch for temp repo")
	require.NotNil(t, branch, "Branch object should not be nil")
	require.NotNil(t, branch.GetCommit(), "Branch commit should not be nil")
	baseTreeSHA := branch.GetCommit().GetCommit().GetTree().GetSHA()
	t.Logf("Using baseTreeSHA: %s", baseTreeSHA)

	// 创建测试文件
	testFileName1 := "test-file1.txt"
	testFileContent1 := "Hello from GraphQL test file 1!"
	testFileName2 := "test-file2.txt"
	testFileContent2 := "Hello from GraphQL test file 2!"

	treeEntries := []*gh.TreeEntry{
		{
			Path:    gh.Ptr(testFileName1),
			Type:    gh.Ptr("blob"),
			Mode:    gh.Ptr("100644"),
			Content: gh.Ptr(testFileContent1),
		},
		{
			Path:    gh.Ptr(testFileName2),
			Type:    gh.Ptr("blob"),
			Mode:    gh.Ptr("100644"),
			Content: gh.Ptr(testFileContent2),
		},
	}

	// 创建包含测试文件的树
	createdTree, resp, err := ghService.Trees.CreateTree(ctx, owner, repoName, baseTreeSHA, treeEntries)
	require.NoError(t, err, "Error creating tree")
	require.NotNil(t, resp, "Response from CreateTree should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for CreateTree")
	require.NotNil(t, createdTree, "Created tree object should not be nil")
	t.Logf("Successfully created new tree with SHA: %s", createdTree.GetSHA())

	// 获取测试文件的 SHA
	var testFileSHA1, testFileSHA2 string
	for _, entry := range createdTree.Entries {
		if entry.GetPath() == testFileName1 {
			testFileSHA1 = entry.GetSHA()
		} else if entry.GetPath() == testFileName2 {
			testFileSHA2 = entry.GetSHA()
		}
	}
	require.NotEmpty(t, testFileSHA1, "Test file SHA should not be empty")
	require.NotEmpty(t, testFileSHA2, "Test file SHA should not be empty")
	t.Logf("Test file SHA1: %s", testFileSHA1)
	t.Logf("Test file SHA2: %s", testFileSHA2)

	// 创建提交
	commitMessage := fmt.Sprintf("Add test files for GraphQL test - %d", time.Now().UnixNano())
	commit := &gh.Commit{
		Message: gh.Ptr(commitMessage),
		Tree:    &gh.Tree{SHA: gh.Ptr(createdTree.GetSHA())},
		Parents: []*gh.Commit{{SHA: gh.Ptr(branch.GetCommit().GetSHA())}},
	}

	createdCommit, resp, err := ghService.Commits.CreateCommit(ctx, owner, repoName, commit, nil)
	require.NoError(t, err, "Error creating commit")
	require.NotNil(t, resp, "Response from CreateCommit should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for CreateCommit")
	require.NotNil(t, createdCommit, "Created commit should not be nil")
	t.Logf("Successfully created commit with SHA: %s", createdCommit.GetSHA())

	// 更新主分支引用
	forceUpdate := false
	_, resp, err = ghService.References.UpdateReference(ctx, owner, repoName, "refs/heads/main", createdCommit.GetSHA(), forceUpdate)
	require.NoError(t, err, "Error updating reference")
	require.NotNil(t, resp, "Response from UpdateReference should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK for UpdateReference")
	t.Logf("Successfully updated main branch to point to new commit")

	// 等待 GitHub API 状态一致
	time.Sleep(3 * time.Second)

	// 获取 GraphQL 客户端
	graphqlClient := ghService.GetGraphQLClient()
	require.NotNil(t, graphqlClient, "GraphQL 客户端不应该为 nil")

	// 准备文件信息
	files := []actualGitHub.FileInfo{
		{Path: testFileName1, Sha: testFileSHA1},
		{Path: testFileName2, Sha: testFileSHA2},
	}

	// 使用 GraphQL API 批量查询文件内容
	result, err := ghService.Files.FetchFileContents(ctx, owner, repoName, files, 10)
	require.NoError(t, err, "FetchFileContents 应该成功执行")
	require.NotNil(t, result, "结果不应该为 nil")
	assert.Len(t, result, len(files), "返回的文件数量应该匹配输入")

	// 验证结果
	if len(result) > 0 {
		file := result[0]
		assert.Equal(t, testFileName1, file.Name, "文件名应该匹配")
		assert.False(t, file.Binary, "文件不应该是二进制文件")
		assert.Equal(t, testFileContent1, file.Contents, "文件内容应该匹配")
		t.Logf("成功通过 GraphQL API 查询到文件内容: %s", file.Contents)
	}

	if len(result) > 1 {
		file := result[1]
		assert.Equal(t, testFileName2, file.Name, "文件名应该匹配")
		assert.False(t, file.Binary, "文件不应该是二进制文件")
		assert.Equal(t, testFileContent2, file.Contents, "文件内容应该匹配")
		t.Logf("成功通过 GraphQL API 查询到文件内容: %s", file.Contents)
	}

	t.Logf("成功批量查询了 %d 个文件的内容", len(result))
}

// executeBuildGraphQLQuery 测试 BuildGraphQLQuery 方法
func executeBuildGraphQLQuery(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	// 测试查询构建
	files := []actualGitHub.FileInfo{
		{Path: "file1.txt", Sha: "sha1"},
		{Path: "file2.txt", Sha: "sha2"},
	}

	query := actualGitHub.BuildGraphQLQuery(files)

	// 验证查询包含必要的字段
	assert.Contains(t, query, "query GetFileContents", "查询应该包含正确的查询名称")
	assert.Contains(t, query, "$owner: String!", "查询应该包含 owner 变量声明")
	assert.Contains(t, query, "$repo: String!", "查询应该包含 repo 变量声明")
	assert.Contains(t, query, "$sha0: ID!", "查询应该包含第一个 SHA 变量声明")
	assert.Contains(t, query, "$sha1: ID!", "查询应该包含第二个 SHA 变量声明")
	assert.Contains(t, query, "file0: object(oid: $sha0)", "查询应该包含第一个文件字段")
	assert.Contains(t, query, "file1: object(oid: $sha1)", "查询应该包含第二个文件字段")

	t.Logf("成功构建 GraphQL 查询: %s", query)
}

// executeBuildVariables 测试 BuildVariables 方法
func executeBuildVariables(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	// 测试变量构建
	files := []actualGitHub.FileInfo{
		{Path: "file1.txt", Sha: "sha1"},
		{Path: "file2.txt", Sha: "sha2"},
	}

	variables := actualGitHub.BuildVariables(files)

	// 验证变量声明
	assert.Contains(t, variables, "$sha0: ID!", "变量声明应该包含第一个 SHA")
	assert.Contains(t, variables, "$sha1: ID!", "变量声明应该包含第二个 SHA")

	t.Logf("成功构建变量声明: %s", variables)
}

// executeGraphQLClientInitialization 测试 GraphQL 客户端初始化
func executeGraphQLClientInitialization(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	// 测试 GraphQL 客户端是否正确初始化
	graphqlClient := ghService.GetGraphQLClient()
	require.NotNil(t, graphqlClient, "GraphQL 客户端不应该为 nil")

	t.Logf("GraphQL 客户端初始化成功")
}

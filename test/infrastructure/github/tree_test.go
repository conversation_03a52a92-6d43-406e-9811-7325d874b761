package github_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// executeCreateTree tests CreateTree method.
func executeCreateTree(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner)
	repoName := tempRepo.GetName()
	defaultBranchName := "main"

	time.Sleep(2 * time.Second) // Ensure GitHub API state consistency
	branch, _, err := ghService.Branches.GetBranch(ctx, owner, repoName, defaultBranchName, 1)
	require.NoError(t, err, "Failed to get main branch for temp repo")
	require.NotNil(t, branch, "Branch object should not be nil")
	require.NotNil(t, branch.GetCommit(), "Branch commit should not be nil")
	baseTreeSHA := branch.GetCommit().GetCommit().GetTree().GetSHA()
	t.Logf("Using baseTreeSHA: %s", baseTreeSHA)

	newFileName := "new-file-in-tree.txt"
	newFileContent := "Hello from the new tree!"
	treeEntries := []*gh.TreeEntry{
		{
			Path:    gh.Ptr(newFileName),
			Type:    gh.Ptr("blob"),
			Mode:    gh.Ptr("100644"),
			Content: gh.Ptr(newFileContent),
		},
	}

	createdTree, resp, err := ghService.Trees.CreateTree(ctx, owner, repoName, baseTreeSHA, treeEntries)

	require.NoError(t, err, "Error creating tree")
	require.NotNil(t, resp, "Response from CreateTree should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for CreateTree")
	require.NotNil(t, createdTree, "Created tree object should not be nil")
	assert.NotEmpty(t, createdTree.GetSHA(), "Created tree SHA should not be empty")
	t.Logf("Successfully created new tree with SHA: %s", createdTree.GetSHA())

	foundNewFile := false
	for _, entry := range createdTree.Entries {
		if entry.GetPath() == newFileName {
			foundNewFile = true
			assert.Equal(t, "blob", entry.GetType(), "Type of the new file entry should be blob")
			assert.NotEmpty(t, entry.GetSHA(), "SHA of the new file entry in the tree should not be empty")
			break
		}
	}
	assert.True(t, foundNewFile, fmt.Sprintf("Newly created file '%s' not found in the created tree's entries", newFileName))
}

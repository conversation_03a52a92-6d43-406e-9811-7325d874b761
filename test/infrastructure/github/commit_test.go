package github_test

import (
	"context"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// executeGetCommit tests GetCommit method.
func executeGetCommit(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner) // AutoInit=true, so there's an initial commit
	repoName := tempRepo.GetName()

	commits, _, err := ghService.Repositories.ListRepositoryCommits(ctx, owner, repoName, &gh.CommitsListOptions{
		ListOptions: gh.ListOptions{PerPage: 1},
	})
	require.NoError(t, err, "Failed to list commits to get initial commit SHA")
	require.NotEmpty(t, commits, "Commit list should not be empty for an auto-initialized repo")
	initialCommitSHA := commits[0].GetSHA()
	require.NotEmpty(t, initialCommitSHA, "Initial commit SHA should not be empty")
	t.Logf("Using initial commit SHA for GetCommit test: %s", initialCommitSHA)

	fetchedCommit, resp, err := ghService.Commits.GetCommit(ctx, owner, repoName, initialCommitSHA, nil)

	require.NoError(t, err, "Error getting commit")
	require.NotNil(t, resp, "Response from GetCommit should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK for GetCommit")
	require.NotNil(t, fetchedCommit, "Fetched commit object should not be nil")
	assert.Equal(t, initialCommitSHA, fetchedCommit.GetSHA(), "Fetched commit SHA does not match requested SHA")
	assert.Contains(t, fetchedCommit.GetCommit().GetMessage(), "Initial commit", "Commit message should be for the initial commit")
	t.Logf("Successfully fetched commit %s", fetchedCommit.GetSHA())
}

// executeCreateCommit tests CreateCommit method.
func executeCreateCommit(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner)
	repoName := tempRepo.GetName()

	commits, _, err := ghService.Repositories.ListRepositoryCommits(ctx, owner, repoName, &gh.CommitsListOptions{
		ListOptions: gh.ListOptions{PerPage: 1},
	})
	require.NoError(t, err)
	require.NotEmpty(t, commits)
	parentCommit := commits[0]
	parentCommitSHA := parentCommit.GetSHA()
	baseTreeSHA := parentCommit.GetCommit().GetTree().GetSHA()
	require.NotEmpty(t, parentCommitSHA)
	require.NotEmpty(t, baseTreeSHA)
	t.Logf("Parent commit SHA: %s, Base tree SHA: %s", parentCommitSHA, baseTreeSHA)

	commitMessage := "Test commit created via API - " + time.Now().Format(time.RFC3339)
	newCommitData := &gh.Commit{
		Message: gh.Ptr(commitMessage),
		Tree:    &gh.Tree{SHA: gh.Ptr(baseTreeSHA)},
		Parents: []*gh.Commit{{SHA: gh.Ptr(parentCommitSHA)}},
		Author: &gh.CommitAuthor{
			Name:  gh.Ptr("Test Author"),
			Email: gh.Ptr("<EMAIL>"),
			Date:  &gh.Timestamp{Time: time.Now()},
		},
	}

	createdCommit, resp, err := ghService.Commits.CreateCommit(ctx, owner, repoName, newCommitData, nil)

	require.NoError(t, err, "Error creating commit")
	require.NotNil(t, resp, "Response from CreateCommit should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for CreateCommit")
	require.NotNil(t, createdCommit, "Created commit object should not be nil")
	assert.NotEmpty(t, createdCommit.GetSHA(), "Created commit SHA should not be empty")
	assert.Equal(t, commitMessage, createdCommit.GetMessage(), "Commit message does not match")
	assert.Equal(t, baseTreeSHA, createdCommit.GetTree().GetSHA(), "Commit tree SHA does not match")
	require.Len(t, createdCommit.Parents, 1, "Expected one parent commit")
	assert.Equal(t, parentCommitSHA, createdCommit.Parents[0].GetSHA(), "Parent commit SHA does not match")
	t.Logf("Successfully created commit %s with message: %s", createdCommit.GetSHA(), createdCommit.GetMessage())
}

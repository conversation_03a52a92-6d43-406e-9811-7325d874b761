package github_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain is executed before any other tests in this package.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test") // Or your preferred default log level for tests
	m.Run()
}

// setupTestService is a helper function to load config and initialize GitHub Service.
// It skips test if essential config (token/owner) is missing.
func setupTestService(t *testing.T) (context.Context, *actualGitHub.Service, string, string) {
	t.Helper()
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v. Ensure conf/config_dev.yaml is set or GITHUB_TOKEN/GITHUB_OWNER env vars are present.", err)
	}

	token := cfg.GitHub.Token
	owner := cfg.GitHub.Owner

	if token == "" || owner == "" {
		t.Skip("Skipping integration test: GITHUB_TOKEN and GITHUB_OWNER must be available via config file or environment variables.")
	}

	ghService := actualGitHub.NewService(token)
	ctx := context.Background()
	return ctx, ghService, owner, token
}

// createTempRepo creates a temporary unique repository and registers a cleanup to delete it.
func createTempRepo(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) *gh.Repository {
	t.Helper()
	repoName := fmt.Sprintf("test-temp-repo-%d", time.Now().UnixNano())
	repoToCreate := &gh.Repository{
		Name:     gh.Ptr(repoName),
		Private:  gh.Ptr(true),
		AutoInit: gh.Ptr(true),
	}

	createdRepo, resp, err := ghService.Repositories.CreateUserRepository(ctx, repoToCreate)
	require.NoError(t, err, "Failed to create temporary repository")
	require.NotNil(t, resp, "Response from creating temp repo should not be nil")
	require.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status 201 for temp repo creation")
	require.NotNil(t, createdRepo, "Created temporary repository should not be nil")
	t.Logf("Successfully created temporary repository %s/%s", owner, createdRepo.GetName())

	t.Cleanup(func() {
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, createdRepo.GetName())
		if delErr != nil {
			if ghErr, ok := delErr.(*gh.ErrorResponse); ok && ghErr.Response != nil && ghErr.Response.StatusCode == http.StatusNotFound {
				t.Logf("Temporary repository %s/%s was already deleted or not found.", owner, createdRepo.GetName())
			} else {
				t.Logf("Warning: failed to delete temporary repository %s/%s: %v", owner, createdRepo.GetName(), delErr)
			}
		} else {
			t.Logf("Successfully deleted temporary repository %s/%s", owner, createdRepo.GetName())
		}
	})

	return createdRepo
}

// TestGitHubIntegrationSuite is the main entry point for running ordered GitHub integration tests.
func TestGitHubIntegrationSuite(t *testing.T) {
	ctx, ghService, owner, _ := setupTestService(t) // The token is also available if needed

	// Repository Lifecycle Tests
	t.Run("CreateRepository", func(t *testing.T) {
		executeCreateRepository(t, ctx, ghService, owner)
	})
	t.Run("UpdateRepository", func(t *testing.T) {
		// This test uses its own temp repo, so it's self-contained after initial setup
		executeUpdateRepository(t, ctx, ghService, owner)
	})
	t.Run("CreateRepositoryFromTemplate", func(t *testing.T) {
		executeCreateRepositoryFromTemplate(t, ctx, ghService, owner)
	})

	// Tests depending on a repository (often using createTempRepo internally)
	t.Run("ListRepositoryCommits", func(t *testing.T) {
		executeListRepositoryCommits(t, ctx, ghService, owner)
	})
	t.Run("AddAndDeleteDeployKey", func(t *testing.T) {
		executeAddAndDeleteDeployKey(t, ctx, ghService, owner)
	})

	// Branch tests
	t.Run("GetBranch", func(t *testing.T) {
		executeGetBranch(t, ctx, ghService, owner)
	})

	// Commit tests
	t.Run("GetCommit", func(t *testing.T) {
		executeGetCommit(t, ctx, ghService, owner)
	})
	t.Run("CreateCommit", func(t *testing.T) {
		executeCreateCommit(t, ctx, ghService, owner)
	})

	// Reference tests
	t.Run("UpdateReference", func(t *testing.T) {
		executeUpdateReference(t, ctx, ghService, owner)
	})
	t.Run("GetReference", func(t *testing.T) {
		executeGetReference(t, ctx, ghService, owner)
	})

	// Tree tests
	t.Run("CreateTree", func(t *testing.T) {
		executeCreateTree(t, ctx, ghService, owner)
	})

	// GraphQL tests
	t.Run("GraphQLClientInitialization", func(t *testing.T) {
		executeGraphQLClientInitialization(t, ctx, ghService, owner)
	})
	t.Run("BuildGraphQLQuery", func(t *testing.T) {
		executeBuildGraphQLQuery(t, ctx, ghService, owner)
	})
	t.Run("BuildVariables", func(t *testing.T) {
		executeBuildVariables(t, ctx, ghService, owner)
	})
	t.Run("FetchFileContents", func(t *testing.T) {
		executeFetchFileContents(t, ctx, ghService, owner)
	})

	// Add more tests here if needed, and they will run in the defined order
}

package github_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github" // gh 作为 go-github 的别名
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	// Config, actualGitHub, and logger are used by functions now in main_test.go
	// or by the execute... functions themselves.
)

// executeCreateRepository tests CreateRepository method.
func executeCreateRepository(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	repoName := fmt.Sprintf("test-repo-%d", time.Now().UnixNano())
	description := "Temporary repository for integration testing"
	private := true

	repoToCreate := &gh.Repository{
		Name:        gh.Ptr(repoName),
		Description: gh.Ptr(description),
		Private:     gh.Ptr(private),
		AutoInit:    gh.Ptr(true),
	}

	t.Cleanup(func() {
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, repoName)
		if delErr != nil {
			if ghErr, ok := delErr.(*gh.ErrorResponse); ok && ghErr.Response != nil && ghErr.Response.StatusCode == http.StatusNotFound {
				t.Logf("Repository %s/%s for executeCreateRepository was already deleted or not found.", owner, repoName)
			} else {
				t.Logf("Warning: failed to delete repository %s/%s in executeCreateRepository: %v", owner, repoName, delErr)
			}
		} else {
			t.Logf("Successfully deleted repository %s/%s in executeCreateRepository", owner, repoName)
		}
	})

	createdRepo, resp, createErr := ghService.Repositories.CreateUserRepository(ctx, repoToCreate)

	require.NoError(t, createErr, "Error creating repository")
	require.NotNil(t, resp, "Response should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created")

	require.NotNil(t, createdRepo, "Created repository should not be nil")
	assert.Equal(t, repoName, createdRepo.GetName(), "Repository name does not match")
	assert.Equal(t, description, createdRepo.GetDescription(), "Repository description does not match")
	assert.Equal(t, private, createdRepo.GetPrivate(), "Repository privacy status does not match")
	t.Logf("Successfully created repository %s/%s", owner, createdRepo.GetName())

	fetchedRepo, _, getErr := ghService.Repositories.GetRepository(ctx, owner, repoName)
	require.NoError(t, getErr, "Error fetching repository after creation")
	require.NotNil(t, fetchedRepo, "Fetched repository should not be nil")
	assert.Equal(t, repoName, fetchedRepo.GetName(), "Fetched repository name does not match")
}

// executeUpdateRepository tests UpdateRepository method.
func executeUpdateRepository(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner) // Uses createTempRepo from main_test.go
	repoName := tempRepo.GetName()

	originalDescription := tempRepo.GetDescription()
	newDescription := fmt.Sprintf("Updated repository description - %d", time.Now().UnixNano())
	private := true

	repoUpdate := &gh.Repository{
		Description: gh.Ptr(newDescription),
		Private:     gh.Ptr(private),
	}

	updatedRepo, resp, err := ghService.Repositories.UpdateRepository(ctx, owner, repoName, repoUpdate)
	require.NoError(t, err, "Error updating repository")
	require.NotNil(t, resp, "Response from updating repository should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK for UpdateRepository")
	require.NotNil(t, updatedRepo, "Updated repository should not be nil")

	assert.Equal(t, repoName, updatedRepo.GetName(), "Repository name should not change on update")
	assert.Equal(t, newDescription, updatedRepo.GetDescription(), "Repository description was not updated")
	assert.Equal(t, private, updatedRepo.GetPrivate(), "Repository privacy status was not updated as expected")
	assert.NotEqual(t, originalDescription, updatedRepo.GetDescription(), "Description should have changed")

	t.Logf("Successfully updated repository %s/%s. New description: '%s'", owner, repoName, updatedRepo.GetDescription())
}

// executeCreateRepositoryFromTemplate tests CreateRepositoryFromTemplate method.
func executeCreateRepositoryFromTemplate(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	templateOwner := "github-learning-lab"
	templateRepoName := "github-slideshow"

	newRepoName := fmt.Sprintf("test-repo-from-template-%d", time.Now().UnixNano())
	newRepoDescription := "Test repository created from a template"
	includeAllBranches := false
	private := true

	templateRepoRequest := &gh.TemplateRepoRequest{
		Name:               gh.Ptr(newRepoName),
		Owner:              gh.Ptr(owner),
		Description:        gh.Ptr(newRepoDescription),
		IncludeAllBranches: gh.Ptr(includeAllBranches),
		Private:            gh.Ptr(private),
	}

	createdRepo, resp, err := ghService.Repositories.CreateRepositoryFromTemplate(ctx, templateOwner, templateRepoName, templateRepoRequest)

	if err != nil {
		if ghErr, ok := err.(*gh.ErrorResponse); ok && ghErr.Response != nil {
			t.Logf("GitHub API Error when creating from template: Status: %s, Message: %s", ghErr.Response.Status, ghErr.Message)
			if ghErr.Response.StatusCode == http.StatusNotFound {
				t.Skipf("Skipping test: Template repository %s/%s not found or not accessible.", templateOwner, templateRepoName)
			}
			if ghErr.Response.StatusCode == http.StatusUnprocessableEntity {
				t.Errorf("GitHub API Error 422 (Unprocessable Entity) creating from template. Request: %+v, Error: %v", templateRepoRequest, err)
			}
		}
		require.NoError(t, err, "Error creating repository from template")
	}

	require.NotNil(t, resp, "Response from creating repository from template should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for CreateRepositoryFromTemplate")
	require.NotNil(t, createdRepo, "Created repository from template should not be nil")

	assert.Equal(t, newRepoName, createdRepo.GetName(), "Repository name does not match")
	assert.Equal(t, newRepoDescription, createdRepo.GetDescription(), "Repository description does not match")
	assert.Equal(t, owner, createdRepo.GetOwner().GetLogin(), "Repository owner does not match")
	assert.Equal(t, private, createdRepo.GetPrivate(), "Repository privacy does not match")

	t.Logf("Successfully created repository %s/%s from template %s/%s", owner, newRepoName, templateOwner, templateRepoName)

	t.Cleanup(func() {
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, newRepoName)
		if delErr != nil {
			t.Logf("Warning: failed to delete repository %s/%s (created from template): %v", owner, newRepoName, delErr)
		} else {
			t.Logf("Successfully deleted repository %s/%s (created from template)", owner, newRepoName)
		}
	})
}

// executeAddAndDeleteDeployKey tests AddDeployKey and DeleteDeployKey methods.
func executeAddAndDeleteDeployKey(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner) // Uses createTempRepo from main_test.go
	repoName := tempRepo.GetName()

	keyTitle := fmt.Sprintf("test-deploy-key-%d", time.Now().UnixNano())
	publicKeyContent := "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC/tJ8UgOMiBU/c58qrk35PqNzjkxIEUrZRU43kFWYzdEDa5lgdampT5eoDShy+jlw5M83hDSV5W8AgwaLrw/skQnCSToky282PfqkjAtoX7nw6BQhBFS2j6aoOhKLWpCIDyDOn5evv+XM7x/tzOX6JNiz2DHZYFR9Il1+FmsqJRLikBc0X7+fmjaqfSFbtmbydPlZz/oEy6RQDHlpoFvSftW4GSKatZLF2NGwC3W5TzzJsrZMY/wGR/0ZLpmHgUw5srgCBY1zwrl2ykZA1XJKHPeYnUrck7N1xJT5lmjh33ZSG6BdHNmmeL05K0DKYypOERjw7ptS+UEnIuI5/Cg0N ssh-rsa-20250519151909"
	readOnly := true

	deployKey := &gh.Key{
		Title:    gh.Ptr(keyTitle),
		Key:      gh.Ptr(publicKeyContent),
		ReadOnly: gh.Ptr(readOnly),
	}

	createdKey, resp, err := ghService.Repositories.AddDeployKey(ctx, owner, repoName, deployKey)
	require.NoError(t, err, "Error adding deploy key")
	require.NotNil(t, resp, "Response from adding deploy key should not be nil")
	assert.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status code 201 Created for AddDeployKey")
	require.NotNil(t, createdKey, "Created deploy key should not be nil")
	assert.Equal(t, keyTitle, createdKey.GetTitle(), "Deploy key title does not match")
	assert.Equal(t, readOnly, createdKey.GetReadOnly(), "Deploy key read-only status does not match")
	t.Logf("Successfully added deploy key '%s' (ID: %d) to repo %s/%s", createdKey.GetTitle(), createdKey.GetID(), owner, repoName)

	t.Cleanup(func() {
		deleteResp, delKeyErr := ghService.Repositories.DeleteDeployKey(ctx, owner, repoName, createdKey.GetID())
		if delKeyErr != nil {
			t.Logf("Warning: failed to delete deploy key ID %d from repo %s/%s: %v", createdKey.GetID(), owner, repoName, delKeyErr)
		} else {
			assert.Equal(t, http.StatusNoContent, deleteResp.StatusCode, "Expected status code 204 No Content for DeleteKey")
			t.Logf("Successfully deleted deploy key ID %d from repo %s/%s", createdKey.GetID(), owner, repoName)
		}
	})
}

// executeListRepositoryCommits tests ListRepositoryCommits method.
func executeListRepositoryCommits(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner) // AutoInit=true and uses createTempRepo from main_test.go
	repoName := tempRepo.GetName()

	commits, listCommitsResp, err := ghService.Repositories.ListRepositoryCommits(ctx, owner, repoName, nil)
	require.NoError(t, err, "Error listing repository commits")
	require.NotNil(t, listCommitsResp, "Response from listing commits should not be nil")
	assert.Equal(t, http.StatusOK, listCommitsResp.StatusCode, "Expected status 200 OK for ListRepositoryCommits")

	assert.Len(t, commits, 1, "Expected exactly one commit for a new repository with AutoInit=true")
	if len(commits) == 1 {
		initialCommit := commits[0]
		assert.Contains(t, initialCommit.GetCommit().GetMessage(), "Initial commit", "Commit message should indicate it is an initial commit")
		t.Logf("Successfully listed 1 initial commit for auto-initialized repo %s/%s.", owner, repoName)
	}
}

// TestGitHubIntegrationSuite, TestMain, setupTestService, createTempRepo have been moved to main_test.go

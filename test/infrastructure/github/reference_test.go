package github_test

import (
	"context"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// executeUpdateReference tests UpdateReference method.
func executeUpdateReference(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner)
	repoName := tempRepo.GetName()
	mainBranchRefPath := "refs/heads/main"

	branch, _, err := ghService.Branches.GetBranch(ctx, owner, repoName, "main", 1)
	require.NoError(t, err, "Failed to get main branch to find parent commit")
	parentCommitSHA := branch.GetCommit().GetSHA()
	baseTreeSHA := branch.GetCommit().GetCommit().GetTree().GetSHA()
	require.NotEmpty(t, parentCommitSHA, "Parent commit SHA is empty")
	require.NotEmpty(t, baseTreeSHA, "Base tree SHA is empty")
	t.Logf("Initial main branch at commit: %s, tree: %s", parentCommitSHA, baseTreeSHA)

	newCommitMessage := "New commit to update reference - " + time.Now().String()
	commitToCreate := &gh.Commit{
		Message: gh.Ptr(newCommitMessage),
		Tree:    &gh.Tree{SHA: gh.Ptr(baseTreeSHA)},
		Parents: []*gh.Commit{{SHA: gh.Ptr(parentCommitSHA)}},
	}
	newGitCommit, _, err := ghService.Commits.CreateCommit(ctx, owner, repoName, commitToCreate, nil)
	require.NoError(t, err, "Failed to create new commit object")
	require.NotNil(t, newGitCommit, "New git commit object is nil")
	newCommitSHA := newGitCommit.GetSHA()
	require.NotEmpty(t, newCommitSHA, "New commit SHA is empty")
	t.Logf("Created new commit object: %s", newCommitSHA)

	forceUpdate := false
	updatedRef, resp, err := ghService.References.UpdateReference(ctx, owner, repoName, mainBranchRefPath, newCommitSHA, forceUpdate)

	require.NoError(t, err, "Error updating reference")
	require.NotNil(t, resp, "Response from UpdateReference should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK for UpdateReference")
	require.NotNil(t, updatedRef, "Updated reference object should not be nil")
	assert.Equal(t, mainBranchRefPath, updatedRef.GetRef(), "Updated ref path does not match")
	require.NotNil(t, updatedRef.GetObject(), "Updated ref object's git object is nil")
	assert.Equal(t, newCommitSHA, updatedRef.GetObject().GetSHA(), "Updated ref SHA does not point to the new commit")
	t.Logf("Successfully updated ref '%s' to point to commit %s", updatedRef.GetRef(), updatedRef.GetObject().GetSHA())

	time.Sleep(5 * time.Second) // Give GitHub a moment for ref update
	mainBranchAfterUpdate, _, err := ghService.Branches.GetBranch(ctx, owner, repoName, "main", 1)
	require.NoError(t, err, "Failed to get main branch after update")
	require.NotNil(t, mainBranchAfterUpdate.GetCommit(), "Branch commit after update is nil")
	assert.Equal(t, newCommitSHA, mainBranchAfterUpdate.GetCommit().GetSHA(), "Main branch HEAD SHA was not updated to the new commit")
	t.Logf("Main branch is now at commit: %s", mainBranchAfterUpdate.GetCommit().GetSHA())
}

// executeGetReference tests GetReference method.
func executeGetReference(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) {
	tempRepo := createTempRepo(t, ctx, ghService, owner)
	repoName := tempRepo.GetName()
	mainBranchRefPath := "refs/heads/main"

	time.Sleep(2 * time.Second) // Wait for repo initialization

	ref, resp, err := ghService.References.GetReference(ctx, owner, repoName, mainBranchRefPath)
	require.NoError(t, err, "Error getting reference")
	require.NotNil(t, resp, "Response from GetReference should not be nil")
	assert.Equal(t, http.StatusOK, resp.StatusCode, "Expected status code 200 OK")
	require.NotNil(t, ref, "Reference object should not be nil")
	assert.Equal(t, mainBranchRefPath, ref.GetRef(), "Reference path does not match")
	require.NotNil(t, ref.GetObject(), "Reference git object is nil")
	assert.NotEmpty(t, ref.GetObject().GetSHA(), "Reference SHA should not be empty")
	t.Logf("Successfully fetched reference '%s' pointing to SHA: %s", ref.GetRef(), ref.GetObject().GetSHA())

	nonExistentRefPath := "refs/heads/non-existent-branch"
	_, nfResp, nfErr := ghService.References.GetReference(ctx, owner, repoName, nonExistentRefPath)
	require.Error(t, nfErr, "Expected error for non-existent reference")
	require.NotNil(t, nfResp, "Response should not be nil for non-existent ref")
	assert.Equal(t, http.StatusNotFound, nfResp.StatusCode, "Expected 404 for non-existent reference")
}

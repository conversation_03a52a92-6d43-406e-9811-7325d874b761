package cloudflare_test

import (
	"context"
	"testing"

	cf "github.com/cloudflare/cloudflare-go/v4"
	"github.com/cloudflare/cloudflare-go/v4/alerting"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
)

// testCreateNotificationPolicy 测试创建通知策略的用例
func testCreateNotificationPolicy(t *testing.T, svc *cloudflare.Service, ctx context.Context) {
	// 准备测试数据
	name := "Test Notification Policy"
	description := "Test notification policy for webhook"
	alertType := alerting.PolicyNewParamsAlertTypePagesEventAlert
	mechanisms := alerting.MechanismParam{
		Webhooks: cf.F([]alerting.MechanismWebhookParam{
			{
				ID: cf.F("fc265172d4c24de48cd3ab292307ae18"),
			},
		}),
	}
	enabled := true

	// 构建 filters
	filters := alerting.PolicyFilterParam{
		ProjectID:   cf.F([]string{"5e8d5c1e-9912-4a62-931c-6c1756a4c107"}),
		Event:       cf.F([]string{"EVENT_DEPLOYMENT_STARTED", "EVENT_DEPLOYMENT_FAILED", "EVENT_DEPLOYMENT_SUCCESS"}),
		Environment: cf.F([]string{"ENVIRONMENT_PRODUCTION", "ENVIRONMENT_PREVIEW"}),
	}

	// 创建策略
	policyID, err := svc.Alerting.CreateNotificationPolicy(ctx, name, description, alertType, mechanisms, enabled, filters)
	require.NoError(t, err)
	assert.NotEmpty(t, policyID)

	// 验证策略是否创建成功
	// TODO: 添加获取策略的方法来验证
}

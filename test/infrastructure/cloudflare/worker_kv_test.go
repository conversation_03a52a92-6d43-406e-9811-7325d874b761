package cloudflare_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
)

// testWorkerKVOperations 测试 Worker KV 的基本操作
func testWorkerKVOperations(t *testing.T, svc *cloudflare.Service, ctx context.Context, cfg *config.Config) {
	// 从配置中获取 namespace ID
	namespaceID := cfg.Cloudflare.KVNamespaceID
	testKey := "test-key11111111"
	testValue := "test-value"
	// testMetadata := map[string]interface{}{
	// 	"author":  "test",
	// 	"version": "1.0",
	// }

	// 跳过测试如果没有配置 namespace ID
	if namespaceID == "" || namespaceID == "your-kv-namespace-id-here" {
		t.Skip("Skipping Worker KV test: KV namespace ID not configured")
	}

	// 测试写入键值对（带元数据）
	t.Run("WriteKeyValuePairWithMetadata", func(t *testing.T) {
		err := svc.WorkerKV.WriteKeyValuePairWithOptionalMetadata(
			ctx,
			namespaceID,
			testKey,
			testValue,
			nil,
		)
		assert.NoError(t, err, "Should be able to write key-value pair with metadata")
	})

	// 测试读取键值对
	t.Run("ReadKeyValuePair", func(t *testing.T) {
		value, err := svc.WorkerKV.ReadKeyValuePair(ctx, namespaceID, testKey)
		require.NoError(t, err, "Should be able to read key-value pair")
		assert.Equal(t, testValue, value, "Retrieved value should match written value")
	})

	// 测试写入键值对（不带元数据）
	t.Run("WriteKeyValuePairWithoutMetadata", func(t *testing.T) {
		testKey2 := "test-key-2"
		testValue2 := "test-value-2"

		err := svc.WorkerKV.WriteKeyValuePairWithOptionalMetadata(
			ctx,
			namespaceID,
			testKey2,
			testValue2,
			nil, // 不提供元数据
		)
		assert.NoError(t, err, "Should be able to write key-value pair without metadata")

		// 验证能够读取
		value, err := svc.WorkerKV.ReadKeyValuePair(ctx, namespaceID, testKey2)
		require.NoError(t, err, "Should be able to read key-value pair")
		assert.Equal(t, testValue2, value, "Retrieved value should match written value")
	})

	// 测试删除键值对
	t.Run("DeleteKeyValuePair", func(t *testing.T) {
		testKey3 := "test-key-to-delete"
		testValue3 := "test-value-to-delete"

		// 先写入一个键值对
		err := svc.WorkerKV.WriteKeyValuePairWithOptionalMetadata(
			ctx,
			namespaceID,
			testKey3,
			testValue3,
			nil,
		)
		require.NoError(t, err, "Should be able to write key-value pair")

		// 验证键值对存在
		value, err := svc.WorkerKV.ReadKeyValuePair(ctx, namespaceID, testKey3)
		require.NoError(t, err, "Should be able to read key-value pair before deletion")
		assert.Equal(t, testValue3, value, "Retrieved value should match written value")

		// 删除键值对
		err = svc.WorkerKV.DeleteKeyValuePair(ctx, namespaceID, testKey3)
		assert.NoError(t, err, "Should be able to delete key-value pair")

		// 验证键值对已被删除（读取应该失败）
		_, err = svc.WorkerKV.ReadKeyValuePair(ctx, namespaceID, testKey3)
		assert.Error(t, err, "Should not be able to read deleted key-value pair")
	})
}

// TestWorkerKVIntegration 是 Worker KV 集成测试的入口
// 注意：这个测试需要真实的 Cloudflare 凭据和 KV namespace
func TestWorkerKVIntegration(t *testing.T) {
	// 只在集成测试环境中运行
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx, svc, cfg := setupTestServiceWithConfig(t)

	// Worker KV 测试
	t.Run("WorkerKVOperations", func(t *testing.T) {
		testWorkerKVOperations(t, svc, ctx, cfg)
	})
}

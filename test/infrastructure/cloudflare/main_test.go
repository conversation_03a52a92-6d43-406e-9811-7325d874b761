package cloudflare_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain is executed before any other tests in this package.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test") // Or your preferred default log level for tests
	m.Run()
}

// setupTestService 创建测试用的服务实例
func setupTestService(t *testing.T) (context.Context, *cloudflare.Service) {
	ctx, svc, _ := setupTestServiceWithConfig(t)
	return ctx, svc
}

// setupTestServiceWithConfig 创建测试用的服务实例并返回配置
func setupTestServiceWithConfig(t *testing.T) (context.Context, *cloudflare.Service, *config.Config) {
	t.Helper()
	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v. Ensure conf/config_dev.yaml is set or CLOUDFLARE_API_KEY/CLOUDFLARE_API_EMAIL/CLOUDFLARE_ACCOUNT_ID env vars are present.", err)
	}

	apiKey := cfg.Cloudflare.APIKey
	apiEmail := cfg.Cloudflare.APIEmail
	accountID := cfg.Cloudflare.AccountID

	if apiKey == "" || apiEmail == "" || accountID == "" {
		t.Skip("Skipping integration test: CLOUDFLARE_API_KEY, CLOUDFLARE_API_EMAIL and CLOUDFLARE_ACCOUNT_ID must be available via config file or environment variables.")
	}

	// 创建服务实例
	svc, err := cloudflare.NewService(apiKey, apiEmail, accountID)
	require.NoError(t, err)
	require.NotNil(t, svc)

	return context.Background(), svc, cfg
}

// TestCloudflareIntegrationSuite 是运行 Cloudflare 集成测试的主入口
func TestCloudflareIntegrationSuite(t *testing.T) {
	ctx, svc, cfg := setupTestServiceWithConfig(t)

	// Alerting 测试
	// t.Run("CreateNotificationPolicy", func(t *testing.T) {
	// 	testCreateNotificationPolicy(t, svc, ctx)
	// })

	// Worker KV 测试
	t.Run("WorkerKVOperations", func(t *testing.T) {
		testWorkerKVOperations(t, svc, ctx, cfg)
	})

	// 可以在这里添加更多测试用例
}

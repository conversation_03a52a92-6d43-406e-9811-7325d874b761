package fly_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// 引入 app_test.go 的导出方法
// ExecuteListAppsTest, ExecuteListAppsWithEmptyOrgSlugTest, ExecuteAppDataStructureTest

// TestMain is executed before any other tests in this package.
// It's a good place for one-time setup, like logger initialization.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test")
	os.Exit(m.Run())
}

// setupFlyTestService loads config and initializes the Fly.io Service.
// Skips test if essential Fly.io config (api_token, org_slug) is missing.
func setupFlyTestService(t *testing.T) (context.Context, *actualFly.Service, *config.Config) {
	t.Helper()
	// Logger is initialized in TestMain, so no need to call logger.InitLogger here repeatedly.

	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping Fly.io integration test: Could not load config for env 'dev': %v.", err)
	}

	apiToken := cfg.Fly.APIToken
	orgSlug := cfg.Fly.OrgSlug
	if apiToken == "" {
		t.Skip("Skipping Fly.io integration test: FLY_API_TOKEN must be available.")
	}
	if orgSlug == "" {
		t.Skip("Skipping Fly.io integration test: FLY_ORG_SLUG must be available.")
	}

	flyService, err := actualFly.NewService(cfg)
	require.NoError(t, err, "Failed to create Fly.io service")
	require.NotNil(t, flyService, "Fly.io service should not be nil")

	ctx := context.Background()
	return ctx, flyService, cfg
}

// TestFlyIntegrationSuite is the main entry point for running ordered Fly.io integration tests.
func TestFlyIntegrationSuite(t *testing.T) {
	ctx, flyService, cfg := setupFlyTestService(t)

	orgSlug := cfg.Fly.OrgSlug
	appName := "test-app-demo-" + fmt.Sprintf("%d", time.Now().UnixNano())

	t.Run("CreateApp", func(t *testing.T) {
		createApp(t, ctx, flyService, orgSlug, appName)
	})

	var machineID string
	t.Run("CreateMachine", func(t *testing.T) {
		machineID = createMachine(t, ctx, flyService, appName)
	})

	t.Run("GetMachine", func(t *testing.T) {
		getMachine(t, ctx, flyService, appName, machineID)
	})

	t.Run("UpdateMachine", func(t *testing.T) {
		updateMachine(t, ctx, flyService, appName, machineID)
	})

	t.Run("ListMachines", func(t *testing.T) {
		listMachines(t, ctx, flyService, appName)
	})

	t.Run("DeleteMachine", func(t *testing.T) {
		deleteMachine(t, ctx, flyService, appName, machineID)
	})

	t.Run("GetApp", func(t *testing.T) {
		getApp(t, ctx, flyService, appName)
	})

	t.Run("CreateAppDeployToken", func(t *testing.T) {
		createAppDeployToken(t, ctx, flyService, appName)
	})

	t.Run("DeleteApp", func(t *testing.T) {
		deleteApp(t, ctx, flyService, appName)
	})

	t.Run("ListApps", func(t *testing.T) {
		ExecuteListAppsTest(t, ctx, flyService)
	})
}

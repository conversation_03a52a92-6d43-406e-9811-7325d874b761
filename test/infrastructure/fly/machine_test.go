package fly_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
)

// createMachine 测试创建 Machine 的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func createMachine(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName string) string {
	req := &actualFly.CreateMachineRequest{
		Config: &actualFly.FlyMachineConfig{
			Image: ptrString("busybox"),
			Guest: &actualFly.FlyMachineGuest{
				CpuKind:  ptrString("shared"),
				Cpus:     ptrInt32(1),
				MemoryMb: ptrInt32(256),
			},
		},
		Name: ptrString("test-machine-demo"),
	}
	machine, err := flyService.Machines.CreateMachine(ctx, appName, req)
	require.NoError(t, err, "CreateMachine should not return an error")
	require.NotNil(t, machine, "CreateMachine should return a non-nil machine")
	require.NotEmpty(t, machine.Id, "Machine ID should not be empty")
	return derefString(machine.Id)
}

// getMachine 测试获取 Machine 详情的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func getMachine(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName, machineID string) {
	machine, err := flyService.Machines.GetMachine(ctx, appName, machineID)
	require.NoError(t, err, "GetMachine should not return an error")
	require.NotNil(t, machine, "GetMachine should return a non-nil machine")
	require.NotEmpty(t, machine.Id, "Machine ID should not be empty")
	require.NotEmpty(t, machine.Name, "Machine name should not be empty")
}

// updateMachine 测试更新 Machine 的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func updateMachine(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName, machineID string) {
	req := &actualFly.UpdateMachineRequest{
		Config: &actualFly.FlyMachineConfig{
			Image: ptrString("busybox"),
			Guest: &actualFly.FlyMachineGuest{
				CpuKind:  ptrString("shared"),
				Cpus:     ptrInt32(1),
				MemoryMb: ptrInt32(512),
			},
		},
	}
	machine, err := flyService.Machines.UpdateMachine(ctx, appName, machineID, req)
	require.NoError(t, err, "UpdateMachine should not return an error")
	require.NotNil(t, machine, "UpdateMachine should return a non-nil machine")
}

// deleteMachine 测试删除 Machine 的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func deleteMachine(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName, machineID string) {
	err := flyService.Machines.DeleteMachine(ctx, appName, machineID, true)
	require.NoError(t, err, "DeleteMachine should not return an error")
}

// listMachines 测试列出 Machine 的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func listMachines(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName string) {
	machines, err := flyService.Machines.ListMachines(ctx, appName)
	require.NoError(t, err, "ListMachines should not return an error")
	require.NotNil(t, machines, "ListMachines should return a non-nil slice")
}

// 工具方法：指针和解引用
func ptrString(s string) *string { return &s }
func ptrInt32(i int32) *int32    { return &i }
func derefString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

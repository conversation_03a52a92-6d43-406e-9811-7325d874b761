package fly_test

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
)

// ExecuteListAppsTest tests the ListApps method of the Fly.io service.
func ExecuteListAppsTest(t *testing.T, ctx context.Context, flyService *actualFly.Service) {
	resp, err := flyService.Apps.ListApps(ctx)
	require.NoError(t, err, "ListApps should not return an error")
	require.NotNil(t, resp, "ListApps should return a non-nil response")

	totalApps := resp.TotalApps
	t.Logf("API返回的total_apps: %d, apps数组长度: %d", totalApps, len(resp.Apps))
	assert.Equal(t, totalApps, len(resp.Apps), "total_apps 字段应等于 apps 数组长度")

	for i, app := range resp.Apps {
		if i >= 3 {
			break
		}
		t.Logf("App %d: ID=%s, Name=%s, MachineCount=%d, Network=%s",
			i+1, app.ID, app.Name, app.MachineCount, string(app.Network))
	}

	for _, app := range resp.Apps {
		assert.NotEmpty(t, app.ID, "App ID should not be empty")
		assert.NotEmpty(t, app.Name, "App name should not be empty")
		assert.GreaterOrEqual(t, app.MachineCount, 0, "App MachineCount should be >= 0")
		if len(app.Network) > 0 {
			assert.True(t, json.Valid(app.Network), "App Network should be valid JSON or null")
		}
	}
}

// createApp 测试创建应用的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func createApp(t *testing.T, ctx context.Context, flyService *actualFly.Service, orgSlug string, appName string) {
	req := &actualFly.CreateAppRequest{
		AppName:          appName, // 由main_test.go传入，确保唯一
		EnableSubdomains: false,
		Network:          "",
	}
	err := flyService.Apps.CreateApp(ctx, req)
	require.NoError(t, err, "CreateApp should not return an error")
}

// getApp 测试获取应用详情的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func getApp(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName string) {
	resp, err := flyService.Apps.GetApp(ctx, appName)
	require.NoError(t, err, "GetApp should not return an error")
	require.NotNil(t, resp, "GetApp should return a non-nil response")
	require.NotEmpty(t, resp.ID, "App ID should not be empty")
	require.NotEmpty(t, resp.Name, "App name should not be empty")
	require.NotNil(t, resp.Organization, "App organization should not be nil")
	require.NotEmpty(t, resp.Organization.Name, "Organization name should not be empty")
	require.NotEmpty(t, resp.Organization.Slug, "Organization slug should not be empty")
	require.NotEmpty(t, resp.Status, "App status should not be empty")
}

// createAppDeployToken 测试创建deploy token的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func createAppDeployToken(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName string) {
	req := &actualFly.CreateAppDeployTokenRequest{
		Expiry: "", // 可选
	}
	resp, err := flyService.Apps.CreateAppDeployToken(ctx, appName, req)
	require.NoError(t, err, "CreateAppDeployToken should not return an error")
	require.NotNil(t, resp, "CreateAppDeployToken should return a non-nil response")
	require.NotEmpty(t, resp.Token, "Deploy token should not be empty")
}

// deleteApp 测试删除应用的正确流程
// 该方法为内部测试工具方法，仅供main_test.go调用，不能直接作为go test入口
func deleteApp(t *testing.T, ctx context.Context, flyService *actualFly.Service, appName string) {
	err := flyService.Apps.DeleteApp(ctx, appName)
	require.NoError(t, err, "DeleteApp should not return an error")
}

package netlify_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
)

// executeCreateSiteBuildTest 测试为指定站点触发新的构建。
// siteID 是必需的，因为构建是针对特定站点的。
// 注意: 构建通常是部署流程的一部分，没有直接的"删除构建"API。
// 构建成功后，通常会关联一个 deployID。
// 此函数现在返回创建的 Build 对象，以便调用者可以访问 BuildID 和 DeployID。
func executeCreateSiteBuildTest(t *testing.T, ctx context.Context, nfService *nactual.Service, siteID string) *nactual.Build {
	t.Helper()
	t.Logf("Attempting to create site build for site ID: %s", siteID)

	createdBuild, err := nfService.Builds.CreateSiteBuild(ctx, siteID)
	require.NoError(t, err, "Error creating site build for site %s", siteID)
	require.NotNil(t, createdBuild, "Created build should not be nil")

	assert.NotEmpty(t, createdBuild.ID, "Build ID should not be empty")
	// DeployID 可能会在构建开始后才填充，或者在 CreateSiteBuild 响应中立即可用。
	// 对于立即流式传输日志的测试，如果 DeployID 为空，则无法进行。
	// if createdBuild.DeployID == "" {
	// 	t.Logf("Warning: DeployID is empty immediately after creating build ID: %s for site ID: %s", createdBuild.ID, siteID)
	// }
	assert.False(t, createdBuild.Done, "Build should not be marked as done immediately after creation trigger")
	assert.NotEmpty(t, createdBuild.CreatedAt, "Build CreatedAt should not be empty")

	t.Logf("Successfully triggered build ID: %s for site ID: %s. DeployID (if available): '%s', State: %s", createdBuild.ID, siteID, createdBuild.DeployID, createdBuild.State)

	// 通常不直接删除构建记录，它们是部署历史的一部分。
	return createdBuild
}

// executeStreamBuildLogsTest 测试为指定的部署流式传输构建日志。
func executeStreamBuildLogsTest(t *testing.T, ctx context.Context, nfService *nactual.Service, siteID string, deployID string) {
	t.Helper()
	require.NotEmpty(t, siteID, "siteID cannot be empty for streaming logs")
	require.NotEmpty(t, deployID, "deployID cannot be empty for streaming logs")

	t.Logf("Attempting to stream build logs for site ID: %s, deploy ID: %s", siteID, deployID)

	logs, err := nfService.Builds.StreamBuildLogs(ctx, deployID, siteID)

	// 流式日志的错误处理可能比较复杂。
	// StreamBuildLogs 内部会处理 WebSocket 连接错误和消息解析错误。
	// 此处 require.NoError 检查 StreamBuildLogs 本身返回的错误，例如配置问题或早期连接失败。
	require.NoError(t, err, "Error calling StreamBuildLogs for site %s, deploy %s", siteID, deployID)

	// 日志本身可能为空（特别是对于非常快速的构建），但不应该是 nil。
	assert.NotNil(t, logs, "Streamed logs result should not be nil, even if empty")

	if err == nil {
		t.Logf("Successfully completed StreamBuildLogs call for site ID: %s, deploy ID: %s. Log count: %d", siteID, deployID, len(logs))
		if len(logs) > 0 {
			// 可以选择性地检查日志内容，例如最后一条日志的类型
			lastLog := logs[len(logs)-1]
			logType, ok := lastLog["type"].(string)
			if ok {
				t.Logf("Last log message type: %s", logType)
				// 通常，Netlify 的日志流以 type: "report" 的消息结束
				// assert.Equal(t, "report", logType, "Expected last log message type to be 'report'")
			} else {
				t.Logf("Last log message does not have a 'type' field or it's not a string: %+v", lastLog)
			}
		} else {
			t.Logf("Streamed logs were empty for site ID: %s, deploy ID: %s. This might be normal for very fast builds (e.g., using 'echo' as build command).", siteID, deployID)
		}
	}
}

package netlify_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	// "github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// executeCreateAndThenDeleteSite tests the creation and subsequent deletion of a Netlify site.
// It's designed to be called from a master test function in main_test.go.
func executeCreateAndThenDeleteSite(t *testing.T, ctx context.Context, nfService *nactual.Service) {
	uniqueSiteName := fmt.Sprintf("be-web-builder-test-site-%d", time.Now().UnixNano())
	siteOptions := &nactual.CreateSiteOptions{
		Name: uniqueSiteName,
		// BuildSettings 可以根据需要添加，对于基本测试，可能不需要
	}

	createdSite, err := nfService.Sites.CreateSite(ctx, siteOptions)
	require.NoError(t, err, "Error creating site")
	require.NotNil(t, createdSite, "Created site should not be nil")
	assert.NotEmpty(t, createdSite.ID, "Site ID should not be empty")
	if siteOptions.Name != "" {
		assert.NotEmpty(t, createdSite.Name, "Site name should not be empty if one was requested")
		t.Logf("Attempted to create site with name: %s, got: %s (ID: %s)", siteOptions.Name, createdSite.Name, createdSite.ID)
	} else {
		t.Logf("Successfully created site with auto-generated name: %s (ID: %s)", createdSite.Name, createdSite.ID)
	}

	// 测试 GetSite 方法
	t.Run("GetSite_Found", func(t *testing.T) {
		retrievedSite, getErr := nfService.Sites.GetSite(ctx, createdSite.ID)
		require.NoError(t, getErr, "GetSite should not return an error for existing site")
		require.NotNil(t, retrievedSite, "GetSite should return a site object for existing site")
		assert.Equal(t, createdSite.ID, retrievedSite.ID, "Retrieved site ID should match created site ID")
		assert.Equal(t, createdSite.Name, retrievedSite.Name, "Retrieved site name should match created site name")
		// 记录一些额外信息以供调试
		t.Logf("Successfully retrieved site via GetSite: ID=%s, Name=%s, URL=%s, AdminURL=%s", retrievedSite.ID, retrievedSite.Name, retrievedSite.URL, retrievedSite.AdminURL)
		if retrievedSite.Repo != nil {
			t.Logf("Retrieved site repo info: Provider=%s, RepoPath=%s, Branch=%s", retrievedSite.Repo.Provider, retrievedSite.Repo.Repo, retrievedSite.Repo.Branch)
		}
		if retrievedSite.PublishedDeploy != nil {
			t.Logf("Retrieved site published deploy info: ID=%s, State=%s", retrievedSite.PublishedDeploy.ID, retrievedSite.PublishedDeploy.State)
		}
	})

	// 测试 GetSite 方法 - 尝试获取不存在的站点
	t.Run("GetSite_NotFound", func(t *testing.T) {
		nonExistentSiteID := "00000000-0000-0000-0000-000000000000" // 一个不太可能存在的ID
		_, getErr := nfService.Sites.GetSite(ctx, nonExistentSiteID)
		assert.Error(t, getErr, "GetSite should return an error for a non-existent site ID")
		// 可以在这里添加更具体的错误类型检查，如果 APIError 类型可用且包含状态码
		t.Logf("Correctly failed to retrieve non-existent site ID: %s, Error: %v", nonExistentSiteID, getErr)
	})

	// 测试 GetSite 方法 - 尝试使用空ID
	t.Run("GetSite_EmptyID", func(t *testing.T) {
		_, getErr := nfService.Sites.GetSite(ctx, "")
		assert.Error(t, getErr, "GetSite should return an error for an empty site ID")
		t.Logf("Correctly failed to retrieve site with empty ID, Error: %v", getErr)
	})

	t.Cleanup(func() {
		t.Logf("Cleaning up: attempting to delete site ID: %s (Name: %s)", createdSite.ID, createdSite.Name)
		delErr := nfService.Sites.DeleteSite(ctx, createdSite.ID)
		assert.NoError(t, delErr, "Error deleting site during cleanup")
		if delErr == nil {
			t.Logf("Successfully deleted site ID: %s (Name: %s) during cleanup", createdSite.ID, createdSite.Name)
		}
	})
}

// TestListSite tests the ListSite method of the Netlify service.
func TestListSite(t *testing.T) {
	ctx := context.Background()
	cfg, err := config.LoadConfig("dev")
	require.NoError(t, err, "Failed to load config")

	nfService, err := nactual.NewService(cfg.Netlify.Token)
	require.NoError(t, err, "Failed to create Netlify service")
	require.NotNil(t, nfService, "Netlify service should not be nil")

	// Test with default options
	t.Run("ListSite_Default", func(t *testing.T) {
		sites, err := nfService.Sites.ListSite(ctx, nil)
		require.NoError(t, err, "ListSite should not return an error")
		require.NotNil(t, sites, "ListSite should return a non-nil slice")
		t.Logf("Successfully retrieved %d sites with default options", len(sites))
		
		// Log some details about the first few sites
		for i, site := range sites {
			if i >= 3 { // Only log first 3 sites
				break
			}
			t.Logf("Site %d: ID=%s, Name=%s, URL=%s", i+1, site.ID, site.Name, site.URL)
		}
	})

	// Test with custom options
	t.Run("ListSite_WithOptions", func(t *testing.T) {
		opts := &nactual.ListSiteOptions{
			Filter:           "all",
			SortBy:           "published_at",
			OrderBy:          "desc",
			Page:             1,
			PerPage:          10,
			IncludeFavorites: true,
		}

		sites, err := nfService.Sites.ListSite(ctx, opts)
		require.NoError(t, err, "ListSite should not return an error with custom options")
		require.NotNil(t, sites, "ListSite should return a non-nil slice with custom options")
		t.Logf("Successfully retrieved %d sites with custom options", len(sites))

		// Log some details about the first few sites
		for i, site := range sites {
			if i >= 3 { // Only log first 3 sites
				break
			}
			t.Logf("Site %d: ID=%s, Name=%s, URL=%s", i+1, site.ID, site.Name, site.URL)
		}
	})
}

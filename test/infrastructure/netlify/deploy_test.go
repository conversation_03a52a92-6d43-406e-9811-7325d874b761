package netlify_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	// "github.com/web-builder-dev/be-web-builder/internal/config" // config is used by the shared helper
	// "github.com/web-builder-dev/be-web-builder/internal/pkg/logger" // logger is initialized in TestMain
)

// executeCreateAndThenDeleteDeployKey tests the creation and subsequent deletion of a Netlify deploy key.
// It's designed to be called from a master test function in main_test.go.
func executeCreateAndThenDeleteDeployKey(t *testing.T, ctx context.Context, nfService *nactual.Service) {
	createdKey, err := nfService.Deploys.CreateDeployKey(ctx)
	require.NoError(t, err, "Error creating deploy key")
	require.NotNil(t, createdKey, "Created deploy key should not be nil")
	assert.NotEmpty(t, createdKey.ID, "Deploy key ID should not be empty")
	assert.NotEmpty(t, createdKey.PublicKey, "Deploy key PublicKey should not be empty")
	assert.NotEmpty(t, createdKey.CreatedAt, "Deploy key CreatedAt should not be empty")
	t.Logf("Successfully created deploy key ID: %s", createdKey.ID)

	t.Cleanup(func() {
		t.Logf("Cleaning up: attempting to delete deploy key ID: %s", createdKey.ID)
		delErr := nfService.Deploys.DeleteDeployKey(ctx, createdKey.ID)
		assert.NoError(t, delErr, "Error deleting deploy key during cleanup")
		if delErr == nil {
			t.Logf("Successfully deleted deploy key ID: %s during cleanup", createdKey.ID)
		}
	})
}

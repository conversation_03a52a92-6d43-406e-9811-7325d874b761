package netlify_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain is executed before any other tests in this package.
// It's a good place for one-time setup, like logger initialization.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test")
	os.Exit(m.Run())
}

// setupNetlifyTestService loads config and initializes the Netlify Service.
// Skips test if essential Netlify config (token) is missing.
func setupNetlifyTestService(t *testing.T) (context.Context, *nactual.Service) {
	t.Helper()
	// Logger is initialized in TestMain, so no need to call logger.InitLogger here repeatedly.

	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping Netlify integration test: Could not load config for env 'dev': %v.", err)
	}

	token := cfg.Netlify.Token
	if token == "" {
		t.Skip("Skipping Netlify integration test: NETLIFY_TOKEN must be available.")
	}

	nfService, err := nactual.NewService(token)
	require.NoError(t, err, "Failed to create Netlify service")
	require.NotNil(t, nfService, "Netlify service should not be nil")

	ctx := context.Background()
	return ctx, nfService
}

// setupGithubTestServiceForNetlifyTests loads GitHub config and initializes the GitHub Service.
// Similar to setupTestService in github_test, but tailored for use here.
func setupGithubTestServiceForNetlifyTests(t *testing.T) (context.Context, *actualGitHub.Service, string /* owner */) {
	t.Helper()
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping GitHub dependent Netlify tests: Could not load GitHub config for env 'dev': %v.", err)
	}
	ghToken := cfg.GitHub.Token
	ghOwner := cfg.GitHub.Owner
	if ghToken == "" || ghOwner == "" {
		t.Skip("Skipping GitHub dependent Netlify tests: GITHUB_TOKEN and GITHUB_OWNER must be available.")
	}
	ghService := actualGitHub.NewService(ghToken)
	ctx := context.Background() // Can reuse ctx if already created
	return ctx, ghService, ghOwner
}

// createTempGithubRepoForNetlifyTest creates a temporary GitHub repository for Netlify tests.
// Adapts createTempRepo from github_test.
func createTempGithubRepoForNetlifyTest(t *testing.T, ctx context.Context, ghService *actualGitHub.Service, owner string) *gh.Repository {
	t.Helper()
	repoName := fmt.Sprintf("netlify-shared-test-repo-%d", time.Now().UnixNano())
	repoToCreate := &gh.Repository{
		Name:     gh.String(repoName),
		Private:  gh.Bool(true),
		AutoInit: gh.Bool(true), // Auto-initialize with a README for a default branch
	}
	createdRepo, _, err := ghService.Repositories.CreateUserRepository(ctx, repoToCreate)
	require.NoError(t, err, "Failed to create temporary GitHub repository for Netlify test")
	require.NotNil(t, createdRepo, "Created temporary GitHub repository should not be nil")
	t.Logf("Successfully created temporary GitHub repository %s/%s for Netlify tests", owner, createdRepo.GetName())

	t.Cleanup(func() {
		t.Logf("Cleaning up temporary GitHub repository: %s/%s", owner, createdRepo.GetName())
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, createdRepo.GetName())
		if delErr != nil {
			t.Logf("Warning: failed to delete temporary GitHub repository %s/%s: %v", owner, createdRepo.GetName(), delErr)
		} else {
			t.Logf("Successfully deleted temporary GitHub repository %s/%s", owner, createdRepo.GetName())
		}
	})
	return createdRepo
}

// TestNetlifyIntegrationSuite is the main entry point for running ordered Netlify integration tests.
func TestNetlifyIntegrationSuite(t *testing.T) {
	ctx, nfService := setupNetlifyTestService(t)

	// Setup GitHub service and a temporary repository for tests that need it
	// This context (ghCtx) might be different if operations take longer or need specific values
	ghCtx, ghService, ghOwner := setupGithubTestServiceForNetlifyTests(t)
	tempRepo := createTempGithubRepoForNetlifyTest(t, ghCtx, ghService, ghOwner)

	// Create a Netlify deploy key
	netlifyDeployKey, err := nfService.Deploys.CreateDeployKey(ctx)
	require.NoError(t, err, "Failed to create Netlify deploy key for shared site")
	require.NotNil(t, netlifyDeployKey, "Netlify deploy key should not be nil")
	t.Logf("Successfully created Netlify deploy key ID: %s", netlifyDeployKey.ID)
	t.Cleanup(func() {
		t.Logf("Cleaning up Netlify deploy key ID: %s", netlifyDeployKey.ID)
		delKeyErr := nfService.Deploys.DeleteDeployKey(ctx, netlifyDeployKey.ID)
		assert.NoError(t, delKeyErr, "Error deleting Netlify deploy key during cleanup")
	})

	// Add Netlify deploy key to the temporary GitHub repository
	ghDeployKeyTitle := fmt.Sprintf("Netlify Test Key for %s", tempRepo.GetName())
	_, _, ghKeyErr := ghService.Repositories.AddDeployKey(ghCtx, ghOwner, tempRepo.GetName(), &gh.Key{
		Title:    gh.String(ghDeployKeyTitle),
		Key:      gh.String(netlifyDeployKey.PublicKey),
		ReadOnly: gh.Bool(true), // Typically Netlify needs read-only access for deploys
	})
	require.NoError(t, ghKeyErr, "Failed to add Netlify deploy key to temporary GitHub repository")
	t.Logf("Successfully added Netlify deploy key to GitHub repo %s/%s", ghOwner, tempRepo.GetName())

	// 为后续的 Hook 和 Build 测试创建一个共享的测试站点, 现在关联到临时GitHub仓库
	var sharedSiteID string
	sharedSiteName := fmt.Sprintf("be-web-builder-shared-site-%d", time.Now().UnixNano())
	createSiteOpts := &nactual.CreateSiteOptions{
		Name: sharedSiteName,
		BuildSettings: &nactual.SiteBuildSettingsInfo{
			Provider:    "github",
			RepoURL:     tempRepo.GetHTMLURL(),
			RepoBranch:  tempRepo.GetDefaultBranch(), // Use the actual default branch
			DeployKeyID: netlifyDeployKey.ID,
			Cmd:         "echo \"Building...\"", // Simple build command
			Dir:         ".",                    // Publish current directory (or specify actual build output dir)
			RepoPath:    tempRepo.GetFullName(), // owner/repo
		},
	}

	sharedSite, err := nfService.Sites.CreateSite(ctx, createSiteOpts)
	require.NoError(t, err, "Failed to create shared site for Hook and Build tests with repo")
	require.NotNil(t, sharedSite, "Shared site should not be nil")
	sharedSiteID = sharedSite.ID
	t.Logf("Successfully created shared site ID: %s (Name: %s) linked to repo %s for subsequent tests.", sharedSiteID, sharedSite.Name, tempRepo.GetHTMLURL())

	t.Cleanup(func() {
		t.Logf("Cleaning up shared site ID: %s (Name: %s)", sharedSiteID, sharedSite.Name)
		delErr := nfService.Sites.DeleteSite(ctx, sharedSiteID)
		if delErr != nil {
			t.Errorf("Error deleting shared site ID %s during cleanup: %v", sharedSiteID, delErr)
		} else {
			t.Logf("Successfully deleted shared site ID: %s (Name: %s) during cleanup", sharedSiteID, sharedSite.Name)
		}
	})

	// 运行与部署密钥相关的测试 (这个测试不依赖共享站点或仓库)
	t.Run("DeployKeyLifecycleStandalone", func(t *testing.T) {
		executeCreateAndThenDeleteDeployKey(t, ctx, nfService) // This creates and deletes its own key
	})

	// 运行与站点生命周期相关的测试 (这个测试会自己创建和删除站点, 不依赖共享仓库)
	t.Run("SiteLifecycleStandalone", func(t *testing.T) {
		executeCreateAndThenDeleteSite(t, ctx, nfService)
	})

	// 运行与 Hook 相关的测试 (使用共享站点ID)
	if sharedSiteID != "" {
		t.Run("HookCreation", func(t *testing.T) {
			executeCreateHookTest(t, ctx, nfService, sharedSiteID)
		})

		// 运行与 Build 相关的测试 (使用共享站点ID, 已关联仓库)
		var createdBuildForLogs *nactual.Build // 用于捕获构建结果以测试日志流
		t.Run("SiteBuildCreationWithRepo", func(t *testing.T) {
			createdBuildForLogs = executeCreateSiteBuildTest(t, ctx, nfService, sharedSiteID)
			require.NotNil(t, createdBuildForLogs, "executeCreateSiteBuildTest should return a build object")
		})

		// 仅当成功创建构建并且获得了 DeployID 时，才运行流式日志测试
		if createdBuildForLogs != nil && createdBuildForLogs.DeployID != "" {
			t.Run("StreamBuildLogsForSite", func(t *testing.T) {
				executeStreamBuildLogsTest(t, ctx, nfService, sharedSiteID, createdBuildForLogs.DeployID)
			})
		} else {
			if createdBuildForLogs == nil {
				t.Log("Skipping StreamBuildLogsForSite test because createdBuildForLogs is nil (SiteBuildCreationWithRepo might have failed).")
			} else {
				t.Logf("Skipping StreamBuildLogsForSite test because DeployID is empty for Build ID: %s. This might happen if the build failed very early or API behavior changed.", createdBuildForLogs.ID)
			}
		}

		// 运行与 Snippet 相关的 CRUD 测试 (使用共享站点ID)
		t.Run("SnippetCRUD", func(t *testing.T) {
			executeSnippetCRUDTests(t, ctx, nfService, sharedSiteID)
		})
		t.Run("ListSite", func(t *testing.T) {
			TestListSite(t)
		})
	} else {
		t.Log("Skipping HookCreation, SiteBuildCreationWithRepo, and SnippetCRUD tests as shared site creation failed or was skipped.")
	}
}

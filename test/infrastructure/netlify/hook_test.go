package netlify_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
)

// executeCreateHookTest 测试创建 Netlify Webhook。
// siteID 是必需的，因为 Webhook 是与特定站点关联的。
// 注意: 当前 Netlify Go 客户端中没有实现 DeleteHook 方法，因此本测试不包含自动清理。
func executeCreateHookTest(t *testing.T, ctx context.Context, nfService *nactual.Service, siteID string) {
	t.Helper()
	t.Logf("Attempting to create hook for site ID: %s", siteID)

	hookData := nactual.HookCreateRequestData{
		SiteID: siteID,
		Type:   "url",                                                         // 一个通用的 Webhook 类型示例
		Event:  "deploy_created",                                              // 监听的事件示例
		Data:   nactual.HookData{URL: "https://example.com/webhook-receiver"}, // 示例数据
	}

	createdHook, err := nfService.Hooks.CreateHook(ctx, hookData)
	require.NoError(t, err, "Error creating hook for site %s", siteID)
	require.NotNil(t, createdHook, "Created hook should not be nil")

	assert.NotEmpty(t, createdHook.ID, "Hook ID should not be empty")
	assert.Equal(t, siteID, createdHook.SiteID, "Hook SiteID should match requested SiteID")
	assert.Equal(t, hookData.Type, createdHook.Type, "Hook Type should match requested Type")
	assert.Equal(t, hookData.Event, createdHook.Event, "Hook Event should match requested Event")
	assert.NotEmpty(t, createdHook.CreatedAt, "Hook CreatedAt should not be empty")

	t.Logf("Successfully created hook ID: %s for site ID: %s", createdHook.ID, siteID)

	// TODO: 添加 Cleanup 逻辑来删除创建的 hook，一旦 nfService.Hooks.DeleteHook 实现。
	// t.Cleanup(func() {
	// 	 t.Logf("Cleaning up: attempting to delete hook ID: %s", createdHook.ID)
	// 	 delErr := nfService.Hooks.DeleteHook(ctx, createdHook.ID) // 假设的 DeleteHook 方法
	// 	 assert.NoError(t, delErr, "Error deleting hook during cleanup")
	// 	 if delErr == nil {
	// 		 t.Logf("Successfully deleted hook ID: %s during cleanup", createdHook.ID)
	// 	 }
	// })
}

package netlify_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
)

// executeSnippetCRUDTests tests the full lifecycle (Create, Read, List, Update, Delete) of Netlify snippets
// using a sharedSiteID created in main_test.go.
func executeSnippetCRUDTests(t *testing.T, ctx context.Context, nfService *nactual.Service, sharedSiteID string) {
	require.NotEmpty(t, sharedSiteID, "sharedSiteID cannot be empty for snippet tests")

	var createdSnippetID string
	// Use a more unique title to aid in retrieval after creation, as create doesn't return the object
	uniqueTimestamp := time.Now().UnixNano()
	originalSnippetTitle := fmt.Sprintf("Test-Snippet-%d", uniqueTimestamp)
	originalGeneralContent := fmt.Sprintf("<script>console.log('original test snippet %d');</script>", uniqueTimestamp)
	originalPosition := "head"

	// 1. Create Snippet (now only returns error)
	t.Run("CreateSnippet", func(t *testing.T) {
		createReq := &nactual.CreateSnippetRequest{
			Title:           originalSnippetTitle,
			General:         originalGeneralContent,
			GeneralPosition: originalPosition,
		}
		err := nfService.Snippets.CreateSiteSnippet(ctx, sharedSiteID, createReq)
		require.NoError(t, err, "Error creating site snippet")
		t.Logf("Successfully requested creation of snippet with title: %s for site ID: %s", originalSnippetTitle, sharedSiteID)
	})

	// 1.5. Get Created Snippet ID by Listing and Matching Title
	t.Run("GetCreatedSnippetIDByListing", func(t *testing.T) {
		maxAttempts := 3
		var foundSnippet *nactual.Snippet
		for attempt := 1; attempt <= maxAttempts; attempt++ {
			snippets, err := nfService.Snippets.ListSiteSnippets(ctx, sharedSiteID)
			require.NoError(t, err, "Error listing site snippets to find created one")
			for i := range snippets {
				if snippets[i].Title == originalSnippetTitle {
					foundSnippet = &snippets[i]
					break
				}
			}
			if foundSnippet != nil {
				break
			}
			t.Logf("Attempt %d to find snippet by title '%s' failed, retrying in 2 seconds...", attempt, originalSnippetTitle)
			time.Sleep(2 * time.Second) // Wait a bit for Netlify to process the creation
		}

		require.NotNil(t, foundSnippet, "Could not find created snippet with title '%s' by listing", originalSnippetTitle)
		require.NotEmpty(t, foundSnippet.ID, "Found snippet ID should not be empty")
		createdSnippetID = foundSnippet.ID
		assert.Equal(t, originalGeneralContent, foundSnippet.General, "Snippet general content mismatch when found by listing")
		assert.Equal(t, originalPosition, foundSnippet.GeneralPosition, "Snippet general position mismatch when found by listing")
		t.Logf("Successfully found created snippet ID: %s with title: %s", createdSnippetID, originalSnippetTitle)
	})

	// Ensure createdSnippetID is available for subsequent tests
	require.NotEmpty(t, createdSnippetID, "Snippet ID was not set after creation and listing, cannot proceed with further snippet tests")

	// 2. Get Snippet (verify the one found)
	t.Run("GetSnippet", func(t *testing.T) {
		snippet, err := nfService.Snippets.GetSiteSnippet(ctx, sharedSiteID, createdSnippetID)
		require.NoError(t, err, "Error getting site snippet")
		require.NotNil(t, snippet, "Retrieved snippet should not be nil")
		assert.Equal(t, createdSnippetID, snippet.ID, "Snippet ID mismatch")
		assert.Equal(t, originalSnippetTitle, snippet.Title, "Snippet title mismatch")
		assert.Equal(t, originalGeneralContent, snippet.General, "Snippet general content mismatch")
		assert.Equal(t, originalPosition, snippet.GeneralPosition, "Snippet general position mismatch")
		t.Logf("Successfully retrieved snippet ID: %s", createdSnippetID)
	})

	// 3. List Snippets (re-check, though already implicitly tested by finding it)
	t.Run("ListSnippetsAgain", func(t *testing.T) {
		snippets, err := nfService.Snippets.ListSiteSnippets(ctx, sharedSiteID)
		require.NoError(t, err, "Error listing site snippets again")
		require.NotNil(t, snippets, "Snippets list should not be nil again")

		found := false
		for _, s := range snippets {
			if s.ID == createdSnippetID {
				assert.Equal(t, originalSnippetTitle, s.Title)
				found = true
				break
			}
		}
		assert.True(t, found, "Created snippet not found in list when listing again")
		t.Logf("Successfully listed snippets again, found snippet ID: %s", createdSnippetID)
	})

	// 4. Update Snippet
	updatedSnippetTitle := fmt.Sprintf("Updated-Test-Snippet-%d", uniqueTimestamp) // Keep timestamp for uniqueness
	updatedGeneralContent := fmt.Sprintf("<script>console.log('updated test snippet %d');</script>", uniqueTimestamp)
	updatedPosition := "footer"
	t.Run("UpdateSnippet", func(t *testing.T) {
		updateReq := &nactual.UpdateSnippetRequest{
			Title:           updatedSnippetTitle,
			General:         updatedGeneralContent,
			GeneralPosition: updatedPosition,
		}
		snippet, err := nfService.Snippets.UpdateSiteSnippet(ctx, sharedSiteID, createdSnippetID, updateReq)
		require.NoError(t, err, "Error updating site snippet")
		require.NotNil(t, snippet, "Updated snippet should not be nil")
		assert.Equal(t, createdSnippetID, snippet.ID, "Snippet ID mismatch after update")
		assert.Equal(t, updatedSnippetTitle, snippet.Title, "Snippet title not updated")
		assert.Equal(t, updatedGeneralContent, snippet.General, "Snippet general content not updated")
		assert.Equal(t, updatedPosition, snippet.GeneralPosition, "Snippet general position not updated")
		t.Logf("Successfully updated snippet ID: %s to title: %s", createdSnippetID, updatedSnippetTitle)
	})

	// 5. Delete Snippet (using t.Cleanup for guaranteed execution)
	t.Cleanup(func() {
		if createdSnippetID == "" {
			t.Logf("Skipping snippet deletion cleanup as createdSnippetID is empty")
			return
		}
		t.Logf("Cleaning up: attempting to delete snippet ID: %s for site ID: %s", createdSnippetID, sharedSiteID)
		err := nfService.Snippets.DeleteSiteSnippet(ctx, sharedSiteID, createdSnippetID)
		assert.NoError(t, err, "Error deleting site snippet during cleanup")
		if err == nil {
			t.Logf("Successfully deleted snippet ID: %s during cleanup", createdSnippetID)
		}

		// Verify deletion by trying to get it again
		_, getErr := nfService.Snippets.GetSiteSnippet(ctx, sharedSiteID, createdSnippetID)
		assert.Error(t, getErr, "Expected error when trying to get a deleted snippet")
		if apiErr, ok := getErr.(*nactual.APIError); ok {
			assert.Equal(t, 404, apiErr.StatusCode, "Expected 404 Not Found after deleting snippet")
		} else if getErr != nil {
			t.Logf("Got non-APIError when trying to get deleted snippet: %v", getErr)
		}
	})
}

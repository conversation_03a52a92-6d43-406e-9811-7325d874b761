package netlify_service_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	gh "github.com/google/go-github/v72/github"
	"github.com/web-builder-dev/be-web-builder/internal/application/netlify_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraSupabase "github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// TestMain is executed before any other tests in this package.
func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test") // Initialize logger for tests
	os.Exit(m.Run())
}

// setupInitNetlifySiteTest 是一个辅助函数，用于为 InitNetlifySite 测试设置服务。
func setupInitNetlifySiteTest(t *testing.T) (context.Context, *netlify_service.InitNetlifySiteService, *infraGitHub.Service, *infraNetlify.Service, repository.ProjectRepository, *config.Config) {
	t.Helper()
	ctx := context.Background()

	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Fatalf("未能加载 'dev' 环境的配置: %v。请确保 conf/config_dev.yaml 文件存在。", err)
	}

	if cfg.Netlify.Token == "" || cfg.GitHub.Token == "" || cfg.GitHub.Owner == "" {
		t.Skip("跳过集成测试：必须配置 NETLIFY_TOKEN, GITHUB_TOKEN, 和 GITHUB_OWNER。")
	}
	if cfg.Supabase.URL == "" || cfg.Supabase.ServiceKey == "" {
		t.Skip("跳过集成测试：必须配置 SUPABASE_URL 和 SUPABASE_SERVICE_KEY。")
	}

	netlifyInfra, err := infraNetlify.NewService(cfg.Netlify.Token)
	require.NoError(t, err, "未能创建 Netlify 基础设施服务")

	githubInfra := infraGitHub.NewService(cfg.GitHub.Token)

	supabaseClient, err := infraSupabase.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	require.NoError(t, err, "未能创建 Supabase 客户端")
	projectRepo := infraSupabase.NewSupabaseProjectRepository(supabaseClient)

	appHookService := netlify_service.NewHookService(netlifyInfra, cfg)

	initSiteSvc := netlify_service.NewInitNetlifySiteService(netlifyInfra, githubInfra, appHookService, projectRepo, cfg)

	return ctx, initSiteSvc, githubInfra, netlifyInfra, projectRepo, cfg
}

// createTempGithubRepoForTest 是一个辅助函数，用于为测试创建一个临时的 GitHub 仓库。
func createTempGithubRepoForTest(t *testing.T, ctx context.Context, ghService *infraGitHub.Service, owner string) string {
	t.Helper()
	repoName := fmt.Sprintf("test-init-netlify-site-%d", time.Now().UnixNano())
	repoToCreate := &gh.Repository{
		Name:     gh.String(repoName),
		Private:  gh.Bool(true),
		AutoInit: gh.Bool(true), // Auto-initialize with a README
	}
	createdRepo, _, err := ghService.Repositories.CreateUserRepository(ctx, repoToCreate)
	require.NoError(t, err, "Failed to create temporary GitHub repository for test")
	require.NotNil(t, createdRepo, "Created temporary GitHub repository should not be nil")
	t.Logf("Successfully created temporary GitHub repository %s/%s for test", owner, createdRepo.GetName())

	t.Cleanup(func() {
		t.Logf("Cleaning up temporary GitHub repository: %s/%s", owner, createdRepo.GetName())
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, createdRepo.GetName())
		if delErr != nil {
			// Log as warning because other cleanups might still need to run
			t.Logf("Warning: failed to delete temporary GitHub repository %s/%s: %v", owner, createdRepo.GetName(), delErr)
		} else {
			t.Logf("Successfully deleted temporary GitHub repository %s/%s", owner, createdRepo.GetName())
		}
	})
	return createdRepo.GetName()
}

func TestInitNetlifySite_SuccessfulInitialization(t *testing.T) {
	ctx, initSiteService, githubInfra, netlifyInfra, projectRepo, cfg := setupInitNetlifySiteTest(t)

	// 为本次测试创建一个临时的 GitHub 仓库
	tempRepoName := createTempGithubRepoForTest(t, ctx, githubInfra, cfg.GitHub.Owner)

	// 为本次测试创建一个临时的项目记录
	testProject := &entity.Project{
		// ID 会在 Create 方法中自动生成 (基于 SupabaseProjectRepository 实现)
		UserID: "4d3d7ae8-bb68-4301-a83f-efe44f089cea", // 使用一个虚拟的用户ID
		Title:  "Temporary Project for InitNetlifySite Test",
		RepoID: tempRepoName, // 可以关联到刚创建的 repo
	}
	err := projectRepo.Create(ctx, testProject) // testProject.ID 将会被填充
	require.NoError(t, err, "创建临时项目记录失败")
	require.NotEmpty(t, testProject.ID, "创建后项目ID不应为空")
	t.Logf("为测试创建了临时项目 ID: %s", testProject.ID)

	t.Cleanup(func() {
		t.Logf("清理测试项目 ID: %s", testProject.ID)
		delErr := projectRepo.Delete(ctx, testProject.ID)
		if delErr != nil {
			t.Logf("警告: 清理测试项目 ID %s 失败: %v", testProject.ID, delErr)
		}
	})

	var createdNetlifySiteID string // 用于 Netlify 站点清理
	t.Cleanup(func() {
		if createdNetlifySiteID != "" {
			t.Logf("清理 Netlify 站点 ID: %s", createdNetlifySiteID)
			nErr := netlifyInfra.Sites.DeleteSite(ctx, createdNetlifySiteID)
			if nErr != nil {
				t.Logf("警告: 清理 Netlify 站点 ID %s 失败: %v", createdNetlifySiteID, nErr)
			} else {
				t.Logf("成功删除 Netlify 站点 ID: %s", createdNetlifySiteID)
			}
		}
		// 注意: InitNetlifySite 创建的 Netlify 部署密钥不会被此测试清理，
		// 因为其 ID 未由 InitNetlifySite 返回。这是一个已知的限制。
	})

	// 执行服务方法，使用临时项目的 ID
	returnedNetlifySiteID, err := initSiteService.InitNetlifySite(ctx, testProject.ID, tempRepoName)
	createdNetlifySiteID = returnedNetlifySiteID // 存储用于清理

	// 断言
	require.NoError(t, err, "InitNetlifySite 在成功初始化时不应返回错误")
	require.NotEmpty(t, returnedNetlifySiteID, "InitNetlifySite 应返回一个非空的 Netlify 站点 ID")

	// 验证项目记录是否已更新
	updatedProjectEntity, err := projectRepo.GetByID(ctx, testProject.ID)
	require.NoError(t, err, "从数据库获取更新后的项目失败")
	require.NotNil(t, updatedProjectEntity, "更新后的项目实体不应为 nil")
	assert.Equal(t, returnedNetlifySiteID, updatedProjectEntity.NetlifySiteID, "项目记录中的 NetlifySiteID 未正确更新")
	// 检查 UpdatedAt 是否有变化可能比较棘手，因为 Create 也会设置它。
	// 可以比较初始创建后的 UpdatedAt 和 InitNetlifySite 之后的 UpdatedAt。
	// 或者，如果 InitNetlifySite 内部的 Update 确实总是更新时间戳，则可以简单检查它是否接近当前时间。
	assert.NotZero(t, updatedProjectEntity.UpdatedAt, "更新后的项目 UpdatedAt 不应为零值")
	initialUpdatedAt := testProject.UpdatedAt // Create 之后 project 应该被填充了 UpdatedAt
	if initialUpdatedAt.IsZero() {            // 如果 Supabase Create 没有立即返回 UpdatedAt，则这个比较意义不大
		t.Log("初始项目的 UpdatedAt 为零，跳过 UpdatedAt 变更检查。")
	} else {
		assert.True(t, updatedProjectEntity.UpdatedAt.After(initialUpdatedAt), "更新后的 UpdatedAt (%v) 应晚于初始 UpdatedAt (%v)", updatedProjectEntity.UpdatedAt, initialUpdatedAt)
	}

	t.Logf("成功初始化 Netlify 站点: %s，项目: %s，仓库: %s", returnedNetlifySiteID, testProject.ID, tempRepoName)
}

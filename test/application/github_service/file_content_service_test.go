package github_service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	actualGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// TestFileContentService 测试 FileContentService 的功能
func TestFileContentService(t *testing.T) {
	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping test: Could not load config for env 'dev': %v", err)
	}

	if cfg.GitHub.Token == "" || cfg.GitHub.Owner == "" {
		t.Skip("Skipping test: GITHUB_TOKEN and GITHUB_OWNER must be available")
	}

	// 创建 GitHub 服务
	ghService := actualGitHub.NewService(cfg.GitHub.Token)

	// 创建 FileContentService
	fileContentService := github_service.NewFileContentService(ghService, cfg)

	// 测试获取仓库文件内容
	ctx := context.Background()
	repoName := "for-unittest" // 使用一个已知存在的测试仓库

	files, err := fileContentService.GetRepositoryFileContents(ctx, repoName)

	// 验证结果
	if err != nil {
		// 如果仓库不存在，这是预期的错误
		if err.Error() == "failed to get tree for main branch: GET https://api.github.com/repos/NextSpace-coder/for-unittest/git/trees/main?recursive=true: 404 Not Found []" {
			t.Logf("Repository %s/%s does not exist, which is expected for this test", cfg.GitHub.Owner, repoName)
			return
		}
		require.NoError(t, err, "GetRepositoryFileContents should not return error for existing repository")
	}

	// 如果成功获取到文件，验证结果
	if files != nil {
		assert.IsType(t, []actualGitHub.File{}, files, "Result should be a slice of File")
		t.Logf("Successfully retrieved %d files from repository %s/%s", len(files), cfg.GitHub.Owner, repoName)

		// 打印前几个文件的信息
		for i, file := range files {
			if i >= 5 { // 只打印前3个文件
				break
			}
			t.Logf("File %d: %s (Binary: %t, Content length: %d)", i+1, file.Name, file.Binary, len(file.Contents))
		}
	}
}

// TestFileContentServiceWithNonExistentRepo 测试使用不存在的仓库
func TestFileContentServiceWithNonExistentRepo(t *testing.T) {
	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping test: Could not load config for env 'dev': %v", err)
	}

	if cfg.GitHub.Token == "" || cfg.GitHub.Owner == "" {
		t.Skip("Skipping test: GITHUB_TOKEN and GITHUB_OWNER must be available")
	}

	// 创建 GitHub 服务
	ghService := actualGitHub.NewService(cfg.GitHub.Token)

	// 创建 FileContentService
	fileContentService := github_service.NewFileContentService(ghService, cfg)

	// 测试获取不存在的仓库文件内容
	ctx := context.Background()
	nonExistentRepoName := "non-existent-repo-" + t.Name()

	_, err = fileContentService.GetRepositoryFileContents(ctx, nonExistentRepoName)

	// 应该返回错误
	require.Error(t, err, "Should return error for non-existent repository")
	assert.Contains(t, err.Error(), "failed to get tree for main:", "Error should indicate tree not found")
}

// TestGetRepositoryFileContentsInDirectory 测试 GetRepositoryFileContentsInDirectory 方法
func TestGetRepositoryFileContentsInDirectory(t *testing.T) {
	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping test: Could not load config for env 'dev': %v", err)
	}

	if cfg.GitHub.Token == "" || cfg.GitHub.Owner == "" {
		t.Skip("Skipping test: GITHUB_TOKEN and GITHUB_OWNER must be available")
	}

	// 创建 GitHub 服务
	ghService := actualGitHub.NewService(cfg.GitHub.Token)

	// 创建 FileContentService
	fileContentService := github_service.NewFileContentService(ghService, cfg)

	// 测试获取仓库 migrations 目录下的文件内容
	ctx := context.Background()
	repoName := "for-unittest" // 使用一个已知存在的测试仓库
	dir := "public"

	files, err := fileContentService.GetRepositoryFileContentsInDirectory(ctx, repoName, dir)

	// 验证结果
	if err != nil {
		// 如果目录不存在，这是预期的错误
		t.Logf("GetRepositoryFileContentsInDirectory error: %v", err)
		return
	}

	// 如果成功获取到文件，验证结果
	if files != nil {
		assert.IsType(t, []actualGitHub.File{}, files, "Result should be a slice of File")
		t.Logf("Successfully retrieved %d files from directory '%s' in repository %s/%s", len(files), dir, cfg.GitHub.Owner, repoName)

		// 打印前几个文件的信息
		for i, file := range files {
			if i >= 5 {
				break
			}
			t.Logf("File %d: %s (Binary: %t, Content length: %d)", i+1, file.Name, file.Binary, len(file.Contents))
		}
	}
}

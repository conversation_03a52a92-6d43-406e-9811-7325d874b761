package github_service_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	gh "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	appgh "github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	infragh "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
)

var (
	ghToken       string
	testRepoOwner string
)

func setupTestCommitService(t *testing.T) (*appgh.CodeCommitService, *infragh.Service, string) {
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v. Ensure conf/config_dev.yaml is set or required env vars are present.", err)
	}

	ghToken = cfg.GitHub.Token
	testRepoOwner = cfg.GitHub.Owner

	if ghToken == "" || testRepoOwner == "" {
		t.Skip("Skipping integration test: GitHub token and owner must be configured in conf/config_dev.yaml.")
	}

	infraSvc := infragh.NewService(ghToken)
	require.NotNil(t, infraSvc, "Infrastructure GitHub service should not be nil")

	appService := appgh.NewCodeCommitService(infraSvc)
	require.NotNil(t, appService, "Application CodeCommitService should not be nil")

	return appService, infraSvc, testRepoOwner
}

func createTempRepo(t *testing.T, ctx context.Context, ghService *infragh.Service, owner string) *gh.Repository {
	t.Helper()
	repoName := fmt.Sprintf("test-app-temp-repo-%d", time.Now().UnixNano())
	repoToCreate := &gh.Repository{
		Name:     gh.Ptr(repoName),
		Private:  gh.Ptr(true),
		AutoInit: gh.Ptr(true),
	}

	createdRepo, resp, err := ghService.Repositories.CreateUserRepository(ctx, repoToCreate)
	require.NoError(t, err, "Failed to create temporary repository")
	require.NotNil(t, resp, "Response from creating temp repo should not be nil")
	require.Equal(t, http.StatusCreated, resp.StatusCode, "Expected status 201 for temp repo creation")
	require.NotNil(t, createdRepo, "Created temporary repository should not be nil")
	t.Logf("Successfully created temporary repository %s/%s", owner, createdRepo.GetName())

	t.Cleanup(func() {
		_, delErr := ghService.Repositories.DeleteRepository(ctx, owner, createdRepo.GetName())
		if delErr != nil {
			if ghErr, ok := delErr.(*gh.ErrorResponse); ok && ghErr.Response != nil && ghErr.Response.StatusCode == http.StatusNotFound {
				t.Logf("Temporary repository %s/%s was already deleted or not found.", owner, createdRepo.GetName())
			} else {
				t.Logf("Warning: failed to delete temporary repository %s/%s: %v", owner, createdRepo.GetName(), delErr)
			}
		} else {
			t.Logf("Successfully deleted temporary repository %s/%s", owner, createdRepo.GetName())
		}
	})

	return createdRepo
}

func TestCodeCommitService_CommitChanges(t *testing.T) {
	appService, infraService, currentOwner := setupTestCommitService(t)
	ctx := context.Background()

	tempRepo := createTempRepo(t, ctx, infraService, currentOwner)
	testRepoName := tempRepo.GetName()

	testBranchName := fmt.Sprintf("test-commit-changes-%d", time.Now().UnixNano())
	mainBranchName := "main"

	mainBranchRef, _, err := infraService.References.GetReference(ctx, currentOwner, testRepoName, "refs/heads/"+mainBranchName)
	require.NoError(t, err, "Failed to get main branch reference from temp repo")
	require.NotNil(t, mainBranchRef, "Main branch reference from temp repo should not be nil")
	require.NotNil(t, mainBranchRef.Object, "Main branch reference object from temp repo should not be nil")
	require.NotNil(t, mainBranchRef.Object.SHA, "Main branch reference SHA from temp repo should not be nil")

	_, _, err = infraService.References.CreateReference(ctx, currentOwner, testRepoName, &gh.Reference{
		Ref:    gh.Ptr("refs/heads/" + testBranchName),
		Object: &gh.GitObject{SHA: mainBranchRef.Object.SHA},
	})
	require.NoError(t, err, "Failed to create test branch '%s' in temp repo", testBranchName)
	t.Logf("Successfully created test branch: %s in repo %s/%s", testBranchName, currentOwner, testRepoName)

	t.Cleanup(func() {
		_, errDelBranch := infraService.References.DeleteReference(ctx, currentOwner, testRepoName, "refs/heads/"+testBranchName)
		if errDelBranch != nil {
			t.Logf("Warning: failed to delete test branch '%s' from repo %s/%s: %v", testBranchName, currentOwner, testRepoName, errDelBranch)
		} else {
			t.Logf("Successfully deleted test branch: %s from repo %s/%s", testBranchName, currentOwner, testRepoName)
		}
	})

	// --- 第一阶段：创建文件 ---
	fileToDeletePath := "file-to-delete-on-temp.txt"
	commitReq1 := &dto.CommitRequest{
		Owner:         currentOwner,
		Repository:    testRepoName,
		Branch:        testBranchName,
		CommitMessage: "feat: Add initial files including one to be deleted later",
		Files: []dto.FileContent{
			{Path: "test-file-1.txt", Content: "Hello from TestCodeCommitService in temp repo!"},
			{Path: "test-dir/test-file-2.md", Content: "# Markdown Test in temp repo\nContent here."},
			{Path: fileToDeletePath, Content: "This file will be deleted soon."},
		},
		// DeletedFiles: nil, // 第一次提交不删除任何文件
		AuthorName:  "Test User",
		AuthorEmail: "<EMAIL>",
	}

	newCommit1, err := appService.CommitChanges(ctx, commitReq1)
	require.NoError(t, err, "CommitChanges (Phase 1: create files) failed")
	require.NotNil(t, newCommit1, "Commit (Phase 1) should not be nil")
	assert.NotEmpty(t, newCommit1.GetSHA(), "New commit SHA (Phase 1) should not be empty")
	t.Logf("Successfully committed Phase 1. SHA: %s", newCommit1.GetSHA())

	// 验证文件 fileToDeletePath 是否已创建
	contentCheck1, _, _, err := infraService.Repositories.GetContents(ctx, currentOwner, testRepoName, fileToDeletePath, &gh.RepositoryContentGetOptions{Ref: testBranchName})
	require.NoError(t, err, "Failed to get content of %s after Phase 1 commit", fileToDeletePath)
	require.NotNil(t, contentCheck1, "%s should exist after Phase 1 commit", fileToDeletePath)
	actualContentCheck1, _ := contentCheck1.GetContent()
	assert.Equal(t, "This file will be deleted soon.", actualContentCheck1, "Content of %s mismatch after Phase 1", fileToDeletePath)

	// --- 第二阶段：删除文件并可能添加/更新其他文件 ---
	commitReq2 := &dto.CommitRequest{
		Owner:         currentOwner,
		Repository:    testRepoName,
		Branch:        testBranchName,
		CommitMessage: "feat: Delete a file and update another",
		Files: []dto.FileContent{
			// 可以选择在此处更新现有文件或添加新文件
			{Path: "test-file-1.txt", Content: "Hello again from TestCodeCommitService! Updated content."},
		},
		DeletedFiles: []string{fileToDeletePath}, // 指定要删除的文件
		AuthorName:   "Test User",
		AuthorEmail:  "<EMAIL>",
	}

	newCommit2, err := appService.CommitChanges(ctx, commitReq2)
	require.NoError(t, err, "CommitChanges (Phase 2: delete file) failed")
	require.NotNil(t, newCommit2, "Commit (Phase 2) should not be nil")
	assert.NotEmpty(t, newCommit2.GetSHA(), "New commit SHA (Phase 2) should not be empty")
	assert.NotEqual(t, newCommit1.GetSHA(), newCommit2.GetSHA(), "SHA of Phase 1 and Phase 2 commits should be different")
	t.Logf("Successfully committed Phase 2. SHA: %s", newCommit2.GetSHA())

	// 验证分支是否指向新的 commit
	time.Sleep(5 * time.Second)
	branchAfterPhase2, _, err := infraService.Branches.GetBranch(ctx, currentOwner, testRepoName, testBranchName, 1)
	require.NoError(t, err, "Failed to get test branch after Phase 2 commit")
	require.NotNil(t, branchAfterPhase2.GetCommit().GetSHA(), "Test branch commit SHA is nil after Phase 2 commit")
	assert.Equal(t, newCommit2.GetSHA(), branchAfterPhase2.GetCommit().GetSHA(), "Branch HEAD does not point to the Phase 2 commit")

	// 验证被删除的文件是否真的不存在了
	_, _, resp, errGetDeleted := infraService.Repositories.GetContents(ctx, currentOwner, testRepoName, fileToDeletePath, &gh.RepositoryContentGetOptions{Ref: testBranchName})
	if errGetDeleted != nil {
		ghErr, ok := errGetDeleted.(*gh.ErrorResponse)
		require.True(t, ok, "Error getting deleted file was not a *gh.ErrorResponse: %v", errGetDeleted)
		assert.Equal(t, http.StatusNotFound, ghErr.Response.StatusCode, "Expected 404 for deleted file after Phase 2, but got other error")
	} else {
		t.Errorf("%s was expected to be deleted, but GetContents returned no error after Phase 2", fileToDeletePath)
	}
	if resp != nil && resp.StatusCode != http.StatusNotFound {
		t.Logf("Response status code for deleted file check after Phase 2: %d", resp.StatusCode)
	}

	// 验证更新的文件内容
	updatedFileContent, _, _, err := infraService.Repositories.GetContents(ctx, currentOwner, testRepoName, "test-file-1.txt", &gh.RepositoryContentGetOptions{Ref: testBranchName})
	require.NoError(t, err, "Failed to get content of updated test-file-1.txt")
	require.NotNil(t, updatedFileContent, "Content of updated test-file-1.txt should not be nil")
	actualUpdatedContent, _ := updatedFileContent.GetContent()
	assert.Equal(t, "Hello again from TestCodeCommitService! Updated content.", actualUpdatedContent, "Content of updated test-file-1.txt mismatch")
}

package cloudflare_service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
)

// setupTestKVService 创建测试用的 KV 应用服务实例
func setupTestKVService(t *testing.T) (context.Context, *cloudflare_service.WorkerKVService, *config.Config) {
	t.Helper()

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v", err)
	}

	// 检查必要的配置
	if cfg.Cloudflare.APIKey == "" || cfg.Cloudflare.APIEmail == "" || cfg.Cloudflare.AccountID == "" {
		t.Skip("Skipping integration test: Cloudflare API credentials not configured")
	}

	if cfg.Cloudflare.KVNamespaceID == "" || cfg.Cloudflare.KVNamespaceID == "your-kv-namespace-id-here" {
		t.Skip("Skipping integration test: KV namespace ID not configured")
	}

	// 创建基础设施服务
	cloudflareInfra, err := cloudflare.NewService(
		cfg.Cloudflare.APIKey,
		cfg.Cloudflare.APIEmail,
		cfg.Cloudflare.AccountID,
	)
	require.NoError(t, err)

	// 创建应用服务
	kvService := cloudflare_service.NewWorkerKVService(cloudflareInfra, cfg)

	return context.Background(), kvService, cfg
}

// TestCreateOrUpdateURLConfig 测试 URL 配置的创建和更新功能
func TestCreateOrUpdateURLConfig(t *testing.T) {
	// 只在集成测试环境中运行
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx, kvService, _ := setupTestKVService(t)

	t.Run("CreateNewURLConfig", func(t *testing.T) {
		// 使用唯一的 key
		testKey := fmt.Sprintf("test-create-%d", time.Now().UnixNano())
		previewURL := "https://preview.example.com"
		publishURL := "https://publish.example.com"

		// 设置清理函数，确保测试完成后删除创建的键值对
		t.Cleanup(func() {
			_ = kvService.DeleteKeyValuePair(ctx, testKey)
		})

		// 创建新的 URL 配置
		err := kvService.CreateOrUpdateURLConfig(ctx, testKey, &previewURL, &publishURL)
		assert.NoError(t, err, "Should be able to create new URL config")

		// 注意：由于 Cloudflare KV 的最终一致性，我们不能立即读取刚写入的数据
		// 这个测试主要验证创建操作成功，而不是验证立即读取的结果
		// 在生产环境中，应该考虑 KV 的最终一致性特性
	})

	t.Run("UpdateOperationsWork", func(t *testing.T) {
		// 使用唯一的 key 来避免测试间的干扰
		testKey := fmt.Sprintf("test-update-%d", time.Now().UnixNano())
		previewURL := "https://preview.example.com"
		publishURL := "https://publish.example.com"
		updatedPreviewURL := "https://updated-preview.example.com"
		updatedPublishURL := "https://updated-publish.example.com"

		// 设置清理函数，确保测试完成后删除创建的键值对
		t.Cleanup(func() {
			_ = kvService.DeleteKeyValuePair(ctx, testKey)
		})

		// 创建初始配置
		err := kvService.CreateOrUpdateURLConfig(ctx, testKey, &previewURL, &publishURL)
		require.NoError(t, err, "Should be able to create initial config")

		// 只更新 preview URL
		err = kvService.CreateOrUpdateURLConfig(ctx, testKey, &updatedPreviewURL, nil)
		assert.NoError(t, err, "Should be able to update preview URL only")

		// 只更新 publish URL
		err = kvService.CreateOrUpdateURLConfig(ctx, testKey, nil, &updatedPublishURL)
		assert.NoError(t, err, "Should be able to update publish URL only")

		// 同时更新两个 URL
		finalPreviewURL := "https://final-preview.example.com"
		finalPublishURL := "https://final-publish.example.com"
		err = kvService.CreateOrUpdateURLConfig(ctx, testKey, &finalPreviewURL, &finalPublishURL)
		assert.NoError(t, err, "Should be able to update both URLs")

		// 注意：由于 Cloudflare KV 的最终一致性，我们主要验证操作成功
		// 而不是立即验证读取结果
	})

	t.Run("CreateWithPartialData", func(t *testing.T) {
		// 使用时间戳确保 key 唯一性
		partialKey := fmt.Sprintf("test-partial-config-%d", time.Now().UnixNano())
		onlyPreviewURL := "https://only-preview.example.com"

		// 设置清理函数，确保测试完成后删除创建的键值对
		t.Cleanup(func() {
			_ = kvService.DeleteKeyValuePair(ctx, partialKey)
		})

		// 只创建 preview URL
		err := kvService.CreateOrUpdateURLConfig(ctx, partialKey, &onlyPreviewURL, nil)
		assert.NoError(t, err, "Should be able to create config with only preview URL")

		// 由于 Cloudflare KV 的最终一致性，我们不能立即读取刚写入的数据
		// 这个测试主要验证操作成功，而不是验证立即读取的结果

		// 后续添加 publish URL
		// 注意：由于 Cloudflare KV 的最终一致性，这个操作可能会创建新配置而不是更新现有配置
		// 这是正常的 KV 行为，在生产环境中应该考虑这种情况
		onlyPublishURL := "https://only-publish.example.com"
		err = kvService.CreateOrUpdateURLConfig(ctx, partialKey, nil, &onlyPublishURL)
		assert.NoError(t, err, "Should be able to add publish URL (may create new config due to eventual consistency)")
	})

	t.Run("NoUpdateWhenBothNil", func(t *testing.T) {
		noUpdateKey := fmt.Sprintf("test-no-update-%d", time.Now().UnixNano())
		initialPreview := "https://initial-preview.example.com"
		initialPublish := "https://initial-publish.example.com"

		// 设置清理函数，确保测试完成后删除创建的键值对
		t.Cleanup(func() {
			_ = kvService.DeleteKeyValuePair(ctx, noUpdateKey)
		})

		// 创建初始配置
		err := kvService.CreateOrUpdateURLConfig(ctx, noUpdateKey, &initialPreview, &initialPublish)
		require.NoError(t, err, "Should be able to create initial config")

		// 尝试"更新"但不传入任何值
		err = kvService.CreateOrUpdateURLConfig(ctx, noUpdateKey, nil, nil)
		assert.NoError(t, err, "Should not error when both URLs are nil")

		// 验证配置没有改变
		value, err := kvService.ReadKeyValuePair(ctx, noUpdateKey)
		require.NoError(t, err, "Should be able to read the unchanged config")

		expected := `{"previewUrl":"https://initial-preview.example.com","publishUrl":"https://initial-publish.example.com"}`
		assert.JSONEq(t, expected, value, "Config should remain unchanged when both URLs are nil")
	})
}

// TestDeleteKeyValuePair 测试删除键值对功能
func TestDeleteKeyValuePair(t *testing.T) {
	// 只在集成测试环境中运行
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx, kvService, _ := setupTestKVService(t)

	t.Run("DeleteExistingKey", func(t *testing.T) {
		// 使用唯一的 key
		testKey := fmt.Sprintf("test-delete-%d", time.Now().UnixNano())
		previewURL := "https://preview-to-delete.example.com"
		publishURL := "https://publish-to-delete.example.com"

		// 设置清理函数，确保测试完成后删除创建的键值对（防止删除操作失败）
		t.Cleanup(func() {
			_ = kvService.DeleteKeyValuePair(ctx, testKey)
		})

		// 先创建一个键值对
		err := kvService.CreateOrUpdateURLConfig(ctx, testKey, &previewURL, &publishURL)
		require.NoError(t, err, "Should be able to create URL config")

		// 验证键值对存在
		value, err := kvService.ReadKeyValuePair(ctx, testKey)
		require.NoError(t, err, "Should be able to read the created config")
		assert.NotEmpty(t, value, "Config should not be empty")

		// 删除键值对
		err = kvService.DeleteKeyValuePair(ctx, testKey)
		assert.NoError(t, err, "Should be able to delete key-value pair")

		// 注意：由于 Cloudflare KV 的最终一致性和缓存机制，
		// 删除操作可能不会立即生效，读取操作可能仍然返回数据
		// 这是 Cloudflare KV 的正常行为，不是代码错误
		// 在生产环境中，应该考虑这种延迟
	})

	t.Run("DeleteNonExistentKey", func(t *testing.T) {
		// 尝试删除不存在的键
		nonExistentKey := fmt.Sprintf("non-existent-key-%d", time.Now().UnixNano())

		// 删除不存在的键应该不会报错（Cloudflare KV 的行为）
		err := kvService.DeleteKeyValuePair(ctx, nonExistentKey)
		assert.NoError(t, err, "Deleting non-existent key should not error")
	})
}

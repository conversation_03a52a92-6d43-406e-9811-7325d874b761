package cloudflare_service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	infraCloudflare "github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
)

// setupInitCloudflarePagesTest 是一个辅助函数，用于为 InitCloudflarePages 测试设置服务。
func setupInitCloudflarePagesTest(t *testing.T) (context.Context, *cloudflare_service.InitCloudflarePagesService, *infraGitHub.Service, *infraCloudflare.Service, *config.Config) {
	t.Helper()
	ctx := context.Background()

	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Fatalf("未能加载 'dev' 环境的配置: %v。请确保 conf/config_dev.yaml 文件存在。", err)
	}

	if cfg.Cloudflare.APIKey == "" || cfg.Cloudflare.APIEmail == "" || cfg.Cloudflare.AccountID == "" {
		t.Skip("跳过集成测试：必须配置 CLOUDFLARE_API_KEY, CLOUDFLARE_API_EMAIL, 和 CLOUDFLARE_ACCOUNT_ID。")
	}
	if cfg.GitHub.Token == "" || cfg.GitHub.Owner == "" {
		t.Skip("跳过集成测试：必须配置 GITHUB_TOKEN 和 GITHUB_OWNER。")
	}

	cloudflareInfra, err := infraCloudflare.NewService(
		cfg.Cloudflare.APIKey,
		cfg.Cloudflare.APIEmail,
		cfg.Cloudflare.AccountID,
	)
	require.NoError(t, err, "未能创建 Cloudflare 基础设施服务")

	githubInfra := infraGitHub.NewService(cfg.GitHub.Token)

	initPagesSvc := cloudflare_service.NewInitCloudflarePagesService(cloudflareInfra, cfg)

	return ctx, initPagesSvc, githubInfra, cloudflareInfra, cfg
}

func TestInitCloudflarePagesCore_SuccessfulInitialization(t *testing.T) {
	ctx, initPagesService, _, cloudflareInfra, cfg := setupInitCloudflarePagesTest(t)

	// 使用固定的测试仓库
	testRepoName := "for-unittest"

	// 为本次测试创建一个临时的项目名称
	projectName := fmt.Sprintf("unittest-pages-%d", time.Now().UnixNano())

	var createdProjectID string // 用于 Cloudflare Pages 项目清理
	t.Cleanup(func() {
		if createdProjectID != "" {
			t.Logf("清理 Cloudflare Pages 项目 ID: %s", createdProjectID)
			err := cloudflareInfra.Project.DeleteProject(ctx, projectName)
			if err != nil {
				t.Logf("清理 Cloudflare Pages 项目失败: %v", err)
			} else {
				t.Logf("成功清理 Cloudflare Pages 项目: %s", projectName)
			}
		}
	})

	// 执行服务方法
	returnedProjectID, err := initPagesService.InitCloudflarePagesCore(
		ctx,
		projectName,
		"npm run build", // 构建命令
		"dist",          // 构建输出目录
		testRepoName,
	)
	createdProjectID = returnedProjectID // 存储用于清理

	// 断言
	require.NoError(t, err, "InitCloudflarePagesCore 在成功初始化时不应返回错误")
	require.NotEmpty(t, returnedProjectID, "InitCloudflarePagesCore 应返回一个非空的项目 ID")

	// 验证项目是否已创建
	project, err := cloudflareInfra.Project.GetProjectByName(ctx, projectName)
	require.NoError(t, err, "从 Cloudflare 获取项目信息失败")
	require.NotNil(t, project, "获取的项目信息不应为 nil")
	assert.Equal(t, projectName, project.Name, "项目名称不匹配")
	assert.Equal(t, "npm run build", project.BuildConfig.BuildCommand, "构建命令不匹配")
	assert.Equal(t, "dist", project.BuildConfig.DestinationDir, "构建输出目录不匹配")
	assert.Equal(t, "github", project.Source.Type, "源代码类型不匹配")
	assert.Equal(t, cfg.GitHub.Owner, project.Source.Config.Owner, "GitHub 仓库所有者不匹配")
	assert.Equal(t, testRepoName, project.Source.Config.RepoName, "GitHub 仓库名称不匹配")
	assert.Equal(t, "main", project.Source.Config.ProductionBranch, "生产环境分支不匹配")

	t.Logf("成功初始化 Cloudflare Pages 项目: %s，项目名称: %s，仓库: %s", returnedProjectID, projectName, testRepoName)
}

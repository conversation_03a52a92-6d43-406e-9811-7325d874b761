package cloudflare_service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	cloudflareService "github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

func TestMain(m *testing.M) {
	logger.InitLogger("debug", "test")
	m.Run()
}

// setupTestService 设置测试环境并返回测试所需的服务实例。
func setupTestService(t *testing.T) (context.Context, *cloudflareService.NotificationService) {
	t.Helper()

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	require.NoError(t, err, "Failed to load config")

	// 验证必要的配置
	require.NotEmpty(t, cfg.Cloudflare.APIKey, "Cloudflare API key is required")
	require.NotEmpty(t, cfg.Cloudflare.APIEmail, "Cloudflare API email is required")
	require.NotEmpty(t, cfg.Cloudflare.AccountID, "Cloudflare account ID is required")
	require.NotEmpty(t, cfg.Cloudflare.WebhookID, "Cloudflare webhook ID is required")

	// 创建 Cloudflare 基础设施服务
	cloudflareInfraService, err := cloudflare.NewService(
		cfg.Cloudflare.APIKey,
		cfg.Cloudflare.APIEmail,
		cfg.Cloudflare.AccountID,
	)
	require.NoError(t, err, "Failed to create Cloudflare service")

	// 创建通知服务
	notificationService := cloudflareService.NewNotificationService(cloudflareInfraService, cfg)

	return context.Background(), notificationService
}

func TestNotificationService_CreateAndDeletePolicy(t *testing.T) {
	ctx, service := setupTestService(t)

	// 测试项目名称
	projectName := "for-unittest"

	// 创建通知策略
	policyID, err := service.CreateNotificationPolicy(ctx, projectName)
	require.NoError(t, err, "Failed to create notification policy")
	require.NotEmpty(t, policyID, "Policy ID should not be empty")

	// 清理：删除创建的通知策略
	t.Cleanup(func() {
		err := service.DeleteNotificationPolicy(ctx, policyID)
		require.NoError(t, err, "Failed to delete notification policy")
	})
}

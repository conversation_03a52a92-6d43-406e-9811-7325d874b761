package fly_service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/application/fly_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
)

// mockProjectStatusRepository 是一个模拟的项目状态仓库
type mockProjectStatusRepository struct{}

func (m *mockProjectStatusRepository) CreateOrUpdate(ctx context.Context, projectStatus *entity.ProjectStatus) error {
	return nil
}

func (m *mockProjectStatusRepository) GetByProjectID(ctx context.Context, projectID string) (*entity.ProjectStatus, error) {
	return &entity.ProjectStatus{
		ProjectID: projectID,
		Status:    entity.ProjectStatusStarted,
	}, nil
}

func (m *mockProjectStatusRepository) DeleteByProjectID(ctx context.Context, projectID string) error {
	return nil
}

func (m *mockProjectStatusRepository) GetByNetlifySiteID(ctx context.Context, netlifySiteID string) (*entity.ProjectStatus, error) {
	return nil, nil
}

func (m *mockProjectStatusRepository) UpdateByNetlifySiteID(ctx context.Context, netlifySiteID string, updates map[string]any) (*entity.ProjectStatus, error) {
	return nil, nil
}

func (m *mockProjectStatusRepository) UpdateByProjectID(ctx context.Context, projectID string, updates map[string]any) (*entity.ProjectStatus, error) {
	return &entity.ProjectStatus{
		ProjectID: projectID,
		Status:    entity.ProjectStatusInProgress,
	}, nil
}

func setupFlyAppMachineService(t *testing.T) (*fly_service.FlyAppMachineService, *actualFly.Service, string) {
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v.", err)
	}

	apiToken := cfg.Fly.APIToken
	orgSlug := cfg.Fly.OrgSlug
	if apiToken == "" || orgSlug == "" {
		t.Skip("Skipping integration test: FLY_API_TOKEN and FLY_ORG_SLUG must be available.")
	}

	flySvc, err := actualFly.NewService(cfg)
	require.NoError(t, err, "Failed to create Fly.io service")
	mockRepo := &mockProjectStatusRepository{}
	service := fly_service.NewFlyAppMachineService(flySvc, cfg, mockRepo)
	return service, flySvc, orgSlug
}

// TestCreateAppAndMachine 测试一站式创建 app 和 machine
func TestCreateAppAndMachine(t *testing.T) {
	service, flyInfra, _ := setupFlyAppMachineService(t)
	ctx := context.Background()
	appName := "unittest-app-machine-" + fmt.Sprintf("%d", time.Now().UnixNano())

	machine, err := service.CreateAppAndMachine(ctx, appName)
	require.NoError(t, err, "CreateAppAndMachine should not return error")
	require.NotNil(t, machine, "CreateAppAndMachine should return a non-nil machine")
	require.NotEmpty(t, machine.Id, "Machine ID should not be empty")
	require.NotEmpty(t, machine.Name, "Machine name should not be empty")
	require.NotEmpty(t, machine.Config, "Machine config should not be empty")

	// 用 t.Cleanup 注册清理逻辑
	t.Cleanup(func() {
		if machine.Id != nil {
			_ = flyInfra.Machines.DeleteMachine(ctx, appName, *machine.Id, true)
		}
		_ = flyInfra.Apps.DeleteApp(ctx, appName)
	})
}

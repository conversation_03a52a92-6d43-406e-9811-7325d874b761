package workflow_service

import (
	"testing"

	"github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/fileutils"
)

func TestIsMigrationFile(t *testing.T) {
	tests := []struct {
		name     string
		filePath string
		want     bool
	}{
		{
			name:     "标准migration文件",
			filePath: "migration/001_initial.sql",
			want:     true,
		},
		{
			name:     "migrations目录文件",
			filePath: "migrations/002_users.sql",
			want:     true,
		},
		{
			name:     "子目录migration文件",
			filePath: "migration/auth/003_roles.sql",
			want:     true,
		},
		{
			name:     "非SQL文件",
			filePath: "migration/README.md",
			want:     false,
		},
		{
			name:     "非migration目录",
			filePath: "src/database/schema.sql",
			want:     false,
		},
		{
			name:     "根目录SQL文件",
			filePath: "database.sql",
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := workflow_service.IsMigrationFile(tt.filePath)
			if got != tt.want {
				t.Errorf("IsMigrationFile(%v) = %v, want %v", tt.filePath, got, tt.want)
			}
		})
	}
}

func TestMigrationProcessorService_ProcessContentWithMigrationCheck(t *testing.T) {
	service := workflow_service.NewMigrationProcessorService()

	tests := []struct {
		name             string
		fileNames        []string
		content          string
		replacementRules map[bool][]fileutils.ReplaceRule
		wantNeedsUpdate  bool
		wantContent      string
	}{
		{
			name:      "有migration文件",
			fileNames: []string{"src/main.go", "migration/001_init.sql", "README.md"},
			content:   "Database setup: {{DB_SETUP}}",
			replacementRules: map[bool][]fileutils.ReplaceRule{
				true:  {{Old: "{{DB_SETUP}}", New: "需要运行迁移"}},
				false: {{Old: "{{DB_SETUP}}", New: "无需迁移"}},
			},
			wantNeedsUpdate: true,
			wantContent:     "Database setup: 需要运行迁移",
		},
		{
			name:      "无migration文件",
			fileNames: []string{"src/main.go", "README.md", "package.json"},
			content:   "Database setup: {{DB_SETUP}}",
			replacementRules: map[bool][]fileutils.ReplaceRule{
				true:  {{Old: "{{DB_SETUP}}", New: "需要运行迁移"}},
				false: {{Old: "{{DB_SETUP}}", New: "无需迁移"}},
			},
			wantNeedsUpdate: false,
			wantContent:     "Database setup: 无需迁移",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ProcessContentWithMigrationCheck(
				tt.fileNames,
				tt.content,
				tt.replacementRules,
			)
			if err != nil {
				t.Errorf("ProcessContentWithMigrationCheck() error = %v", err)
				return
			}
			if result.NeedsUpdate != tt.wantNeedsUpdate {
				t.Errorf("ProcessContentWithMigrationCheck() NeedsUpdate = %v, want %v", result.NeedsUpdate, tt.wantNeedsUpdate)
			}
			if result.ProcessedContent != tt.wantContent {
				t.Errorf("ProcessContentWithMigrationCheck() ProcessedContent = %v, want %v", result.ProcessedContent, tt.wantContent)
			}
		})
	}
}

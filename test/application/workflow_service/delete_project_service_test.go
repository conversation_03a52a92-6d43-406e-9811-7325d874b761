//go:build integration
// +build integration

package workflow_service_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	infraFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

func setupDeleteProjectService(t *testing.T) (*workflow_service.DeleteProjectService, repository.ProjectRepository, repository.ProjectStatusRepository) {
	// 初始化logger
	logger.InitLogger("info", "dev")

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		t.Skipf("Skipping integration test: Could not load config for env 'dev': %v.", err)
	}

	// 检查必要的配置
	if cfg.Fly.APIToken == "" || cfg.Fly.OrgSlug == "" {
		t.Skip("Skipping integration test: FLY_API_TOKEN and FLY_ORG_SLUG must be available.")
	}
	if cfg.Netlify.Token == "" {
		t.Skip("Skipping integration test: NETLIFY_TOKEN must be available.")
	}

	// 初始化基础设施服务
	flyService, err := infraFly.NewService(cfg)
	require.NoError(t, err, "Failed to create Fly.io service")

	netlifyService, err := infraNetlify.NewService(cfg.Netlify.Token)
	require.NoError(t, err, "Failed to create Netlify service")

	// 初始化数据库客户端和仓库
	supabaseClient, err := supabase.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	require.NoError(t, err, "Failed to create Supabase client")

	projectRepo := supabase.NewSupabaseProjectRepository(supabaseClient)
	projectStatusRepo := supabase.NewSupabaseProjectStatusRepository(supabaseClient)

	// 创建删除项目服务
	deleteProjectService := workflow_service.NewDeleteProjectService(
		projectRepo,
		projectStatusRepo,
		flyService,
		netlifyService,
	)

	return deleteProjectService, projectRepo, projectStatusRepo
}

// TestDeleteProject_RealProject 测试删除真实存在的项目
func TestDeleteProject_RealProject(t *testing.T) {
	deleteProjectService, projectRepo, projectStatusRepo := setupDeleteProjectService(t)
	ctx := context.Background()

	// 使用真实存在的项目ID
	realProjectID := "b852dbfb-23e0-41cb-b574-6d56dd55ee93"

	// 先获取项目信息
	project, err := projectRepo.GetByID(ctx, realProjectID)
	if err != nil {
		t.Skipf("Skipping test: Project %s does not exist: %v", realProjectID, err)
	}

	t.Logf("Found real project: ID=%s, Title=%s, NetlifySiteID=%s", project.ID, project.Title, project.NetlifySiteID)

	// 执行删除操作
	result, err := deleteProjectService.DeleteProject(ctx, realProjectID)

	// 检查删除结果
	require.NoError(t, err, "DeleteProject should not return error")
	assert.NotNil(t, result, "Result should not be nil")
	assert.Equal(t, realProjectID, result.ProjectID)

	// 检查删除结果
	assert.True(t, result.DeletionResults.ProjectRecordDeleted, "Project record should be deleted")
	assert.True(t, result.DeletionResults.ProjectStatusDeleted, "Project status should be deleted")

	// 记录删除结果
	t.Logf("Delete result: %+v", result.DeletionResults)
	t.Logf("Errors: %v", result.Errors)

	// 验证项目确实被删除了
	_, err = projectRepo.GetByID(ctx, realProjectID)
	assert.Error(t, err, "Project should not exist after deletion")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project not found")

	// 验证项目状态确实被删除了
	_, err = projectStatusRepo.GetByProjectID(ctx, realProjectID)
	assert.Error(t, err, "Project status should not exist after deletion")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project status not found")

	t.Logf("Successfully deleted project %s", realProjectID)
}

// TestDeleteProject_NonExistentProject 测试删除不存在的项目
func TestDeleteProject_NonExistentProject(t *testing.T) {
	deleteProjectService, _, _ := setupDeleteProjectService(t)
	ctx := context.Background()

	// 生成一个不存在的项目ID
	testProjectID := uuid.NewString()

	// 测试删除不存在的项目
	result, err := deleteProjectService.DeleteProject(ctx, testProjectID)

	// 应该返回错误，因为项目不存在
	require.Error(t, err, "Should return error for non-existent project")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project not found")

	// 结果应该包含错误信息
	assert.NotNil(t, result, "Result should not be nil")
	assert.Equal(t, testProjectID, result.ProjectID)
	assert.Len(t, result.Errors, 1, "Should have one error")
	assert.Contains(t, result.Errors[0], "Failed to get project", "Error should be about getting project")
}

// TestDeleteProject_ErrorHandling 测试错误处理
func TestDeleteProject_ErrorHandling(t *testing.T) {
	deleteProjectService, _, _ := setupDeleteProjectService(t)
	ctx := context.Background()

	// 测试空项目ID
	result, err := deleteProjectService.DeleteProject(ctx, "")

	require.Error(t, err, "Should return error for empty project ID")
	assert.Contains(t, err.Error(), "cannot be empty", "Error should indicate project ID cannot be empty")
	assert.NotNil(t, result, "Result should not be nil")
	assert.Len(t, result.Errors, 1, "Should have one error")
}

// TestDeleteProject_WithTestData 测试删除真实项目（需要手动创建测试数据）
func TestDeleteProject_WithTestData(t *testing.T) {
	deleteProjectService, projectRepo, projectStatusRepo := setupDeleteProjectService(t)
	ctx := context.Background()

	// 创建测试项目
	testProject := &entity.Project{
		UserID:            "test-user-id",
		Title:             "Test Project for Deletion",
		IsPublished:       false,
		SupabaseProjectID: "test-supabase-project",
		RepoID:            "test-repo-id",
		NetlifySiteID:     "", // 暂时不设置Netlify站点ID
	}

	err := projectRepo.Create(ctx, testProject)
	require.NoError(t, err, "Failed to create test project")
	require.NotEmpty(t, testProject.ID, "Project ID should be set after creation")

	// 创建测试项目状态
	testProjectStatus := &entity.ProjectStatus{
		ProjectID:   testProject.ID,
		Status:      entity.ProjectStatusStarted,
		PreviewLink: "https://test-preview.com",
		PublishLink: "https://test-publish.com",
	}

	err = projectStatusRepo.CreateOrUpdate(ctx, testProjectStatus)
	require.NoError(t, err, "Failed to create test project status")

	t.Logf("Created test project with ID: %s", testProject.ID)

	// 执行删除操作
	result, err := deleteProjectService.DeleteProject(ctx, testProject.ID)

	// 检查删除结果
	require.NoError(t, err, "DeleteProject should not return error")
	assert.NotNil(t, result, "Result should not be nil")
	assert.Equal(t, testProject.ID, result.ProjectID)

	// 检查删除结果
	assert.True(t, result.DeletionResults.ProjectRecordDeleted, "Project record should be deleted")
	assert.True(t, result.DeletionResults.ProjectStatusDeleted, "Project status should be deleted")

	// Fly.io和Netlify删除可能失败（因为没有真实的资源），但不应该影响整体流程
	t.Logf("Delete result: %+v", result.DeletionResults)
	t.Logf("Errors: %v", result.Errors)

	// 验证项目确实被删除了
	_, err = projectRepo.GetByID(ctx, testProject.ID)
	assert.Error(t, err, "Project should not exist after deletion")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project not found")

	// 验证项目状态确实被删除了
	_, err = projectStatusRepo.GetByProjectID(ctx, testProject.ID)
	assert.Error(t, err, "Project status should not exist after deletion")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project status not found")
}

// TestDeleteProject_WithNetlifySite 测试删除包含Netlify站点的项目
func TestDeleteProject_WithNetlifySite(t *testing.T) {
	deleteProjectService, projectRepo, projectStatusRepo := setupDeleteProjectService(t)
	ctx := context.Background()

	// 创建测试项目（包含Netlify站点ID）
	testProject := &entity.Project{
		UserID:            "test-user-id",
		Title:             "Test Project with Netlify Site",
		IsPublished:       false,
		SupabaseProjectID: "test-supabase-project",
		RepoID:            "test-repo-id",
		NetlifySiteID:     "test-netlify-site-id", // 设置一个测试的Netlify站点ID
	}

	err := projectRepo.Create(ctx, testProject)
	require.NoError(t, err, "Failed to create test project")
	require.NotEmpty(t, testProject.ID, "Project ID should be set after creation")

	// 创建测试项目状态
	testProjectStatus := &entity.ProjectStatus{
		ProjectID:   testProject.ID,
		Status:      entity.ProjectStatusStarted,
		PreviewLink: "https://test-preview.com",
		PublishLink: "https://test-publish.com",
	}

	err = projectStatusRepo.CreateOrUpdate(ctx, testProjectStatus)
	require.NoError(t, err, "Failed to create test project status")

	t.Logf("Created test project with ID: %s and NetlifySiteID: %s", testProject.ID, testProject.NetlifySiteID)

	// 执行删除操作
	result, err := deleteProjectService.DeleteProject(ctx, testProject.ID)

	// 检查删除结果
	require.NoError(t, err, "DeleteProject should not return error")
	assert.NotNil(t, result, "Result should not be nil")
	assert.Equal(t, testProject.ID, result.ProjectID)

	// 检查删除结果
	assert.True(t, result.DeletionResults.ProjectRecordDeleted, "Project record should be deleted")
	assert.True(t, result.DeletionResults.ProjectStatusDeleted, "Project status should be deleted")

	// Netlify站点删除可能失败（因为站点ID不存在），但不应该影响整体流程
	t.Logf("Delete result: %+v", result.DeletionResults)
	t.Logf("Errors: %v", result.Errors)

	// 验证项目确实被删除了
	_, err = projectRepo.GetByID(ctx, testProject.ID)
	assert.Error(t, err, "Project should not exist after deletion")
	assert.Contains(t, err.Error(), "not found", "Error should indicate project not found")
}

// TestDeleteProject_ConcurrentDeletion 测试并发删除（可选）
func TestDeleteProject_ConcurrentDeletion(t *testing.T) {
	deleteProjectService, projectRepo, projectStatusRepo := setupDeleteProjectService(t)
	ctx := context.Background()

	// 创建测试项目
	testProject := &entity.Project{
		UserID:            "test-user-id",
		Title:             "Test Project for Concurrent Deletion",
		IsPublished:       false,
		SupabaseProjectID: "test-supabase-project",
		RepoID:            "test-repo-id",
	}

	err := projectRepo.Create(ctx, testProject)
	require.NoError(t, err, "Failed to create test project")

	// 创建测试项目状态
	testProjectStatus := &entity.ProjectStatus{
		ProjectID:   testProject.ID,
		Status:      entity.ProjectStatusStarted,
		PreviewLink: "https://test-preview.com",
		PublishLink: "https://test-publish.com",
	}

	err = projectStatusRepo.CreateOrUpdate(ctx, testProjectStatus)
	require.NoError(t, err, "Failed to create test project status")

	t.Logf("Created test project with ID: %s for concurrent deletion test", testProject.ID)

	// 并发执行删除操作
	done := make(chan bool, 2)

	go func() {
		result, err := deleteProjectService.DeleteProject(ctx, testProject.ID)
		t.Logf("Concurrent deletion 1 - Result: %+v, Error: %v", result, err)
		done <- true
	}()

	go func() {
		time.Sleep(100 * time.Millisecond) // 稍微延迟第二个删除操作
		result, err := deleteProjectService.DeleteProject(ctx, testProject.ID)
		t.Logf("Concurrent deletion 2 - Result: %+v, Error: %v", result, err)
		done <- true
	}()

	// 等待两个删除操作完成
	<-done
	<-done

	// 验证项目最终被删除了
	_, err = projectRepo.GetByID(ctx, testProject.ID)
	assert.Error(t, err, "Project should not exist after concurrent deletion")
}

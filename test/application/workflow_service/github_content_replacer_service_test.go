package workflow_service

import (
	"strings"
	"testing"
)

func TestDetectMigrationsInDirectory(t *testing.T) {
	tests := []struct {
		name      string
		fileNames []string
		want      bool
	}{
		{
			name:      "包含migrations目录下的SQL文件",
			fileNames: []string{"src/main.go", "migrations/001_init.sql", "README.md"},
			want:      true,
		},
		{
			name:      "包含migration目录下的SQL文件",
			fileNames: []string{"src/main.go", "migration/002_users.sql", "README.md"},
			want:      true,
		},
		{
			name:      "不包含migrations目录下的SQL文件",
			fileNames: []string{"src/main.go", "README.md", "config.json"},
			want:      false,
		},
		{
			name:      "包含SQL文件但不在migrations目录",
			fileNames: []string{"src/main.go", "database.sql", "README.md"},
			want:      false,
		},
	}

	// 这里我们测试detectMigrationsInDirectory的逻辑
	// 由于它是CopyProjectService的私有方法，我们复制其逻辑进行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := detectMigrationsLogic(tt.fileNames)
			if got != tt.want {
				t.Errorf("detectMigrationsInDirectory() = %v, want %v", got, tt.want)
			}
		})
	}
}

// detectMigrationsLogic 复制CopyProjectService.detectMigrationsInDirectory的逻辑用于测试
func detectMigrationsLogic(fileNames []string) bool {
	for _, fileName := range fileNames {
		if strings.Contains(strings.ToLower(fileName), "migration") && strings.HasSuffix(strings.ToLower(fileName), ".sql") {
			parts := strings.Split(fileName, "/")
			for _, part := range parts {
				if strings.ToLower(part) == "migrations" || strings.ToLower(part) == "migration" {
					return true
				}
			}
		}
	}
	return false
}

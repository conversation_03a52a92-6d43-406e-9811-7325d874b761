package main

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	actualFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// 配置常量
const (
	// INACTIVE_DAYS 不活跃天数阈值
	INACTIVE_DAYS = 3
	// DRY_RUN 是否为试运行
	DRY_RUN = false
)

func main() {
	// 初始化日志
	logger.InitLogger("info", "dev")

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		logger.Error("Failed to load config", "Error", err)
		os.Exit(1)
	}

	// 创建 Fly.io 服务
	flyService, err := actualFly.NewService(cfg)
	if err != nil {
		logger.Error("Failed to create Fly.io service", "Error", err)
		os.Exit(1)
	}

	// 创建上下文
	ctx := context.Background()

	// 自动模式：扫描所有应用并清理不活跃的
	fmt.Printf("Starting auto cleanup mode (inactive threshold: %d days, dry-run: %v)\n", INACTIVE_DAYS, DRY_RUN)
	successApps, failedApps, err := autoCleanupInactiveApps(ctx, flyService, INACTIVE_DAYS, DRY_RUN)
	if err != nil {
		logger.Error("Failed to cleanup Fly apps", "Error", err)
		os.Exit(1)
	}

	// 输出结果
	printCleanupResults(successApps, failedApps, DRY_RUN)
}

// printCleanupResults 打印清理结果
func printCleanupResults(successApps, failedApps []string, dryRun bool) {
	action := "cleaned"
	if dryRun {
		action = "would be cleaned"
	}

	fmt.Printf("\n=== Cleanup Results ===\n")
	fmt.Printf("Total apps processed: %d\n", len(successApps)+len(failedApps))
	fmt.Printf("Successfully %s: %d\n", action, len(successApps))
	fmt.Printf("Failed to clean: %d\n", len(failedApps))

	if len(successApps) > 0 {
		fmt.Printf("Successfully %s apps: %v\n", action, successApps)
	}

	if len(failedApps) > 0 {
		fmt.Printf("Failed to clean apps: %v\n", failedApps)
	}

	if dryRun {
		fmt.Println("This was a dry run - no apps were actually deleted.")
	} else {
		fmt.Println("Cleanup completed.")
	}
}

// cleanupFlyApps 清理 Fly.io 应用
// 对每个应用：1. 列出所有机器 2. 删除所有机器 3. 列出所有卷 4. 删除所有卷 5. 删除应用
func cleanupFlyApps(ctx context.Context, flyService *actualFly.Service, appNames []string) ([]string, []string, error) {
	var successApps []string
	var failedApps []string

	for _, appName := range appNames {
		fmt.Printf("\n--- Processing app: %s ---\n", appName)

		// 1. 列出应用的所有机器
		fmt.Printf("Listing machines for app %s...\n", appName)
		machines, err := flyService.Machines.ListMachines(ctx, appName)
		if err != nil {
			logger.Error("Failed to list machines", "AppName", appName, "Error", err)
			fmt.Printf("Failed to list machines for app %s: %v\n", appName, err)
			failedApps = append(failedApps, appName)
			continue
		}

		fmt.Printf("Found %d machines for app %s\n", len(machines), appName)

		// 2. 删除所有机器
		if len(machines) > 0 {
			fmt.Printf("Deleting %d machines for app %s...\n", len(machines), appName)
			for _, machine := range machines {
				if machine.Id == nil {
					fmt.Printf("  Skipping machine with nil ID\n")
					continue
				}
				fmt.Printf("deleting machine %s (%s)...\n", *machine.Id, *machine.Name)
				err := flyService.Machines.DeleteMachine(ctx, appName, *machine.Id, true)
				if err != nil {
					logger.Error("Failed to delete machine", "AppName", appName, "MachineID", *machine.Id, "Error", err)
					fmt.Printf("  Failed to delete machine %s: %v\n", *machine.Id, err)
				} else {
					fmt.Printf("  Successfully deleted machine %s\n", *machine.Id)
				}
			}
		}

		// 3. 列出应用的所有卷
		fmt.Printf("Listing volumes for app %s...\n", appName)
		volumes, err := flyService.Volumes.ListVolumes(ctx, appName, false)
		if err != nil {
			logger.Error("Failed to list volumes", "AppName", appName, "Error", err)
			fmt.Printf("Failed to list volumes for app %s: %v\n", appName, err)
			failedApps = append(failedApps, appName)
			continue
		}

		fmt.Printf("Found %d volumes for app %s\n", len(volumes), appName)

		// 4. 删除所有卷
		if len(volumes) > 0 {
			fmt.Printf("Deleting %d volumes for app %s...\n", len(volumes), appName)
			for _, volume := range volumes {
				fmt.Printf("  Deleting volume %s (%s)...\n", volume.ID, volume.Name)
				err := flyService.Volumes.DeleteVolume(ctx, appName, volume.ID)
				if err != nil {
					logger.Error("Failed to delete volume", "AppName", appName, "VolumeID", volume.ID, "Error", err)
					fmt.Printf("  Failed to delete volume %s: %v\n", volume.ID, err)
				} else {
					fmt.Printf("  Successfully deleted volume %s\n", volume.ID)
				}
			}
		}

		// 5. 删除应用
		fmt.Printf("Deleting app %s...\n", appName)
		err = flyService.Apps.DeleteApp(ctx, appName)
		if err != nil {
			logger.Error("Failed to delete app", "AppName", appName, "Error", err)
			fmt.Printf("Failed to delete app %s: %v\n", appName, err)
			failedApps = append(failedApps, appName)
		} else {
			fmt.Printf("Successfully deleted app %s\n", appName)
			successApps = append(successApps, appName)
		}
	}

	return successApps, failedApps, nil
}

// autoCleanupInactiveApps 自动清理不活跃的应用
func autoCleanupInactiveApps(ctx context.Context, flyService *actualFly.Service, inactiveDays int, dryRun bool) ([]string, []string, error) {
	var successApps []string
	var failedApps []string

	// 1. 获取所有应用
	fmt.Println("Fetching all Fly.io apps...")
	appsResp, err := flyService.Apps.ListApps(ctx)
	if err != nil {
		logger.Error("Failed to list all apps", "Error", err)
		return nil, nil, fmt.Errorf("failed to list apps: %w", err)
	}

	fmt.Printf("Found %d total apps in organization\n", len(appsResp.Apps))

	// 2. 分析每个应用的活跃状态
	inactiveThreshold := time.Now().AddDate(0, 0, -inactiveDays)
	var inactiveApps []string

	for _, app := range appsResp.Apps {
		if app.Name == "" {
			continue
		}

		// 检查应用是否为预览应用（基于命名规则）
		if !isPreviewApp(app.Name) {
			fmt.Printf("Skipping non-preview app: %s\n", app.Name)
			continue
		}

		isInactive, reason, err := isAppInactive(ctx, flyService, app.Name, inactiveThreshold)
		if err != nil {
			logger.Error("Failed to check app activity", "AppName", app.Name, "Error", err)
			fmt.Printf("Failed to check activity for app %s: %v\n", app.Name, err)
			failedApps = append(failedApps, app.Name)
			continue
		}

		if isInactive {
			fmt.Printf("App %s is inactive: %s\n", app.Name, reason)
			inactiveApps = append(inactiveApps, app.Name)
		} else {
			fmt.Printf("App %s is active: %s\n", app.Name, reason)
		}
	}

	fmt.Printf("\nFound %d inactive apps to clean up: %v\n", len(inactiveApps), inactiveApps)

	if len(inactiveApps) == 0 {
		fmt.Println("No inactive apps found. Nothing to clean up.")
		return successApps, failedApps, nil
	}

	if dryRun {
		fmt.Println("DRY RUN: The following apps would be deleted:")
		for _, appName := range inactiveApps {
			fmt.Printf("  - %s\n", appName)
		}
		return inactiveApps, failedApps, nil
	}

	// 3. 清理不活跃的应用
	fmt.Printf("\nStarting cleanup of %d inactive apps...\n", len(inactiveApps))
	cleanupSuccess, cleanupFailed, err := cleanupFlyApps(ctx, flyService, inactiveApps)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to cleanup inactive apps: %w", err)
	}

	successApps = append(successApps, cleanupSuccess...)
	failedApps = append(failedApps, cleanupFailed...)

	return successApps, failedApps, nil
}

// isPreviewApp 判断应用是否为预览应用
// 基于项目的命名规则，预览应用通常是UUID格式的项目ID
func isPreviewApp(appName string) bool {
	// UUID 格式检查：8-4-4-4-12 字符，包含连字符
	uuidPattern := `^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`
	matched, err := regexp.MatchString(uuidPattern, appName)
	if err != nil {
		logger.Error("Failed to match UUID pattern", "AppName", appName, "Error", err)
		return false
	}

	if matched {
		return true
	}

	// 也检查是否以 "project-" 开头的格式（从代码中看到的另一种命名模式）
	projectPattern := `^project-[0-9a-f]{8}-\d+$`
	matched, err = regexp.MatchString(projectPattern, appName)
	if err != nil {
		logger.Error("Failed to match project pattern", "AppName", appName, "Error", err)
		return false
	}

	return matched
}

// isAppInactive 检查应用是否不活跃
// 返回值：(是否不活跃, 原因描述, 错误)
func isAppInactive(ctx context.Context, flyService *actualFly.Service, appName string, inactiveThreshold time.Time) (bool, string, error) {
	// 1. 获取应用的机器列表
	machines, err := flyService.Machines.ListMachines(ctx, appName)
	if err != nil {
		return false, "", fmt.Errorf("failed to list machines for app %s: %w", appName, err)
	}

	// 如果没有机器，认为是不活跃的
	if len(machines) == 0 {
		return true, "no machines found", nil
	}

	// 2. 检查机器状态和时间
	var latestActivity time.Time
	var activeMachines int
	var stoppedMachines int

	for _, machine := range machines {
		if machine.State != nil {
			switch *machine.State {
			case "started", "starting":
				activeMachines++
			case "stopped", "stopping":
				stoppedMachines++
			}
		}

		// 检查机器的创建时间和更新时间
		if machine.CreatedAt != nil {
			if createdAt, err := time.Parse(time.RFC3339, *machine.CreatedAt); err == nil {
				if createdAt.After(latestActivity) {
					latestActivity = createdAt
				}
			}
		}

		if machine.UpdatedAt != nil {
			if updatedAt, err := time.Parse(time.RFC3339, *machine.UpdatedAt); err == nil {
				if updatedAt.After(latestActivity) {
					latestActivity = updatedAt
				}
			}
		}
	}

	// 3. 判断是否不活跃
	// 如果所有机器都是停止状态，且最后活跃时间超过阈值，则认为不活跃
	if activeMachines == 0 && stoppedMachines > 0 {
		if latestActivity.Before(inactiveThreshold) {
			daysSinceActivity := int(time.Since(latestActivity).Hours() / 24)
			return true, fmt.Sprintf("all machines stopped, last activity %d days ago", daysSinceActivity), nil
		} else {
			daysSinceActivity := int(time.Since(latestActivity).Hours() / 24)
			return false, fmt.Sprintf("machines stopped but recent activity (%d days ago)", daysSinceActivity), nil
		}
	}

	// 如果有活跃的机器，跳过
	if activeMachines > 0 {
		return false, fmt.Sprintf("%d active machines", activeMachines), nil
	}

	// 默认情况：如果无法确定状态，保守地认为是活跃的
	return false, fmt.Sprintf("%d machines with unknown state", len(machines)), nil
}

# Fly.io 应用清理脚本

这个脚本用于自动清理不活跃的 Fly.io 预览应用。

## 功能特性

- **自动模式**：扫描所有 Fly.io 应用，自动识别并清理不活跃的预览应用
- **常量配置**：通过代码中的常量配置参数，无需命令行参数
- **智能识别**：自动识别预览应用（UUID 格式的项目 ID）
- **活跃度检测**：基于机器状态和最后活跃时间判断应用是否不活跃
- **完整清理**：先删除机器，再删除卷，最后删除应用

## 使用方法

```bash
# 直接运行脚本
go run main.go
```

## 配置参数

在代码中通过常量配置：

```go
const (
    // INACTIVE_DAYS 不活跃天数阈值
    INACTIVE_DAYS = 7
    // DRY_RUN 是否为预览模式
    DRY_RUN = false
)
```

- `INACTIVE_DAYS`: 不活跃天数阈值，默认 7 天
- `DRY_RUN`: 预览模式开关，`true` 为预览模式，`false` 为实际删除模式

## 不活跃应用判断标准

应用被认为是不活跃的，当满足以下条件之一：

1. **无机器**：应用下没有任何机器
2. **机器全部停止且超过阈值**：所有机器都处于停止状态，且最后活跃时间超过指定天数
3. **有活跃机器的应用会被跳过**：如果有运行中的机器，应用会被认为是活跃的

## 预览应用识别

脚本会自动识别预览应用，跳过非预览应用的清理。预览应用的识别规则：

1. **UUID 格式**：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
2. **项目格式**：`project-xxxxxxxx-timestamp`

## 安全特性

- **保守策略**：当无法确定应用状态时，默认认为应用是活跃的
- **详细日志**：提供详细的操作日志和错误信息
- **Dry-run 模式**：支持预览模式，避免误删除
- **分步清理**：按照正确的顺序删除资源（机器 → 卷 → 应用）

## 示例输出

### 预览模式（DRY_RUN = true）
```
Starting auto cleanup mode (inactive threshold: 7 days, dry-run: true)
Fetching all Fly.io apps...
Found 23 total apps in organization
Skipping non-preview app: fly-agent-base
App 72998440-edf6-4553-ab60-7eb591f855a3 is active: machines stopped but recent activity (4 days ago)
App d5b7ec1e-445c-423a-b94b-f8beac1a7b65 is inactive: all machines stopped, last activity 8 days ago

Found 1 inactive apps to clean up: [d5b7ec1e-445c-423a-b94b-f8beac1a7b65]
DRY RUN: The following apps would be deleted:
  - d5b7ec1e-445c-423a-b94b-f8beac1a7b65

=== Cleanup Results ===
Total apps processed: 1
Successfully would be cleaned: 1
Failed to clean: 0
This was a dry run - no apps were actually deleted.
```

### 实际清理模式（DRY_RUN = false）
```
Starting auto cleanup mode (inactive threshold: 7 days, dry-run: false)
Fetching all Fly.io apps...
Found 23 total apps in organization
Skipping non-preview app: fly-agent-base
App 458da60c-c7e8-4736-b885-d08b4175aa6d is inactive: all machines stopped, last activity 7 days ago

Found 2 inactive apps to clean up: [458da60c-c7e8-4736-b885-d08b4175aa6d 346808b9-7b41-4422-a4b2-61485bf4558e]

Starting cleanup of 2 inactive apps...

--- Processing app: 458da60c-c7e8-4736-b885-d08b4175aa6d ---
Listing machines for app 458da60c-c7e8-4736-b885-d08b4175aa6d...
Found 1 machines for app 458da60c-c7e8-4736-b885-d08b4175aa6d
Deleting 1 machines for app 458da60c-c7e8-4736-b885-d08b4175aa6d...
  Successfully deleted machine 2865995c43d758
Listing volumes for app 458da60c-c7e8-4736-b885-d08b4175aa6d...
Found 1 volumes for app 458da60c-c7e8-4736-b885-d08b4175aa6d
Deleting 1 volumes for app 458da60c-c7e8-4736-b885-d08b4175aa6d...
  Successfully deleted volume vol_r7q8je35kj1yk7gv
Deleting app 458da60c-c7e8-4736-b885-d08b4175aa6d...
Successfully deleted app 458da60c-c7e8-4736-b885-d08b4175aa6d

=== Cleanup Results ===
Total apps processed: 2
Successfully cleaned: 2
Failed to clean: 0
Successfully cleaned apps: [458da60c-c7e8-4736-b885-d08b4175aa6d 346808b9-7b41-4422-a4b2-61485bf4558e]
Cleanup completed.
```

## 定时任务设置

可以将此脚本设置为定时任务，定期清理不活跃的应用：

```bash
# 添加到 crontab，每天凌晨 2 点执行
0 2 * * * cd /path/to/scripts/cleanup_fly_apps && go run main.go
```

## 注意事项

1. **配置要求**：确保 Fly.io API Token 和 Org Slug 已正确配置
2. **权限要求**：需要有删除应用、机器和卷的权限
3. **备份建议**：重要应用建议先备份再删除
4. **测试建议**：首次使用建议先设置 `DRY_RUN = true` 进行测试
5. **参数调整**：根据需要调整 `INACTIVE_DAYS` 和 `DRY_RUN` 常量

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// 需要排除的站点ID列表
var excludedSiteIDs = map[string]bool{
	"a6a2d5e6-eaac-4d25-befa-5309c8417f43": true,
	"98a0378d-0769-4292-8dca-9048c0b95ada": true,
	"ad459a07-72f4-4fa8-be28-88380334a87f": true,
	"e46b7368-d53d-43f1-a562-6477dc0b78e5": true,
	"0e4775cb-0ce8-4791-8880-4cfa588666f0": true,
	"65aabf1f-453a-4b46-bdbf-5c4e11e5a319": true,
	"c31f541b-5f04-4fa0-b9c4-5d16329afe83": true,
	"011d48d6-1dd5-428c-a511-7ed95556aaf2": true,
}

func main() {
	// 初始化日志
	logger.InitLogger("info", "cleanup-sites")

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		logger.Error("Failed to load config", "Error", err)
		os.Exit(1)
	}

	// 创建 Netlify 服务
	nfService, err := nactual.NewService(cfg.Netlify.Token)
	if err != nil {
		logger.Error("Failed to create Netlify service", "Error", err)
		os.Exit(1)
	}

	// 创建 Supabase 客户端和项目仓库
	supabaseClient, err := supabase.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	if err != nil {
		logger.Error("Failed to create Supabase client", "Error", err)
		os.Exit(1)
	}
	projectRepo := supabase.NewSupabaseProjectRepository(supabaseClient)
	projectStatusRepo := supabase.NewSupabaseProjectStatusRepository(supabaseClient)

	// 创建上下文
	ctx := context.Background()

	// 获取站点列表，按更新时间排序
	opts := &nactual.ListSiteOptions{
		Filter:           "all",
		SortBy:           "updated_at",
		OrderBy:          "desc",
		Page:             1,
		PerPage:          30,
		IncludeFavorites: true,
	}

	// 处理所有页面的站点
	for {
		sites, err := nfService.Sites.ListSite(ctx, opts)
		if err != nil {
			logger.Error("Failed to list sites", "Error", err)
			os.Exit(1)
		}

		if len(sites) == 0 {
			fmt.Println("No more sites found.")
			break
		}

		fmt.Printf("Processing page %d, found %d sites:\n\n", opts.Page, len(sites))

		// 遍历当前页的站点
		for i, site := range sites {
			// 检查是否在排除列表中
			if excludedSiteIDs[site.ID] {
				fmt.Printf("Site %d/%d (ID: %s) is in the exclusion list, skipping...\n\n", i+1, len(sites), site.ID)
				continue
			}

			// 解析更新时间
			updatedAt, err := time.Parse(time.RFC3339, site.UpdatedAt)
			if err != nil {
				logger.Error("Failed to parse updated_at time", "Error", err, "SiteID", site.ID)
				continue
			}

			// 显示站点信息
			jsonBytes, _ := json.Marshal(site)
			fmt.Printf("Site: %s\n", string(jsonBytes))

			// 检查是否是24小时前的站点
			twentyFourHoursAgo := time.Now().Add(-24 * time.Hour)
			if updatedAt.Before(twentyFourHoursAgo) {
				// 获取项目信息
				project, err := projectRepo.GetByNetlifySiteID(ctx, site.ID)
				if err != nil {
					// 如果是项目未找到的错误，只删除站点
					if err.Error() == fmt.Sprintf("project with NetlifySiteID %s not found", site.ID) {
						fmt.Printf("Project not found for site %s, only deleting site...\n", site.ID)
						if err := deleteSiteOnly(ctx, nfService, site.ID); err != nil {
							logger.Error("Failed to delete site", "Error", err, "SiteID", site.ID)
							fmt.Printf("Failed to delete site: %v\n\n", err)
						}
						continue
					}
					logger.Error("Failed to get project", "Error", err, "SiteID", site.ID)
					continue
				}

				// 如果项目已发布，则跳过
				if project.IsPublished {
					fmt.Printf("Site was updated %s ago but project is published, skipping...\n", time.Since(updatedAt).Round(time.Second))
					continue
				}

				// 未发布的项目，执行删除操作
				fmt.Printf("Site was updated %s ago and project is not published, automatically deleting...\n", time.Since(updatedAt).Round(time.Second))
				if err := deleteSiteAndUpdateProject(ctx, nfService, projectRepo, projectStatusRepo, site.ID, project); err != nil {
					logger.Error("Failed to process site", "Error", err, "SiteID", site.ID)
					fmt.Printf("Failed to process site: %v\n\n", err)
				}
			} else {
				fmt.Printf("Site was updated recently (%s ago), skipping...\n", time.Since(updatedAt).Round(time.Second))
			}
		}

		// 检查是否还有下一页
		if len(sites) < opts.PerPage {
			break
		}
		opts.Page++
	}

	fmt.Println("Cleanup completed.")
}

// deleteSiteAndUpdateProject 删除站点并更新关联的项目
func deleteSiteAndUpdateProject(ctx context.Context, nfService *nactual.Service, projectRepo repository.ProjectRepository, projectStatusRepo repository.ProjectStatusRepository, siteID string, project *entity.Project) error {
	// 删除站点
	fmt.Printf("Deleting site %s...\n", siteID)
	err := nfService.Sites.DeleteSite(ctx, siteID)
	if err != nil {
		return fmt.Errorf("failed to delete site: %w", err)
	}
	fmt.Printf("Successfully deleted site %s\n", siteID)

	// 更新项目的 NetlifySiteID
	if err := updateProjectNetlifySiteID(ctx, projectRepo, project.ID); err != nil {
		return fmt.Errorf("failed to update project: %w", err)
	}
	fmt.Printf("Successfully cleared project NetlifySiteID\n")

	// 更新项目状态
	if err := updateProjectStatus(ctx, projectStatusRepo, project.ID); err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}
	fmt.Printf("Successfully updated project status\n")
	fmt.Println()

	return nil
}

// updateProjectNetlifySiteID 更新项目的 NetlifySiteID 为空字符串
func updateProjectNetlifySiteID(ctx context.Context, projectRepo repository.ProjectRepository, projectID string) error {
	// 更新 NetlifySiteID 为空字符串
	updates := map[string]any{
		"netlify_site_id": "",
	}
	_, err := projectRepo.UpdateFields(ctx, projectID, updates)
	if err != nil {
		return fmt.Errorf("failed to update project fields: %w", err)
	}

	return nil
}

// updateProjectStatus 更新项目状态
func updateProjectStatus(ctx context.Context, projectStatusRepo repository.ProjectStatusRepository, projectID string) error {
	// 更新项目状态
	updates := map[string]any{
		"preview_link": "",
		"publish_link": "",
	}
	_, err := projectStatusRepo.UpdateByProjectID(ctx, projectID, updates)
	if err != nil {
		return fmt.Errorf("failed to update project status: %w", err)
	}

	return nil
}

// deleteSiteOnly 只删除站点，不更新项目信息
func deleteSiteOnly(ctx context.Context, nfService *nactual.Service, siteID string) error {
	// 删除站点
	fmt.Printf("Deleting site %s...\n", siteID)
	err := nfService.Sites.DeleteSite(ctx, siteID)
	if err != nil {
		return fmt.Errorf("failed to delete site: %w", err)
	}
	fmt.Printf("Successfully deleted site %s\n", siteID)
	fmt.Println()
	return nil
}

package main

import (
	"context"
	"fmt"
	"os"

	"github.com/web-builder-dev/be-web-builder/internal/config"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	nactual "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// 常量定义
const (
	// 要清理的用户ID
	TargetUserID = "1d3c6899-d21a-4943-9903-e5a915494c85"

	// 清理模式
	ModeDeleteAll     = "delete_all"     // 删除所有资源（GitHub仓库、Netlify站点和Project记录）
	ModeDeleteNetlify = "delete_netlify" // 只删除Netlify站点并更新Project记录

	// 当前选择的清理模式
	CurrentMode = ModeDeleteAll
)

func main() {
	// 初始化日志
	logger.InitLogger("info", "cleanup-user-projects")

	// 加载配置
	cfg, err := config.LoadConfig("dev")
	if err != nil {
		logger.Error("Failed to load config", "Error", err)
		os.Exit(1)
	}

	// 创建 Netlify 服务
	nfService, err := nactual.NewService(cfg.Netlify.Token)
	if err != nil {
		logger.Error("Failed to create Netlify service", "Error", err)
		os.Exit(1)
	}

	// 创建 GitHub 服务
	ghService := github.NewService(cfg.GitHub.Token)

	// 创建 Supabase 客户端和项目仓库
	supabaseClient, err := supabase.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	if err != nil {
		logger.Error("Failed to create Supabase client", "Error", err)
		os.Exit(1)
	}
	projectRepo := supabase.NewSupabaseProjectRepository(supabaseClient)

	// 创建上下文
	ctx := context.Background()

	// 获取用户的所有项目
	projects, err := projectRepo.GetByUserID(ctx, TargetUserID)
	if err != nil {
		logger.Error("Failed to get user projects", "Error", err, "UserID", TargetUserID)
		os.Exit(1)
	}

	if len(projects) == 0 {
		fmt.Printf("No projects found for user %s\n", TargetUserID)
		return
	}

	fmt.Printf("Found %d projects for user %s\n\n", len(projects), TargetUserID)

	// 处理每个项目
	for _, project := range projects {
		fmt.Printf("Processing project: %s\n", project.ID)
		fmt.Printf("  Title: %s\n", project.Title)
		fmt.Printf("  NetlifySiteID: %s\n", project.NetlifySiteID)
		fmt.Printf("  RepoID: %s\n", project.RepoID)

		switch CurrentMode {
		case ModeDeleteAll:
			// 删除 GitHub 仓库
			if project.RepoID != "" {
				fmt.Printf("  Deleting GitHub repository: %s\n", project.RepoID)
				_, err := ghService.Repositories.DeleteRepository(ctx, cfg.GitHub.Owner, project.RepoID)
				if err != nil {
					logger.Error("Failed to delete GitHub repository", "Error", err, "Repo", project.RepoID)
					continue
				}
				fmt.Printf("  Successfully deleted GitHub repository\n")
			}

			// 删除 Netlify 站点
			if project.NetlifySiteID != "" {
				fmt.Printf("  Deleting Netlify site: %s\n", project.NetlifySiteID)
				err := nfService.Sites.DeleteSite(ctx, project.NetlifySiteID)
				if err != nil {
					logger.Error("Failed to delete Netlify site", "Error", err, "SiteID", project.NetlifySiteID)
					continue
				}
				fmt.Printf("  Successfully deleted Netlify site\n")
			}

			// 删除项目记录
			fmt.Printf("  Deleting project record\n")
			err := projectRepo.Delete(ctx, project.ID)
			if err != nil {
				logger.Error("Failed to delete project record", "Error", err, "ProjectID", project.ID)
				continue
			}
			fmt.Printf("  Successfully deleted project record\n")

		case ModeDeleteNetlify:
			// 只删除 Netlify 站点并更新项目记录
			if project.NetlifySiteID != "" {
				fmt.Printf("  Deleting Netlify site: %s\n", project.NetlifySiteID)
				err := nfService.Sites.DeleteSite(ctx, project.NetlifySiteID)
				if err != nil {
					logger.Error("Failed to delete Netlify site", "Error", err, "SiteID", project.NetlifySiteID)
					continue
				}
				fmt.Printf("  Successfully deleted Netlify site\n")

				// 更新项目的 NetlifySiteID
				updates := map[string]any{
					"netlify_site_id": "",
				}
				_, err = projectRepo.UpdateFields(ctx, project.ID, updates)
				if err != nil {
					logger.Error("Failed to update project", "Error", err, "ProjectID", project.ID)
					continue
				}
				fmt.Printf("  Successfully updated project record\n")
			}
		}

		fmt.Println()
	}

	fmt.Println("Cleanup completed.")
}

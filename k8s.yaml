# be-web-builder Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: be-web-builder
  labels:
    app: be-web-builder
spec:
  replicas: 1
  selector:
    matchLabels:
      app: be-web-builder
  template:
    metadata:
      labels:
        app: be-web-builder
    spec:
      containers:
      - name: be-web-builder
        image: ghcr.io/web-builder-dev/be-web-builder:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          value: "dev"
        - name: SERVER_ADDRESS
          value: ":8080"
---
# be-web-builder Service
apiVersion: v1
kind: Service
metadata:
  name: be-web-builder-service
  labels:
    app: be-web-builder
spec:
  selector:
    app: be-web-builder
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
services:
  be-agent:
    image: ghcr.io/web-builder-dev/be-agent:latest
    container_name: be-agent
    pull_policy: always
    restart: always
    depends_on:
      - be-web-builder
    ports:
      - "9243:9243"
    environment:
      - APP_ENV=dev
      - DEPLOY_URL=http://be-web-builder:8080
    env_file:
      - .env.be-agent
    volumes:
      - /tmp/be-agent:/tmp/be-agent-logs
    networks:
      - web-builder-network

  fe-web-builder:
    image: ghcr.io/web-builder-dev/fe-web-builder:latest
    container_name: fe-web-builder
    pull_policy: always
    restart: always
    depends_on:
      - be-web-builder
      - be-agent
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - BE_WEB_BUILDER_API_URL=http://be-web-builder:8080
      - BE_AGENT_API_URL=http://be-agent:9243
    volumes:
      - /tmp/fe-web-builder:/app/logs
    networks:
      - web-builder-network

  be-web-builder:
    image: ghcr.io/web-builder-dev/be-web-builder:latest
    container_name: be-web-builder
    pull_policy: always
    restart: always
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=dev
      - SERVER_ADDRESS=:8080
    volumes:
      - /tmp/be-web-builder:/app/logs
    networks:
      - web-builder-network

networks:
  web-builder-network:
    name: web-builder-network
    driver: bridge

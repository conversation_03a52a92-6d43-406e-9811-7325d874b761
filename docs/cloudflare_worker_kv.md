# Cloudflare Worker KV 服务使用指南

本文档介绍如何使用配置化的 Cloudflare Worker KV 服务。

## 概述

Worker KV 服务现在已经完全集成到项目的配置系统中，支持：

1. **配置文件管理** - KV namespace ID 通过配置文件管理
2. **环境变量覆盖** - 支持通过环境变量覆盖配置
3. **应用服务层** - 提供高级的应用服务接口
4. **基础设施层** - 提供底层的 API 调用接口

## 配置设置

### 1. 配置文件设置

在 `conf/config_dev.yaml` 或 `conf/config_prod.yaml` 中添加：

```yaml
cloudflare:
  api_key: "your-api-key"
  api_email: "<EMAIL>"
  account_id: "your-account-id"
  kv_namespace_id: "your-kv-namespace-id"  # 新增的配置项
```

### 2. 环境变量设置

你也可以通过环境变量设置：

```bash
export CLOUDFLARE_API_KEY="your-api-key"
export CLOUDFLARE_API_EMAIL="<EMAIL>"
export CLOUDFLARE_ACCOUNT_ID="your-account-id"
export CLOUDFLARE_KV_NAMESPACE_ID="your-kv-namespace-id"  # 新增的环境变量
```

## 使用方式

### 方式1: 应用服务层（推荐）

应用服务层自动使用配置中的 namespace ID，提供更简洁的 API：

```go
package main

import (
    "context"
    "log"

    "github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
    "github.com/web-builder-dev/be-web-builder/internal/config"
    "github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
)

func main() {
    // 加载配置
    cfg, err := config.LoadConfig("dev")
    if err != nil {
        log.Fatal(err)
    }

    // 创建基础设施服务
    cloudflareInfra, err := cloudflare.NewService(
        cfg.Cloudflare.APIKey,
        cfg.Cloudflare.APIEmail,
        cfg.Cloudflare.AccountID,
    )
    if err != nil {
        log.Fatal(err)
    }

    // 创建应用服务（自动使用配置中的 namespace ID）
    kvService := cloudflare_service.NewWorkerKVService(cloudflareInfra, cfg)

    ctx := context.Background()

    // 写入键值对（不需要指定 namespace ID）
    err = kvService.WriteKeyValuePair(ctx, "my-key", "my-value")
    if err != nil {
        log.Fatal(err)
    }

    // 读取键值对
    value, err := kvService.ReadKeyValuePair(ctx, "my-key")
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("Value: %s", value)
}
```

### 方式3: URL 配置管理（推荐用于 URL 存储）

对于存储 URL 配置，推荐使用专门的 `CreateOrUpdateURLConfig` 方法：

```go
// 创建新的 URL 配置
previewURL := "https://preview.example.com"
publishURL := "https://publish.example.com"

err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", &previewURL, &publishURL)
if err != nil {
    log.Fatal(err)
}

// 只更新预览 URL（发布 URL 保持不变）
newPreviewURL := "https://new-preview.example.com"
err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", &newPreviewURL, nil)
if err != nil {
    log.Fatal(err)
}

// 只更新发布 URL（预览 URL 保持不变）
newPublishURL := "https://new-publish.example.com"
err = kvService.CreateOrUpdateURLConfig(ctx, "website-config", nil, &newPublishURL)
if err != nil {
    log.Fatal(err)
}

// 读取配置（返回 JSON 格式）
config, err := kvService.ReadKeyValuePair(ctx, "website-config")
if err != nil {
    log.Fatal(err)
}
// config 内容类似: {"previewUrl":"https://new-preview.example.com","publishUrl":"https://new-publish.example.com"}
```

### 方式2: 基础设施层

如果你需要更多控制，可以直接使用基础设施层：

```go
// 仍然需要手动传入 namespace ID
err = cloudflareInfra.WorkerKV.WriteKeyValuePairWithOptionalMetadata(
    ctx,
    cfg.Cloudflare.KVNamespaceID,  // 从配置中获取
    "my-key",
    "my-value",
    nil,
)
```

## API 方法

### 应用服务层方法

#### ReadKeyValuePair
```go
func (s *WorkerKVService) ReadKeyValuePair(ctx context.Context, keyName string) (string, error)
```

#### WriteKeyValuePair
```go
func (s *WorkerKVService) WriteKeyValuePair(ctx context.Context, keyName, value string) error
```

#### WriteKeyValuePairWithOptionalMetadata
```go
func (s *WorkerKVService) WriteKeyValuePairWithOptionalMetadata(
    ctx context.Context,
    keyName, value string,
    metadata map[string]interface{},
) error
```

#### CreateOrUpdateURLConfig
```go
func (s *WorkerKVService) CreateOrUpdateURLConfig(
    ctx context.Context,
    keyName string,
    previewUrl, publishUrl *string,
) error
```

这是一个专门用于管理 URL 配置的高级方法，支持：
- 自动创建或更新 JSON 格式的 URL 配置
- 部分字段更新（传入 nil 表示不更新该字段）
- 智能合并现有配置

## 示例代码

完整的示例代码请参考 `examples/cloudflare_worker_kv_example.go`。

## 测试

### 运行测试

```bash
# 运行快速测试（跳过集成测试）
go test -short ./test/infrastructure/cloudflare/... -v

# 运行完整测试（需要配置真实的凭据）
go test ./test/infrastructure/cloudflare/... -v
```

### 配置测试环境

1. 在 `conf/config_dev.yaml` 中设置真实的 KV namespace ID
2. 或者设置环境变量 `CLOUDFLARE_KV_NAMESPACE_ID`

## 创建 KV Namespace

如果你还没有 KV namespace，可以通过以下方式创建：

### 1. 通过 Cloudflare Dashboard
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 选择你的账户
3. 进入 "Workers & Pages" > "KV"
4. 点击 "Create a namespace"
5. 输入 namespace 名称并创建
6. 复制生成的 namespace ID 到配置文件中

### 2. 通过 API
```bash
curl -X POST "https://api.cloudflare.com/client/v4/accounts/$ACCOUNT_ID/storage/kv/namespaces" \
     -H "X-Auth-Email: $CLOUDFLARE_EMAIL" \
     -H "X-Auth-Key: $CLOUDFLARE_API_KEY" \
     -H "Content-Type: application/json" \
     --data '{"title":"my-namespace"}'
```

## 注意事项

1. **配置验证**: 应用服务会自动检查 namespace ID 是否配置，未配置时会返回错误
2. **日志记录**: 所有操作都会记录详细的日志，便于调试
3. **错误处理**: 提供了详细的错误信息和上下文
4. **性能**: 应用服务层添加了适当的日志记录，但不会显著影响性能
5. **响应解析**: 读取操作会自动处理 Cloudflare KV API 的不同响应格式：
   - 带元数据的键值对返回 JSON 格式，会自动提取 `value` 字段
   - 不带元数据的键值对可能返回纯文本或 JSON 格式，都能正确处理

## 故障排除

### 常见错误

1. **"KV namespace ID is not configured"**
   - 检查配置文件中是否设置了 `kv_namespace_id`
   - 检查环境变量 `CLOUDFLARE_KV_NAMESPACE_ID` 是否设置

2. **"failed to read/write key-value pair"**
   - 检查 API 凭据是否正确
   - 检查 namespace ID 是否存在
   - 检查网络连接

3. **测试跳过**
   - 测试会在没有配置 namespace ID 时自动跳过
   - 这是正常行为，不是错误

{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "This is the API for the Web Builder backend service.", "title": "Web Builder API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "https://www.web-builder-dev.com/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api", "paths": {"/api/netlify/hooks": {"post": {"description": "接收并处理 Netlify 发送的部署状态更新通知", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["netlify"], "summary": "处理 Netlify 部署 Webhook", "parameters": [{"description": "Netlify Webhook 请求体", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDetailedDeploy"}}], "responses": {"200": {"description": "Webhook 处理成功", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "400": {"description": "请求参数错误或无效的 payload", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "500": {"description": "服务器内部处理错误", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}}}}, "/api/workflow/commit-and-deploy": {"post": {"description": "<p>🚀 <strong>异步后台执行：一步完成代码提交和Netlify部署</strong></p>\n<p>此接口接收请求后会立即返回响应，表示任务已接受并在后台开始处理。实际的 GitHub 提交和 Netlify 部署将在后台异步进行。</p>\n<p><strong>后台流程:</strong></p>\n<ol>\n<li><strong>提交代码:</strong> 将文件提交到 GitHub 仓库</li>\n<li><strong>部署代码:</strong> 触发 Netlify 部署流程</li>\n<li><strong>更新数据库:</strong> 更新项目状态和部署信息</li>\n</ol>\n<p><strong>重要提示:</strong></p>\n<ul>\n<li>由于是后台执行，此接口的直接响应<strong>不会</strong>包含最终的部署结果 (如 deploy_id, status, site_url)</li>\n<li>客户端需要通过其他方式（例如轮询状态接口，或等待 webhook/通知）来获取最终部署状态</li>\n<li>后台任务执行过程中的错误将记录在服务器日志中，不会直接返回给调用此接口的客户端</li>\n</ul>\n<p><strong>参数说明:</strong></p>\n<ul>\n<li><code>project_id</code>: (必填) Supabase 中项目记录的 UUID</li>\n<li><code>files</code>: (可选) 包含要创建或更新的文件路径和内容的列表</li>\n<li><code>deleted_files</code>: (可选) 包含要从仓库中删除的文件路径的列表</li>\n<li><code>commit_message</code>: (可选) 本次提交的 Git commit 信息</li>\n<li><code>repo_id</code>: (可选) 目标 GitHub 仓库的名称</li>\n<li><code>netlify_site_id</code>: (可选) 目标 Netlify 站点的 ID</li>\n<li><code>build_cmd</code>: (可选) Netlify 使用的构建命令</li>\n</ul>\n<p><strong>返回值 (立即响应):</strong></p>\n<pre><code>{\n\"code\": 0,\n\"msg\": \"提交并部署流程已启动\",\n\"data\": null\n}</code></pre>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["workflow"], "summary": "提交并部署", "parameters": [{"description": "提交并部署请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.CommitAndDeployRequest"}}], "responses": {"200": {"description": "提交并部署流程已启动", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "500": {"description": "启动提交并部署流程失败", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}}}}, "/api/workflow/copy-project": {"post": {"description": "<p>触发项目复制流程。</p>\n<p>接收源项目 ID 和目标项目 ID，调用后台服务执行复制操作。返回新创建的 repo_id 和 netlify_site_id。</p>\n<p><strong>复制流程:</strong></p>\n<ol>\n<li>复制 GitHub 仓库</li>\n<li>创建新的 Netlify 站点</li>\n<li>配置部署设置</li>\n<li>更新项目信息</li>\n</ol>\n<p><strong>参数说明:</strong></p>\n<ul>\n<li><code>source_project_id</code>: (必填) 源项目的 UUID</li>\n<li><code>target_project_id</code>: (必填) 目标项目的 UUID</li>\n</ul>\n<p><strong>返回值:</strong></p>\n<pre><code>{\n\"code\": 0,\n\"msg\": \"复制项目成功\",\n\"data\": {\n\"new_repo_name\": \"string\",\n\"new_netlify_site_id\": \"string\"\n}\n}</code></pre>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["workflow"], "summary": "复制项目", "parameters": [{"description": "复制项目请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_interface_dto.CopyProjectRequest"}}], "responses": {"200": {"description": "复制项目成功", "schema": {"allOf": [{"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_interface_dto.CopyProjectResult"}}}]}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "500": {"description": "复制项目失败", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}}}}, "/api/workflow/publish": {"post": {"description": "<p>执行发布流程：解锁 -> 触发构建 -> 锁定 -> 等待结果。</p>\n<p><strong>发布流程:</strong></p>\n<ol>\n<li>查找当前锁定的生产部署并解锁</li>\n<li>触发新的构建</li>\n<li>锁定新触发的部署</li>\n<li>轮询新部署的状态，直到成功或失败（或超时）</li>\n</ol>\n<p><strong>重要提示:</strong></p>\n<ul>\n<li>这是一个同步接口，会等待部署完成或失败后才返回结果</li>\n<li>部署过程可能需要较长时间，请确保客户端有足够的超时设置</li>\n<li>如果部署失败，将返回详细的错误信息</li>\n</ul>\n<p><strong>参数说明:</strong></p>\n<ul>\n<li><code>netlify_site_id</code>: (必填) Netlify 站点的 ID</li>\n</ul>\n<p><strong>返回值:</strong></p>\n<pre><code>{\n\"code\": 0,\n\"msg\": \"发布流程已启动\",\n\"data\": null\n}</code></pre>", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["workflow"], "summary": "发布站点", "parameters": [{"description": "发布站点请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_interface_dto.PublishSiteRequest"}}], "responses": {"200": {"description": "发布流程已启动", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "400": {"description": "请求参数错误或缺少 netlify_site_id", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}, "500": {"description": "发布站点失败", "schema": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse"}}}}}}, "definitions": {"github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.CommitAndDeployRequest": {"type": "object", "properties": {"commit_message": {"type": "string"}, "deleted_files": {"type": "array", "items": {"type": "string"}}, "files": {"type": "array", "items": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.WorkflowFileContent"}}, "netlify_site_id": {"type": "string"}, "project_id": {"type": "string"}, "repo_id": {"type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDeployLinks": {"type": "object", "properties": {"alias": {"type": "string"}, "branch": {"type": "string"}, "permalink": {"type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDeployState": {"type": "string", "enum": ["new", "pending_review", "accepted", "rejected", "enqueued", "building", "uploading", "uploaded", "preparing", "prepared", "processing", "processed", "ready", "error", "retrying"], "x-enum-varnames": ["NetlifyDeployStateNew", "NetlifyDeployStatePendingReview", "NetlifyDeployStateAccepted", "NetlifyDeployStateRejected", "NetlifyDeployStateEnqueued", "NetlifyDeployStateBuilding", "NetlifyDeployStateUploading", "NetlifyDeployStateUploaded", "NetlifyDeployStatePreparing", "NetlifyDeployStatePrepared", "NetlifyDeployStateProcessing", "NetlifyDeployStateProcessed", "NetlifyDeployStateReady", "NetlifyDeployStateError", "NetlifyDeployStateRetrying"]}, "github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDetailedDeploy": {"type": "object", "properties": {"admin_url": {"type": "string"}, "available_functions": {"type": "array", "items": {}}, "blobs_region": {"type": "string"}, "branch": {"type": "string"}, "build_id": {"type": "string"}, "commit_message": {"type": "string"}, "commit_ref": {"type": "string"}, "commit_url": {"type": "string"}, "committer": {"type": "string"}, "context": {"type": "string"}, "created_at": {"type": "string"}, "deploy_ssl_url": {"type": "string"}, "deploy_time": {"type": "integer"}, "deploy_url": {"type": "string"}, "deploy_validations_report": {"type": "object", "additionalProperties": {}}, "edge_functions_present": {"type": "boolean"}, "entry_path": {"type": "string"}, "error_message": {"type": "string"}, "expires_at": {"type": "string"}, "framework": {"type": "string"}, "function_schedules": {"type": "array", "items": {}}, "id": {"type": "string"}, "lighthouse": {"type": "object", "additionalProperties": {}}, "lighthouse_plugin_scores": {"type": "object", "additionalProperties": {}}, "links": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDeployLinks"}, "locked": {"type": "boolean"}, "manual_deploy": {"type": "boolean"}, "name": {"type": "string"}, "pending_review_reason": {"type": "string"}, "plugin_state": {"type": "string"}, "public_repo": {"type": "boolean"}, "published_at": {"type": "string"}, "required": {"type": "array", "items": {}}, "requiredFunctions": {"type": "array", "items": {}}, "review_id": {}, "review_url": {"type": "string"}, "screenshot_url": {"type": "string"}, "site_id": {"type": "string"}, "skipped": {"type": "boolean"}, "skipped_log": {"type": "string"}, "ssl_url": {"type": "string"}, "state": {"$ref": "#/definitions/github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.NetlifyDeployState"}, "title": {"type": "string"}, "updated_at": {"type": "string"}, "url": {"type": "string"}, "user_id": {"type": "string"}, "views_count": {"type": "integer"}}}, "github_com_web-builder-dev_be-web-builder_internal_application_workflow_service.WorkflowFileContent": {"type": "object", "properties": {"content": {"type": "string"}, "path": {"type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_interface_dto.CopyProjectRequest": {"type": "object", "required": ["source_project_id", "target_project_id"], "properties": {"source_project_id": {"type": "string"}, "target_project_id": {"type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_interface_dto.CopyProjectResult": {"type": "object", "properties": {"new_repo_id": {"description": "沿用Python示例中的字段名 new_repo_id", "type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_interface_dto.PublishSiteRequest": {"type": "object", "required": ["netlify_site_id"], "properties": {"netlify_site_id": {"type": "string"}}}, "github_com_web-builder-dev_be-web-builder_internal_pkg_response.APIResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"description": "使用 any (Go 1.18+), 移除 omitempty"}, "msg": {"type": "string"}}}}}